<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.kf-oss</groupId>
        <artifactId>performance</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>performance-domain</artifactId>
    <name>performance-domain</name>
    <description>domain 模块，用于编写核心业务逻辑</description>

    <dependencies>
        <dependency>
            <groupId>com.jd.kf-oss</groupId>
            <artifactId>performance-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>actuator-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>cache-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>context-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>dal-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>ducc-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>http-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jim-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jmq-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jsf-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>log-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>registry-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>schedule-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>thread-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>


    </dependencies>
</project>
