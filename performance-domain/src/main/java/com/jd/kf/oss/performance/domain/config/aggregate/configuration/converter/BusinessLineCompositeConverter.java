package com.jd.kf.oss.performance.domain.config.aggregate.configuration.converter;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLineComposite;
import com.jd.kf.oss.performance.utils.CommonPage;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/25
 */
@Mapper
public interface BusinessLineCompositeConverter {
    BusinessLineCompositeConverter INSTANCE = Mappers.getMapper(BusinessLineCompositeConverter.class);

    // DO分页转Composite分页
    default CommonPage<BusinessLineComposite> pageDO2CompositePage(CommonPage<BusinessLineDO> page) {
        if (page == null || page.getData() == null) {
            return CommonPage.emptyPage(0L, 0L);
        }
        List<BusinessLineComposite> compositeList = new ArrayList<>();
        for (BusinessLineDO businessLineDO : page.getData()) {
            BusinessLineComposite composite = new BusinessLineComposite();
            composite.setBusinessLineDO(businessLineDO);
            compositeList.add(composite);
        }
        return CommonPage.getCommonPage(page.getPage(), page.getSize(), page.getTotal(), compositeList);
    }
}
