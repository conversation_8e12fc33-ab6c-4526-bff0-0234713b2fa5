package com.jd.kf.oss.performance.domain.config.domain.coefficient;

import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check.AbsCoefficientItemsCheck;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 绩效系数
 */
@Service
public class CoefficientDomainService {

    @Resource
    private ICoefficientRepository coefficientRepository;

    /**
     * 根据code和绩效月查询聚合DO
     * @param coefficientCode
     * @param period
     * @return
     */
    public CoefficientDO queryCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode,period,"系数编码和周期不能为空");
        return coefficientRepository.selectCoefficientDOByCodeAndPeriod(coefficientCode, period);
    }

    /**
     * 更新聚合对象DO
     * @param coefficientDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO,"系数配置不能为空");
        CheckUtil.notBlank(coefficientDO.getCode(),coefficientDO.getPeriod(),"系数编码和周期不能为空");
        CoefficientDO coefficientDOInDB = coefficientRepository.selectCoefficientDOByCodeAndPeriod(coefficientDO.getCode(),
                coefficientDO.getPeriod());
        trimAndCheckCoefficientName(coefficientDO,coefficientDOInDB.getName());
        CheckUtil.notNull(coefficientDOInDB, "系数配置不存在，无法更新");
        checkCoefficientItems(coefficientDO,coefficientDOInDB.getType());
        coefficientDO.propagateCoefficientItem();
        return coefficientRepository.updateCoefficientDO(coefficientDO);
    }

    /**
     * 检查系数名称是否合法且未被占用
     * @param coefficientDO 包含待检查名称的系数数据对象
     * @param nameInDB 数据库中已存在的名称
     */
    private void trimAndCheckCoefficientName(CoefficientDO coefficientDO, String nameInDB) {
        // 去除name的两边空白
        coefficientDO.setName(coefficientDO.getName().trim());
        if(Objects.equals(coefficientDO.getName(), nameInDB)){
            return;
        }
        checkCoefficientNameNotExits(coefficientDO.getName(), coefficientDO.getPeriod());
    }


    /**
     * 根据系数模板类型检查系数项的有效性
     * @param coefficientDO 系数数据对象，包含需要检查的系数项
     * @param type 系数模板类型枚举，用于确定检查策略
     */
    private void checkCoefficientItems(CoefficientDO coefficientDO, CoefficientTemplateTypeEnum type){
        AbsCoefficientItemsCheck checkStrategy = AbsCoefficientItemsCheck.getCheckStrategy(type);
        checkStrategy.check(coefficientDO);
    }


    /**
     * 需要在聚合服务中判断是否被其他因子或者方案引用
     * 删除聚合DO系数配置,根据系数编码和绩效月
     * @param coefficientCode
     * @param period
     * @return
     */
    public boolean deleteCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode,period,"系数编码和周期不能为空");
        return coefficientRepository.deleteCoefficientDOByCodeAndPeriod(coefficientCode, period);
    }

    /**
     * 创建聚合系数DO
     * @param coefficientDO
     * @return
     */
    public boolean saveCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO, "系数配置不能为空");
        trimAndCheckCoefficientName(coefficientDO);
        coefficientDO.buildCode();
        coefficientDO.propagateCoefficientItem();
        coefficientDO.checkItemsByTemplateType();
        return coefficientRepository.insertCoefficientDO(coefficientDO);
    }
    /**
     * 对系数名称进行去除两边空白并检查是否已存在
     */
    private void trimAndCheckCoefficientName(CoefficientDO coefficientDO){
        // 去除name的两边空白 系数名不能为空
        String coefficientName =coefficientDO.getName().trim();
        coefficientDO.setName(coefficientName);
        checkCoefficientNameNotExits(coefficientName, coefficientDO.getPeriod());
    }
    /**
     * 校验系数名称在指定租户和周期下是否已存在，若存在则抛出异常
     * @param name 要校验的系数名称
     * @param period 要校验的周期
     */
    private void checkCoefficientNameNotExits(String name, String period) {
        // 校验系数项
        String tenantCode = UserContextHolder.getTenantCode();
        CoefficientDO coefficientDO = coefficientRepository.selectCoefficientByName(name, tenantCode, period);
        CheckUtil.isTrue(coefficientDO==null, "系数名称已存在，请修改后重试");
    }

    /**
     * 分页DO
     * 分页DO（已废弃，使用字段参数版本）
     * @param pageCommand
     * @return
     */
    /*
    public CommonPage<CoefficientDO> pageDO(PageCommand<CoefficientDO> pageCommand) {
        return coefficientRepository.pageDO(pageCommand);
    }
    */

    /**
     * 根据系数名称和类型分页查询系数配置
     * @param name 系数名称，可选
     * @param period 绩效月，可选
     * @param type 系数类型，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    public CommonPage<CoefficientDO> queryCoefficientByNameAndType(String name, String period, CoefficientTemplateTypeEnum type,
                                                                   int pageNum, int pageSize) {
        // 保持与原有pageDO方法完全一致的校验逻辑
        // 原有方法通过PageCommand进行校验，这里直接进行相同的校验
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
        // 直接使用字段参数调用Repository，参数顺序与Repository完全一致
        CommonPage<CoefficientDO> pageInfo = coefficientRepository.queryCoefficientByNameAndType(name, period, type, pageNum, pageSize);
        return pageInfo;
    }

    /**
     * 查询所有系数
     * @param tenantCode
     * @param period
     * @return
     */
    public List<CoefficientDO> loadAll(String tenantCode, String period) {
        return coefficientRepository.queryAllCoefficientDO(tenantCode, period);
    }



}
