package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PerformanceFactorDomainService {
    @Resource
    IPerformanceFactorRepository performanceFactorRepository;

    public List<PerformanceFactor> loadAll(String tenantCode, String period) {
        return performanceFactorRepository.queryAll(tenantCode, period);
    }
}
