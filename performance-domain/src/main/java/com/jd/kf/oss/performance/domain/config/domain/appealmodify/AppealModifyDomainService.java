package com.jd.kf.oss.performance.domain.config.domain.appealmodify;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 归属数据修改领域服务
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class AppealModifyDomainService {

    @Resource
    private IAppealModifyRepository appealModifyRepository;

    /**
     * 根据条件分页查询归属数据修改
     *
     * @param period     绩效月
     * @param kpiName    指标名称，可选
     * @param skillId    技能ID，可选
     * @param ticketId   单号，可选
     * @param pageNum    页码
     * @param pageSize   页面大小
     * @return 分页结果
     */
    public CommonPage<AppealModifyDO> queryAppealModifyByConditions(String period, String kpiName, String skillId, String ticketId,
                                                                    int pageNum, int pageSize) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        return appealModifyRepository.queryAppealModifyByConditions(period, kpiName, skillId, ticketId, pageNum, pageSize);
    }

    /**
     * 批量删除归属数据修改
     * @param ids 待删除ID列表
     * @return 是否成功
     */
    public boolean batchDelete(List<Long> ids) {
        CheckUtil.notEmpty(ids, "待删除ID列表不能为空");

        return appealModifyRepository.batchDelete(ids);
    }

    /**
     * 根据条件查询归属数据修改列表（用于导出）
     *
     * @param tenantCode 租户标识
     * @param period     绩效月
     * @param erp        客服ERP，可选
     * @return 归属数据修改列表
     */
    public List<AppealModifyDO> queryAppealModifyList(String tenantCode, String period, String erp) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        
        return appealModifyRepository.queryAppealModifyList(tenantCode, period, erp);
    }

    /**
     * 根据条件查询归属数据修改列表（用于导出，支持更多查询条件）
     *
     * @param period     绩效月
     * @param kpiName    指标名称，可选
     * @param skillId    技能ID，可选
     * @param ticketId   单号，可选
     * @return 归属数据修改列表
     */
    public List<AppealModifyDO> queryAppealModifyListByConditions(String period, String kpiName, String skillId, String ticketId) {
        CheckUtil.notBlank(period, "绩效月不能为空");

        return appealModifyRepository.queryAppealModifyListByConditions(period, kpiName, skillId, ticketId);
    }

    /**
     * 批量保存OR更新归属数据修改（用于导入）
     * @param appealModifyList 归属数据修改列表
     * @return 保存成功的数量
     */
    public int saveOrUpdateBatch(List<AppealModifyDO> appealModifyList) {
        CheckUtil.notEmpty(appealModifyList, "归属数据修改列表不能为空");
        
        // 批量校验
        for (AppealModifyDO appealModifyDO : appealModifyList) {
            appealModifyDO.validateForSave();
        }
        
        return appealModifyRepository.saveOrUpdateBatch(appealModifyList);
    }

}
