package com.jd.kf.oss.performance.domain.config.domain.dept;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 部门仓储接口
 * 提供部门相关的数据访问方法
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
public interface IDeptRepository {
    /**
     * 批量获取部门路径名称
     * @param tenantCode 租户标识
     * @param deptIds 部门ID列表
     * @return 部门ID到部门路径名称的映射
     */
    Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds);
}
