package com.jd.kf.oss.performance.domain.config.domain.businessLine;

import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 业务线仓储接口
 * 提供业务线相关的数据访问方法
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IBusinessLineRepository {

    // CommonPage<BusinessLineDO> pageBusinessLine(PageCommand<BusinessLineDO> pageCommand);

    /**
     * 根据租户标识、编辑人和名称分页查询绩效组
     *
     * @param tenantCode      租户标识
     * @param editor          编辑人，可选
     * @param name            绩效组名称，可选
     * @param businessLineIds
     * @param pageNum         页码
     * @param pageSize        页面大小
     * @return 分页结果
     */
    CommonPage<BusinessLineDO> queryBusinessLineByTenantAndEditorAndName(String tenantCode, String editor, String name, List<String> businessLineIds, int pageNum, int pageSize);

    /**
     * 保存业务线信息
     * @param businessLineDO 业务线DO对象
     * @return 保存是否成功
     */
    boolean save(BusinessLineDO businessLineDO);

    /**
     * 更新业务线信息
     * @param businessLineDO 业务线DO对象
     * @return 更新是否成功
     */
    boolean updateBusinessLine(BusinessLineDO businessLineDO);

    /**
     * 根据业务线ID查询业务线信息
     * @param businessLineId 业务线ID
     * @return 业务线DO对象
     */
    BusinessLineDO queryBusinessLineByLineId(String businessLineId);

    /**
     * 查询所有业务线信息
     * @return 业务线DO对象列表
     */
    List<BusinessLineDO> queryAllBusinessLine();


    /**
     * 查询所有业务线信息
     * @return 业务线DO对象列表
     */
    List<BusinessLineDO> queryAllBusinessLineWithTenantCode();

    /**
     * 根据业务线ID列表查询业务线信息
     * @param businessLineIds 业务线ID列表
     * @return 业务线DO对象列表
     */
    List<BusinessLineDO> selectBusinessLineByLineIds(List<String> businessLineIds);


}
