package com.jd.kf.oss.performance.domain.config.domain.factor;

import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 因子仓储接口
 * 提供因子相关的数据访问方法
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IFactorRepository {
    /**
     * 保存或更新因子信息
     * @param factorDO 因子DO对象
     * @return 保存是否成功
     */
    boolean save(FactorDO factorDO);

    /**
     * 保存或更新因子信息
     * @param factorDO 因子DO对象
     * @return 保存是否成功
     */
    boolean delete(FactorDO factorDO);

    /**
     * 根据编码查询因子信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @param code 因子编码
     * @return 因子DO对象
     */
    FactorDO queryByCode(String tenantCode, String period, String code);

    /**
     * 查询所有因子信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @return 因子DO对象列表
     */
    List<FactorDO> queryAll(String tenantCode, String period);

    /**
     * 查询因子名
     * @param tenantCode
     * @param period
     * @param factorName
     * @param type
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<FactorDO> queryByName(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize);
}
