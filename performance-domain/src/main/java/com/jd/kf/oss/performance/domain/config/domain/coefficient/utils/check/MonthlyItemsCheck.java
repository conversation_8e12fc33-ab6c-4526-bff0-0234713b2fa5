package com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check;

import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientItem;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.NumberExtUtils;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class MonthlyItemsCheck extends AbsCoefficientItemsCheck {
    private final int COMPLETE_MONTH_MASK = (1 << 12) - 1;
    @Override
    public void check(CoefficientDO coefficientDO) {
        List<CoefficientItem> coefficientItems = coefficientDO.getCoefficientItems();
        CheckUtil.notEmpty(coefficientItems,"系数模板类型为月度时，系数条目不能为空");
        checkItem(coefficientItems);
    }

    /**
     * 检查月度系数条目的有效性
     * @param coefficientItems
     */
    public void checkItem(List<CoefficientItem> coefficientItems) {
        CheckUtil.size(coefficientItems.size(),12,"系数模板类型为月度时，系数条目数量必须为12");
        int monthBitMask = 0;
        for (CoefficientItem item : coefficientItems) {
            CheckUtil.isTrue(NumberExtUtils.isNumber(item.getCoefficientNum()), "入职月份区间系数为必填项");
            Integer month = NumberExtUtils.tryParseInteger(item.getLeftEndpoint());
            if(month == null || month <1 || month > 12){
                throw  new IllegalArgumentException("系数配置中的月份必须为1-12的整数");
            }
            // 检查月份是否重复
            int monthBit = 1 << (month - 1);
            CheckUtil.isTrue((monthBitMask & monthBit) == 0, "重复月份：" + month);
            // 设置对应月份的位
            monthBitMask |= monthBit;
            // 避免插入脏数据
            item.setRightEndpoint(null);
        }
        CheckUtil.isTrue( monthBitMask == COMPLETE_MONTH_MASK,"系数模板类型为月度时，月份必须连续且完整覆盖1到12");
    }

    @Override
    public CoefficientTemplateTypeEnum getTemplateType() {
        return CoefficientTemplateTypeEnum.MONTHLY;
    }
}

