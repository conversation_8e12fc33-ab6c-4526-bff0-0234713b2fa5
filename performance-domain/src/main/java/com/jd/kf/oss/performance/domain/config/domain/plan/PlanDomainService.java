package com.jd.kf.oss.performance.domain.config.domain.plan;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.wormhole.util.StringUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PlanDomainService {
    @Resource
    private IPlanRepository planRepository;


    /**
     * 查询所有方案
     * @param tenantCode
     * @param period
     * @return
     */
    public List<PlanDO> loadAllPlan(String tenantCode, String period) {
        return planRepository.queryAll(tenantCode, period);
    }

    /**
     * 根据方案code 查询方案
     * @param tenantCode
     * @param period
     * @return
     */
    public List<PlanDO> queryPlanByCodes(String tenantCode, String period, List<String> codes) {
        return planRepository.queryPlanByCodes(tenantCode, period, codes);
    }

    /**
     * 获取绩效方案DO
     * @param planCode
     * @return
     */
    public PlanDO getPlanByCodeAndPeriod(String planCode,String period) {
        CheckUtil.notBlank(planCode,period,"绩效方案编码和绩效月不能为空");
        return planRepository.selectPlanByCodeAndPeriod(planCode,period);

    }

    /**
     * 查询方案
     * @param tenantCode
     * @param period
     * @param factorName
     * @param type
     * @param pageNum
     * @param pageSize
     * @return
     */
    public CommonPage<PlanDO> queryPlans(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize) {
        return planRepository.queryByName(tenantCode, period, factorName, type, pageNum, pageSize);
    }

    /**
     * 查询所有被引用的系数code
     * @return
     */

    public List<String> listAllCoefficientCodesUsedByPlan(String period){
        CheckUtil.notBlank(period, "绩效月不能为空");
        List<PlanDO> planDOS = planRepository.selectAllPlansByPeriod(period);
        if (planDOS == null || planDOS.isEmpty()) {
            return new ArrayList<>(0);
        }
        List<String>  coefficientCodesUseByPlan = planDOS.parallelStream()
                .map(PlanDO::getAllCoefficientCodes)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        return coefficientCodesUseByPlan;
    }


    /**
     * 检查指定编码和周期的绩效方案是否存在
     * @param planCode 绩效方案ID
     * @param period 绩效周期
     * @return 存在的绩效方案对象
     */
    public PlanDO checkAndGetEvaluationPlan(String planCode, String period) {
        if(StringUtils.isBlank(planCode)|| StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("绩效方案ID和绩效月不能为空");
        }
        PlanDO planDO = getPlanByCodeAndPeriod(planCode, period);
        CheckUtil.notNull(planDO, "绩效方案不存在，请检查绩效方案code");
        return planDO;
    }
    /**
     * 解析公式中的指标index_capacity_1这样的模板名
     */
    public Set<String> parseFormulaIndexTemplates(String formula, List<FactorDO> factorDOS) {
        Set<String> indexes = parseFormula(formula, factorDOS, FormulaNode::getAllIndex);
        if(CollectionUtils.isEmpty(indexes)){
            return new HashSet<>(0);
        }
        return indexes.stream().filter(IndexTemplate::isIndexTemplate).collect(Collectors.toSet());
    }

    /**
     * 解析公式中的系数
     */
    public static Set<String> parseFormulaCoefficient(String formula, List<FactorDO> factorDOS){
        return parseFormula(formula, factorDOS, FormulaNode::getAllCoefficient);
    }

    /**
     * 解析公式中的因子
     */
    public static Set<String> parseFormulaFactor(String formula, List<FactorDO> factorDOS){
        return parseFormula(formula, factorDOS, FormulaNode::getAllFactor);
    }

    /**
     * 解析公式中的指标和系数
     */
    public static Set<String> parseFormula(String formula, List<FactorDO> factorDOS,
                                           Function<FormulaNode,List<FormulaParameter>> dataExtractFunc){
        Set<String> dataCodes = new HashSet<>();
        FormulaNode ast = null;
        try {
            FormulaParser parser = new FormulaParser();
            ast = parser.parse(formula);
        } catch (Exception e) {
            // 处理解析异常
            return dataCodes;
        }
        if (ast != null) {
            List<FormulaParameter> formulaParameters = dataExtractFunc.apply(ast);
            if (CollectionUtils.isNotEmpty(formulaParameters)) {
                dataCodes.addAll(formulaParameters.stream()
                        .map(FormulaParameter::getName)
                        .collect(Collectors.toSet()));
            }
            if (ast.getAllFactor() != null) {
                List<FormulaParameter> factorParameters = ast.getAllFactor();
                if (factorParameters != null) {
                    List<FactorDO>  factorsOfCurrentFactorParameters = matchFactor(factorParameters, factorDOS);
                    for(FactorDO factorDO : factorsOfCurrentFactorParameters) {
                        if (StringUtils.isNotBlank(factorDO.getFormula())) {
                            dataCodes.addAll(parseFormula(factorDO.getFormula(), factorDOS, dataExtractFunc));
                        }
                    }
                }
            }
        }
        return dataCodes;
    }


    private static List<FactorDO> matchFactor(List<FormulaParameter> parameters, List<FactorDO> factorDOS) {
        if (CollectionUtils.isEmpty(parameters)) {
            return Lists.newArrayList();
        }
        Set<String> sets = parameters.stream().map(FormulaParameter::getName).collect(Collectors.toSet());
        return factorDOS.stream().filter(a -> sets.contains(a.getCode())).collect(Collectors.toList());
    }

}
