package com.jd.kf.oss.performance.domain.runtime.domain.performanceresult;

import com.jd.kf.oss.performance.domain.runtime.domain.TaskResultTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class PerformanceResultService {

    @Resource
    private IPerformanceResultRepository performanceResultRepository;

    /**
     * 获取结果
     * @param taskId
     * @param businessLindId
     * @param taskResultTypeEnum
     * @param businessLindName
     * @return
     */
    public PerformanceResultDO getResult(String tenantCode, String period, String erp, Long taskId,
                                                     String businessLindId, TaskResultTypeEnum taskResultTypeEnum,
                                                     String businessLindName) {
        PerformanceResultDO resultDO = performanceResultRepository.query(
                tenantCode, period, taskId, businessLindId, erp
        );
        if (Objects.nonNull(resultDO)) {
            return resultDO;
        }
        resultDO = save(tenantCode, period, erp, taskId, businessLindId, taskResultTypeEnum, businessLindName);
        return resultDO;
    }

    /**
     * 批量保存
     */
    public void batchSave(List<PerformanceFactorResultDO> resultDOList) {
        performanceResultRepository.batchSave(resultDOList);
    }

    /**
     * 批量保存
     */
    public void batchSavePlan(List<PerformanceResultDO> resultDOList) {
        performanceResultRepository.batchSavePlan(resultDOList);
    }

    /**
     * 查询因子结果
     * @param tenantCode
     * @param period
     * @param taskId
     * @param erps
     * @return
     */
    public List<PerformanceFactorResultDO> query(String tenantCode, String period, Long taskId, List<String> erps) {
        return performanceResultRepository.query(tenantCode, period, taskId, erps);
    }

    /**
     * 获取因子结果
     * @param taskId
     * @param businessLindId
     * @param taskResultTypeEnum
     * @param businessLindName
     * @param factorCode
     * @return
     */
    public PerformanceFactorResultDO getFactorResult(String tenantCode, String period, String erp, Long taskId,
                                                     String businessLindId, TaskResultTypeEnum taskResultTypeEnum,
                                                     String businessLindName, String factorCode) {
        PerformanceFactorResultDO resultDO = performanceResultRepository.query(
                tenantCode, period, taskId, businessLindId, erp, factorCode
        );
        if (Objects.nonNull(resultDO)) {
            return resultDO;
        }
        resultDO = save(tenantCode, period, erp, taskId, businessLindId, taskResultTypeEnum, businessLindName, factorCode);
        return resultDO;
    }

    private PerformanceFactorResultDO save(String tenantCode, String period, String erp, Long taskId, String businessLindId,
                                           TaskResultTypeEnum taskResultTypeEnum, String businessLindName, String factorCode) {
        PerformanceFactorResultDO resultDO = buildFactorResult(tenantCode, period, erp, taskId, businessLindId, taskResultTypeEnum, businessLindName, factorCode);
        performanceResultRepository.save(resultDO);
        return resultDO;
    }

    private PerformanceResultDO save(String tenantCode, String period, String erp, Long taskId, String businessLindId,
                                           TaskResultTypeEnum taskResultTypeEnum, String businessLindName) {
        PerformanceResultDO resultDO = buildResult(tenantCode, period, erp, taskId, businessLindId, taskResultTypeEnum, businessLindName);
        performanceResultRepository.save(resultDO);
        return resultDO;
    }

    private static PerformanceFactorResultDO buildFactorResult(String tenantCode, String period, String erp, Long taskId,
                                                               String businessLindId, TaskResultTypeEnum taskResultTypeEnum,
                                                               String businessLindName, String factorCode) {
        PerformanceFactorResultDO resultDO;
        resultDO = new PerformanceFactorResultDO();
        resultDO.setTenantCode(tenantCode);
        resultDO.setBusinessLineId(businessLindId);
        resultDO.setBusinessLineName(businessLindName);
        resultDO.setTaskId(taskId);
        resultDO.setErp(erp);
        resultDO.setType(taskResultTypeEnum.name());
        resultDO.setFactorId(factorCode);
        resultDO.setDetail("");
        resultDO.setResult("");
        resultDO.setPeriod(period);
        resultDO.setStatus("");
        return resultDO;
    }

    private static PerformanceResultDO buildResult(String tenantCode, String period, String erp, Long taskId,
                                                               String businessLindId, TaskResultTypeEnum taskResultTypeEnum,
                                                               String businessLindName) {
        PerformanceResultDO resultDO = new PerformanceResultDO();
        resultDO.setTenantCode(tenantCode);
        resultDO.setBusinessLineId(businessLindId);
        resultDO.setBusinessLineName(businessLindName);
        resultDO.setTaskId(taskId);
        resultDO.setErp(erp);
        resultDO.setType(taskResultTypeEnum.name());
        resultDO.setDetail("");
        resultDO.setResult("");
        resultDO.setPeriod(period);
        resultDO.setStatus("");
        return resultDO;
    }
}
