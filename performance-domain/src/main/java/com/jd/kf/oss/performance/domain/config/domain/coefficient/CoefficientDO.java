package com.jd.kf.oss.performance.domain.config.domain.coefficient;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check.AbsCoefficientItemsCheck;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.IDUtils;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 系数信息实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoefficientDO  extends DomainBaseEntity {

    /**
     * 系数名称
     */
    private String name;

    /**
     * 系数类型：分段、月周期、常量
     */
    private CoefficientTemplateTypeEnum type;

    /**
     * 描述
     */
    private String description;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 系数items
     */
    private List<CoefficientItem> coefficientItems;

    /**
     * 生成系数code
     * @return
     */
    public String buildCode() {
        if (StringUtils.isNotBlank(getCode())) {
            return getCode();
        }
        String code = IDUtils.generateUUID(MetaTypeCodeEnum.COEFFICIENT.getCode());
        setCode(code);
        return code;
    }

    /**
     * 校验系数items配置的有效性
     */
    public void checkItemsByTemplateType() {
        if(type==null){
            throw new BizException("保存系数失败，系数类型不能为空");
        }
        AbsCoefficientItemsCheck checkStrategy = AbsCoefficientItemsCheck.getCheckStrategy(type);
        checkStrategy.check(this);
    }

    /**
     * 设置系数code和系数name到每个系数item中
     * 主要用于在保存时确保每个系数项都关联到正确的系数
     */
    public void propagateCoefficientItem(){
        if(coefficientItems == null || coefficientItems.isEmpty()){
            return;
        }
        for(CoefficientItem item : coefficientItems) {
            item.setCoefficientCode(getCode());
            item.setCoefficientName(getName());
            item.setPeriod(getPeriod());
        }
    }

    /**
     * 保存系数信息到数据库
     * @return
     */
    public boolean save() {
        ICoefficientRepository repository = SpringUtils.getBean(ICoefficientRepository.class);
        return repository.insertCoefficientDO(this);
    }
}

