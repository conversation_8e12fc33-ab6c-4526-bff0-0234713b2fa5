package com.jd.kf.oss.performance.domain.config.domain.index;

import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 指标仓储接口
 * 提供指标相关的数据访问方法
 *
 * <AUTHOR>
 * @date 2025/06/24
 */
public interface IIndexRepository {
    /**
     * 根据条件分页查询指标基础信息
     * @param tenantCode 租户标识
     * @param kpiCd 指标code，可选
     * @param kpiName 指标名称，可选
     * @param status 指标状态，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    CommonPage<IndexDO> queryIndexByConditions(String tenantCode, String kpiCd, String kpiName, String status, int pageNum, int pageSize);

    /**
     * 根据业务线ID查询指标信息
     * @param businessLineId 业务线ID
     * @param tenantCode 租户编码
     * @param period 期间
     * @return 指标DO对象列表
     */
    List<IndexDO> queryIndexesByBusinessId(String businessLineId, String tenantCode, String period);

    /**
     * 根据业务线编码列表和期间查询指标信息
     * @param businessLineCodes 业务线编码列表
     * @param period 期间
     * @return 指标DO对象列表
     */
    List<IndexDO> queryIndexPOsByPeriodAndBusinessLineId(List<String> businessLineCodes,String period);

    /**
     * 批量保存指标数据
     * @param dos 指标DO对象列表
     * @return 保存是否成功
     */
    boolean saveBatchIndex(List<IndexDO> dos);

    /**
     * 根据ID批量更新指标数据
     * @param dos 指标DO对象列表
     * @return 更新是否成功
     */
    boolean updateBatchIndexDOById(List<IndexDO> dos);

    /**
     * 根据业务线ID和期间删除指标数据
     * @param businessLineId 业务线ID
     * @param period 期间
     * @return 删除是否成功
     */
    boolean deleteIndexesByLineIdAndPeriod(String businessLineId, String period);

    /**
     * 从原始数据源查询所有指标数据
     * @return 指标DO对象列表
     */
    List<IndexDO> queryAllIndexDOFromOrigin();

    /**
     * 查询所有指标基础信息
     * @return 指标DO对象列表
     */
    List<IndexDO> queryAllIndexBasic();

    /**
     * 根据指标名称列表查询指标基础信息
     * @param kpiNames 指标名称列表
     * @return 指标基础信息DO对象列表
     */
    List<IndexBasicDO> selectIndexBasicByKpiNames(List<String> kpiNames);

    /**
     * 同步指标平台数据到基础数据库
     * @param platformList 指标平台数据列表
     * @param basicList 基础数据库中的指标数据列表
     */
    void syncIndexBasicWithPlatform(List<IndexDO> platformList, List<IndexDO> basicList);

    /**
     * 根据ID列表批量删除指标数据
     * @param dos ID列表
     * @return 删除是否成功
     */
    boolean deleteBatchById(List<Long> dos);

    /**
     * 根据指标编码列表查询指标基础信息
     * @param kpiCds 指标编码列表
     * @return 指标基础信息DO对象列表
     */
    List<IndexBasicDO> selectIndexBasicByKpiCds(List<String> kpiCds);


    /**
     * 批量删除指定业务线ID该绩效月下的关联的绩效指标
     */
    boolean deleteBatchByBusinessLineIdsAndPeriod(List<String> businessLineIds, String period);
}
