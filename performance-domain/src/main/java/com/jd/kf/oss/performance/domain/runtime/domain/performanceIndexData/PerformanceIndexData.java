package com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData;

import lombok.Data;

@Data
public class PerformanceIndexData {
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 姓名
     */
    private String name;

    /**
     * 是否新老人
     */
    private String newOrOld;

    /**
     * 主管erp
     */
    private String manager;

    /**
     * 主管名字
     */
    private String managerName;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标name
     */
    private String kpiName;

    /**
     * 实际业务线
     */
    private String businessJxName;

    /**
     * 技能组ID
     */
    private String skillId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 分子
     */
    private String numerator;

    /**
     * 分母
     */
    private String denominator;

    /**
     * 是否主管
     */
    private Boolean managerFlag;

    /**
     * 是否全月假
     */
    private Boolean weatherFullMonthVacation;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    public String buildKey() {
        String key = "IndexData_tenantCode:%s_period:%s_code:%s_erp:%s";
        return String.format(key, getTenantCode(), period, kpiCd, erp);
    }

    public static String buildKey(String tenantCode, String period, String kpiCd, String erp) {
        String key = "IndexData_tenantCode:%s_period:%s_code:%s_erp:%s";
        return String.format(key, tenantCode, period, kpiCd, erp);
    }


}
