package com.jd.kf.oss.performance.domain.config.domain.evaluate;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;

/**
 * 领域对象：上级评价
 * 包含上级评价的基本信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EvaluateDO extends DomainBaseEntity {

    /**
     * 上级评价ID
     */
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 主管ERP
     */
    private String erp;

    /**
     * 上级评分
     */
    private BigDecimal score;

    /**
     * 绩效月
     */
    private String period;

    public void validateForSave() {
        if (!this.period.matches("\\d{4}-\\d{2}")) {
            throw new IllegalArgumentException("绩效月格式不正确，应为YYYY-MM格式");
        }
        if (this.score == null) {
            throw new IllegalArgumentException("评价分数不能为空");
        }

        if (Strings.isBlank(this.erp)) {
            throw new IllegalArgumentException("主管Erp不能为空");
        }
    }
}