package com.jd.kf.oss.performance.domain.config.domain.performancetarget;

import com.alibaba.excel.util.StringUtils;
import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.kf.oss.performance.utils.NumberExtUtils;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2025/06/25
 * 绩效目标信息实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PerformanceTargetDO extends DomainBaseEntity {

    /**
     * 绩效组名称
     */
    private String businessLineName;

    /**
     * 绩效组id
     */
    private String businessLineId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 考核方案类型
     */
    private String evaluationType;

    /**
     * 考核方案名称
     */
    private String evaluationPlan;

    /**
     * 考核方案
     */
    private String evaluationPlanCode;

    /**
     * 单价
     */
    private String price;

    /**
     * 产能目标
     */
    private String cpd;

    /**
     * 绩效月
     */
    private String period;


    /**
     * 月标准天数
     */
    private String days;

    /**
     * 生成
     * @return
     */
    public String buildCode() {
        if (StringUtils.isNotBlank(getCode())) {
            return getCode();
        }
        String code =  UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%s", "target", code);
    }

    /**
     * 查询
     * @return
     */
    public PerformanceTargetDO queryByBusinessLineId(String businessLineId, String tenantCode, String period) {
        IPerformanceTargetRepository factorRepository = SpringUtils.getBean(IPerformanceTargetRepository.class);
        return factorRepository.queryTargetByBusinessLineId(businessLineId, tenantCode, period);
    }

    public PerformanceTargetDO save(){
        setCode(buildCode());
        IPerformanceTargetRepository targetRepository = SpringUtils.getBean(IPerformanceTargetRepository.class);
        targetRepository.saveTarget(this);
        return this;
    }

    public void checkCPD() {
        if(StringUtils.isBlank(cpd)){
            return;
        }
        if (!NumberExtUtils.isNumber(cpd) || Double.parseDouble(cpd) < 0) {
            throw new IllegalArgumentException("cpd必须为大于等于0的数字");
        }
    }

    public void checkDays() {
        if(StringUtils.isBlank(days)){
            return;
        }
        if ( !NumberExtUtils.isInteger(days)) {
            throw new IllegalArgumentException("月标准天数不是整数");
        }
        int daysInt = Integer.parseInt(days);
        if (daysInt < 0 || daysInt > 31) {
            throw new IllegalArgumentException("月标准天数范围为0-31");
        }
    }

    public void checkPrice() {
        if(StringUtils.isBlank(price)) {
            return;
        }
        if (!NumberExtUtils.isNumber(price) || Double.parseDouble(price) < 0) {
            throw new IllegalArgumentException("单价不是数字或小于0");
        }
    }
    /**
     * 校验是否当前绩效月
     */
    public void checkIsCurrentPeriod() {
        if (StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("绩效月不能为空");
        }
        String currentPerformancePeriod = DateUtils.getCurrentPerformancePeriod();
        if (!Objects.equals(currentPerformancePeriod, period)) {
            throw new IllegalArgumentException("绩效月必须为当前绩效月");
        }
    }

    public void checkTargetConfigs(){
        checkCPD();
        checkDays();
        checkPrice();
        checkIsCurrentPeriod();
    }

    /**
     * 更新时，重置方案类型和方案
     */
    public void resetEvaluationPlan(){
       this.evaluationPlanCode=null;
    }
}
