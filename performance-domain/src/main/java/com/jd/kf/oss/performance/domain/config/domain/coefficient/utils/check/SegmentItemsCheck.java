package com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check;

import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientItem;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.NumberExtUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分段系数项检查类
 * 用于检查分段类型的系数项是否符合规则
 */
@Component
public class SegmentItemsCheck extends AbsCoefficientItemsCheck {

    /**
     * 检查系数数据对象
     * @param coefficientDO 系数数据对象
     * @throws IllegalArgumentException 如果系数条目为空或配置错误
     */
    @Override
    public void check(CoefficientDO coefficientDO) {
        if (coefficientDO.getCoefficientItems() == null || coefficientDO.getCoefficientItems().isEmpty()) {
            throw new IllegalArgumentException("系数模板类型为分段时，系数条目不能为空");
        }
        checkItem(coefficientDO.getCoefficientItems());
    }

    /**
     * 检查系数条目列表
     * @param items 系数条目列表
     * @throws IllegalArgumentException 如果系数条目配置错误
     */
    private void checkItem(List<CoefficientItem> items) {
        for (CoefficientItem item : items) {
            validateNumberField(item.getLeftEndpoint(), "分段系数左端点必须非空且为数字");
            validateNumberField(item.getRightEndpoint(), "分段系数右端点必须非空且为数字");
            validateNumberField(item.getCoefficientNum(), "系数值必须非空且为数字");
        }
        // 按左端点排序系数条目
        List<CoefficientItem> sortedItems = items.stream()
                .sorted(Comparator.comparing(item ->Double.parseDouble(item.getLeftEndpoint())))
                .collect(Collectors.toList());
        //校验区间连续性
        validateIntervalContinuity(sortedItems);
    }

    private void validateNumberField(String value, String errorMessage) {
        CheckUtil.isTrue(NumberExtUtils.isNumber(value), errorMessage);
    }

    private void validateIntervalContinuity(List<CoefficientItem> sortedItems) {
        // 检查第一个元素的左端点是否为0
        double firstLeft = Double.parseDouble(sortedItems.get(0).getLeftEndpoint());
        CheckUtil.isTrue(firstLeft == 0.0, "分段最小值字段必须为0");
        double previousRight = firstLeft;
        for (CoefficientItem item : sortedItems) {
            double left = Double.parseDouble(item.getLeftEndpoint());
            double right = Double.parseDouble(item.getRightEndpoint());
            // 只用于比较的话，可以使用== 或者equals 或者compare，一旦涉及到计算+-*/，就会失真
            CheckUtil.isTrue(previousRight==left, "分段最小值字段必须从0开始且必须连续");
            CheckUtil.isTrue(left<right, "不能添加交叉分段数值");
            previousRight = right;
        }
    }
    /**
     * 获取模板类型
     * @return 返回分段类型的枚举值
     */
    @Override
    public CoefficientTemplateTypeEnum getTemplateType() {
        return CoefficientTemplateTypeEnum.SEGMENT;
    }

}
