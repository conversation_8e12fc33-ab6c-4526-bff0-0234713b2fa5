package com.jd.kf.oss.performance.domain.config.domain.evaluate;

import com.jd.kf.oss.performance.utils.CommonPage;
import java.util.List;

/**
 * 上级评价Repository接口
 * 提供上级评价相关的数据访问方法
 */
public interface IEvaluateRepository {
    /**
     * 分页查询上级评价数据（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令对象，包含查询条件和分页参数
     * @return 分页结果
     */
    // CommonPage<EvaluateDO> pageEvaluate(PageCommand<EvaluateDO> pageCommand);

    /**
     * 根据条件分页查询上级评价数据
     * @param period 绩效月
     * @param managerErp 主管ERP，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    CommonPage<EvaluateDO> queryEvaluateByConditions(String period, String managerErp, int pageNum, int pageSize);

    /**
     * 批量删除上级评价
     * @param evaluateIds 待删除评价ID列表
     * @param period 绩效月
     */
    void deleteEvaluate(List<Long> evaluateIds, String period);

    /**
     * 根据条件查询评价数据列表（用于导出）
     *
     * @param period     绩效月
     * @param managerErp 主管ERP列表，为空时查询所有
     * @return 评价数据列表
     */
    List<EvaluateDO> queryEvaluateList(String period, String managerErp);

    /**
     * 批量保存评价数据（用于导入）
     * @param evaluateList 评价数据列表
     * @return 保存成功的数量
     */
    int batchSaveEvaluate(List<EvaluateDO> evaluateList);
}