package com.jd.kf.oss.performance.domain.config.domain.coefficient;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public  class CoefficientItem extends DomainBaseEntity {

    /**
     * 系数id
     */
    private String coefficientCode;

    /**
     * 系数名称
     */
    private String coefficientName;

    /**
     * 左边界
     */
    private String leftEndpoint;

    /**
     * 右边界
     */
    private String rightEndpoint;

    /**
     * 系数值
     */
    private String coefficientNum;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
