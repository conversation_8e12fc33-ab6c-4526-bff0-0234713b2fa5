package com.jd.kf.oss.performance.domain.runtime.domain.performanceresult;

import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import lombok.Data;

import com.google.common.collect.Lists;
import java.util.List;

@Data
public class PerformanceProcessResult extends RuntimeDomainBaseEntity {
    /**
     * 绩效月
     */
    private String period;

    /**
     * 绩效条线
     */
    private String businessLineId;

    /**
     * 绩效条线名称
     */
    private String businessLineName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 数据类型
     */
    private TaskElementTypeEnum elementTypeEnum;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * code: factCode、indexCode、FunctionName
     */
    private String code;

    /**
     * 原始公式
     */
    private String formula;

    /**
     * 绩效结果
     */
    private String result;

    /**
     * 详情
     */
    private String detail;

    /**
     * 详情
     */
    private String status;

    /**
     * 过程
     */
    private List<PerformanceProcessResult> processResultList = Lists.newArrayList();

    public PerformanceProcessResult() {

    }

    public PerformanceProcessResult(String tenantCode, String period, String businessLineId, String businessLineName, Long taskId,
                                    TaskElementTypeEnum elementTypeEnum, String erp, String code, String formula) {
        super.setTenantCode(tenantCode);
        this.period = period;
        this.businessLineId = businessLineId;
        this.businessLineName = businessLineName;
        this.taskId = taskId;
        this.elementTypeEnum = elementTypeEnum;
        this.erp = erp;
        this.code = code;
        this.formula = formula;
    }

    /**
     * 构建key
     * @return
     */
    public String buildKey() {
        String key = "processResult_tenantCode:%s_period:%s_businessLineId:%s_code:%s_erp:%s";
        return String.format(key, getTenantCode(), period, businessLineId, code,erp);
    }

    public static String buildKey(String tenantCode, String period, String businessLineId, String code, String erp) {
        String key = "processResult_tenantCode:%s_period:%s_businessLineId:%s_code:%s_erp:%s";
        return String.format(key, tenantCode, period, businessLineId, code,erp);
    }
}
