package com.jd.kf.oss.performance.domain.runtime.aggregate;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IPerformanceIndexDataRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IWaiterHourAdjustmentRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceResultService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskItemDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDomainService;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PerformanceTaskAggregateService {
    @Resource
    private PerformanceUserDomainService performanceUserDomainService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;

    @Resource
    private PlanDomainService planDomainService;

    @Resource
    private IPerformanceIndexDataRepository performanceIndexDataRepository;

    @Resource
    private IWaiterHourAdjustmentRepository waiterHourAdjustmentRepository;

    @Resource
    private PerformanceCoefficientDomainService performanceCoefficientDomainService;

    @Resource
    private PerformanceFactorDomainService performanceFactorDomainService;

    @Resource
    private PerformanceIndexService performanceIndexService;

    @Resource
    private PerformanceResultService performanceResultService;

    /**
     * 计算任务
     * @param performanceTaskDO
     */
    public void calc(PerformanceTaskDO performanceTaskDO) {
        performanceTaskDO.getItems().forEach(this::runItemFirst);
    }

    private void runItemFirst(PerformanceTaskItemDO taskItemDO) {
        PerformanceTaskItemContext context = buildPerformanceTaskItemComposite(taskItemDO);
        List<PerformanceUserDO> userDOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(context.getUserDOList())) {
            userDOList.addAll(context.getUserDOList());
        }
        if (CollectionUtils.isNotEmpty(context.getAssistUserDOList())) {
            userDOList.addAll(context.getAssistUserDOList());
        }
        if (Objects.isNull(context.getPerformancePlanDO())) {
            return;
        }
        if (context.getPerformanceTaskItemDO().changeStatusToFactorRunning()) {
            for (PerformanceUserDO userDO : userDOList) {
                try {
                    context.getPerformancePlanDO().calc(userDO, context);
                } catch (Exception e) {
                }
            }
            context.getPerformanceTaskItemDO().changeStatusToFactorRunningCompleted();
        } else if (context.getPerformanceTaskItemDO().changeStatusToPlanRunning()) {
            for (PerformanceUserDO userDO : userDOList) {
                try {
                    context.getPerformancePlanDO().calc(userDO, context);
                } catch (Exception e) {
                }
            }
            context.getPerformanceTaskItemDO().changeStatusToPlanCompleted();
            savePlanProcess(context);
        } else {
            return;
        }
        saveFactorProcess(context);
    }

    private void savePlanProcess(PerformanceTaskItemContext context) {
        List<PerformanceProcessResult> factorResult = Lists.newArrayList();
        context.getUserDOList().forEach(a -> {
            factorResult.add(a.getPerformanceProcessResult());
        });

        List<PerformanceResultDO> resultDOS = context.getUserDOList().stream().map(PerformanceTaskAggregateService::convertToResult).collect(Collectors.toList());
        performanceResultService.batchSavePlan(resultDOS);
    }

    private void saveFactorProcess(PerformanceTaskItemContext context) {
        List<PerformanceProcessResult> factorResult = Lists.newArrayList();
        context.getUserDOList().forEach(a -> {
            getAllProcessResult(factorResult, a.getPerformanceProcessResult());
        });

        List<PerformanceFactorResultDO> resultDOS = factorResult.stream().map(PerformanceTaskAggregateService::convertToFactorResultDO).collect(Collectors.toList());
        Map<String, PerformanceFactorResultDO> resultDOMap = Maps.newHashMap();
        for (PerformanceFactorResultDO resultDO : resultDOS) {
            resultDOMap.put(resultDO.buildKey(), resultDO);
        }
        performanceResultService.batchSave(Lists.newArrayList(resultDOMap.values()));
    }

    private static PerformanceFactorResultDO convertToFactorResultDO(PerformanceProcessResult processResult) {
        if (processResult == null) {
            return null;
        }

        PerformanceFactorResultDO resultDO = new PerformanceFactorResultDO();
        // 复制相同属性
        BeanUtils.copyProperties(processResult, resultDO);
        // 设置特殊属性
        resultDO.setFactorId(processResult.getCode()); // code映射为factorId
        resultDO.setType("首次"); // 默认类型为首次

        return resultDO;
    }

    private static PerformanceProcessResult convertToFactorResultDO(PerformanceFactorResultDO resultDO) {
        if (resultDO == null) {
            return null;
        }

        PerformanceProcessResult processResult = new PerformanceProcessResult();
        // 复制相同属性
        BeanUtils.copyProperties(resultDO, processResult);
        // 设置特殊属性
        processResult.setCode(resultDO.getFactorId()); // code映射为factorId
        return processResult;
    }



    private static PerformanceResultDO convertToResult(PerformanceUserDO performanceUserDO) {
        if (performanceUserDO == null) {
            return null;
        }

        PerformanceResultDO processResult = new PerformanceResultDO();
        // 复制相同属性
        BeanUtils.copyProperties(performanceUserDO.getPerformanceProcessResult(), processResult);
        // 设置特殊属性
        processResult.setStatus("首次"); // 默认类型为首次
        return processResult;
    }


    private void getAllProcessResult(List<PerformanceProcessResult> factorProcess ,PerformanceProcessResult performanceProcessResult) {
        if (Objects.isNull(performanceProcessResult)) {
            return;
        }
        if (TaskElementTypeEnum.FACTOR.equals(performanceProcessResult.getElementTypeEnum())) {
            factorProcess.add(performanceProcessResult);
        }
        if (CollectionUtils.isNotEmpty(performanceProcessResult.getProcessResultList())) {
            for (PerformanceProcessResult processResult : performanceProcessResult.getProcessResultList()) {
                getAllProcessResult(factorProcess, processResult);
            }
        }
    }


    /**
     * 构建 PerformanceTaskItemComposite
     * @param taskItemDO
     * @return
     */
    private PerformanceTaskItemContext buildPerformanceTaskItemComposite(PerformanceTaskItemDO taskItemDO) {
        PerformanceTaskItemContext composite = new PerformanceTaskItemContext();
        composite.setPerformanceTaskItemDO(taskItemDO);
        buildTarget(taskItemDO, composite);
        buildPlan(composite);
        if (Objects.isNull(composite.getPerformancePlanDO())) {
            return composite;
        }

        buildCoefficient(taskItemDO, composite);
        buildFactor(taskItemDO, composite);
        buildHourData(taskItemDO, composite);
        buildIndex(taskItemDO, composite);
        buildData(taskItemDO, composite);
        buildAllTargetDO(taskItemDO, composite);
        buildUser(taskItemDO, composite);
        buildAssistUser(taskItemDO, composite);
        return composite;
    }

    private void buildUser(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceUserDO> userDOS = queryUser(taskItemDO.getTenantCode(), taskItemDO.getBusinessLineId(), taskItemDO.getPeriod(), taskItemDO.getTaskId());
        composite.setUserDOList(userDOS);
    }

    private void buildTarget(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        PerformanceTargetDO performanceTargetDO = performanceTargetDomainService.queryTargetByBusinessLineIdAndPeriod(taskItemDO.getBusinessLineId(), taskItemDO.getPeriod());
        composite.setPerformanceTargetDO(performanceTargetDO);
    }

    private void buildFactor(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceFactor> factorList = performanceFactorDomainService.loadAll(taskItemDO.getTenantCode(), taskItemDO.getPeriod());
        Map<String, PerformanceFactor> factorMap = factorList.stream().collect(Collectors.toMap(PerformanceFactor::buildKey, PerformanceFactor->PerformanceFactor, (a, b) -> a));
        composite.setPerformanceFactorMap(factorMap);
    }

    private void buildCoefficient(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceCoefficientDO> coefficientDOS = performanceCoefficientDomainService.loadAll(taskItemDO.getTenantCode(), taskItemDO.getPeriod());
        Map<String, PerformanceCoefficientDO> coefficientDOMap = coefficientDOS.stream().collect(Collectors.toMap(PerformanceCoefficientDO::buildKey, PerformanceCoefficientDO->PerformanceCoefficientDO, (a, b) -> a));
        composite.setCoefficientDOMap(coefficientDOMap);
    }

    private static void buildPlan(PerformanceTaskItemContext composite) {
        PerformanceTargetDO performanceTargetDO = composite.getPerformanceTargetDO();
        PerformancePlanDO planDO = new PerformancePlanDO(performanceTargetDO.getTenantCode(), performanceTargetDO.getPeriod(), performanceTargetDO.getEvaluationPlanCode());
        planDO = planDO.load();
        composite.setPerformancePlanDO(planDO);
    }

    private void buildData(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceIndexData> indexDataDOS = performanceIndexDataRepository.query(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), taskItemDO.getBusinessLineName());
        Map<String, PerformanceIndexData> indexDataDOMap = indexDataDOS.stream().collect(Collectors.toMap(PerformanceIndexData::buildKey, PerformanceIndexDataDO-> PerformanceIndexDataDO));
        composite.setIndexDataDOMap(indexDataDOMap);
    }

    private void buildHourData(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        if (CollectionUtils.isEmpty(composite.getUserDOList())) {
            return;
        }
        List<String> erps = composite.getUserDOList().stream().map(PerformanceUserDO::getErp).collect(Collectors.toList());
        List<WaiterHourAdjustment> adjustments = waiterHourAdjustmentRepository.query(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), erps);
        Map<String, WaiterHourAdjustment> waiterHourAdjustmentMap = adjustments.stream().collect(Collectors.toMap(WaiterHourAdjustment::buildKey, WaiterHourAdjustment-> WaiterHourAdjustment));
        composite.setWaiterHourAdjustmentMap(waiterHourAdjustmentMap);
    }

    private void buildIndex(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceIndex> indexList = performanceIndexService.queryByBusinessLineId(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), taskItemDO.getBusinessLineId());
        Map<String, PerformanceIndex> performanceIndexMap = indexList.stream().collect(Collectors.toMap(PerformanceIndex::buildKey, PerformanceIndex-> PerformanceIndex));
        composite.setPerformanceIndexMap(performanceIndexMap);
    }

    private void buildAssistUser(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        Set<String> assistErps = composite.getUserDOList().stream().map(PerformanceUserDO::getErp).collect(Collectors.toSet());
        Set<String> erps = composite.getIndexDataDOMap().values().stream().
                filter(a -> !assistErps.contains(a.getErp())).
                map(PerformanceIndexData::getErp).
                collect(Collectors.toSet());
        List<PerformanceUserDO> performanceUserDOS = performanceUserDomainService.queryUserByBusinessLineIdAndErps(taskItemDO.getBusinessLineId(), taskItemDO.getPeriod(), Lists.newArrayList(erps));
        buildPerformanceTargetDO(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), performanceUserDOS);
        buildPlan(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), performanceUserDOS);
        composite.setAssistUserDOList(performanceUserDOS);
    }

    /**
     * 查询用户
     * @param businessLineId
     * @param period
     * @return
     */
    private List<PerformanceUserDO> queryUser(String tenantCode, String businessLineId, String period, Long taskId) {
        List<PerformanceUserDO> performanceUserDOS = performanceUserDomainService.queryUserByBusinessLineId(businessLineId, period);
        buildPerformanceTargetDO(tenantCode, period, performanceUserDOS);
        buildPlan(tenantCode, period, performanceUserDOS);
        buildFactorResult(tenantCode, period, taskId, performanceUserDOS);
        return performanceUserDOS;
    }

    private void buildFactorResult(String tenantCode, String period, Long taskId, List<PerformanceUserDO> performanceUserDOS) {
        List<String> erps = performanceUserDOS.stream().map(PerformanceUserDO::getErp).collect(Collectors.toList());
        List<PerformanceFactorResultDO> resultDOS = performanceResultService.query(tenantCode, period, taskId, erps);
        Multimap<String, PerformanceFactorResultDO> resultDOMultimap = ArrayListMultimap.create();
        for (PerformanceFactorResultDO resultDO : resultDOS) {
            resultDOMultimap.put(resultDO.getErp(), resultDO);
        }
        performanceUserDOS.forEach(a -> {
            if (CollectionUtils.isNotEmpty(resultDOMultimap.get(a.getErp()))) {
                resultDOMultimap.get(a.getErp()).forEach(
                        b -> {
                            PerformanceProcessResult result = convertToFactorResultDO(b);
                            a.getFactorResultMap().put(result.buildKey(), result);
                        }
                );
            }
        });
    }

    private void buildAllTargetDO(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceTargetDO> targetDOS = performanceTargetDomainService.queryAllTargetByPeriod(taskItemDO.getPeriod());
        if (CollectionUtils.isEmpty(targetDOS)) {
            return;
        }
        Set<String> businessNameSets = composite.getIndexDataDOMap().values().stream().map(PerformanceIndexData::getBusinessJxName).collect(Collectors.toSet());
        targetDOS = targetDOS.stream().filter(a -> businessNameSets.contains(a.getBusinessLineName())).collect(Collectors.toList());
        Map<String, PerformanceTargetDO> targetDOMap = targetDOS.stream().collect(Collectors.toMap(PerformanceTargetDO::getBusinessLineId, PerformanceTargetDO->PerformanceTargetDO, (a, b) -> a));
        composite.setTargetDOMap(targetDOMap);
    }

    /**
     * 构建绩效目标
     * @param period
     * @param performanceUserDOS
     */
    private void buildPerformanceTargetDO(String tenantCode, String period, List<PerformanceUserDO> performanceUserDOS) {
        if (CollectionUtils.isEmpty(performanceUserDOS)) {
            return;
        }
        List<PerformanceTargetDO> targetDOS = performanceTargetDomainService.queryAllTargetByPeriod(period);
        if (CollectionUtils.isEmpty(targetDOS)) {
            return;
        }
        Map<String, PerformanceTargetDO> targetDOMap = targetDOS.stream().collect(Collectors.toMap(PerformanceTargetDO::getBusinessLineId, PerformanceTargetDO->PerformanceTargetDO, (a, b) -> a));
        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (targetDOMap.get(performanceUserDO.getBusinessLineId()) != null) {
                performanceUserDO.setPerformanceTargetDO(targetDOMap.get(performanceUserDO.getBusinessLineId()));
            }
        }
    }

    private void buildPlan(String tenantCode, String period, List<PerformanceUserDO> performanceUserDOS) {
        List<String> planCodes =Lists.newArrayList();
        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (Objects.nonNull(performanceUserDO)) {
                planCodes.add(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode());
            }
        }
        if (CollectionUtils.isEmpty(planCodes)) {
            return;
        }
        List<PlanDO> planDOS = planDomainService.queryPlanByCodes(tenantCode, period, planCodes);
        Map<String, PlanDO> planDOMap = planDOS.stream().collect(Collectors.toMap(PlanDO::getCode, PlanDO->PlanDO, (a, b) -> a));

        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (Objects.isNull(performanceUserDO.getPerformanceTargetDO())) {
                continue;
            }
            if (planDOMap.get(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode()) != null) {
                performanceUserDO.setPlanDO(planDOMap.get(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode()));
            }
        }
    }

}
