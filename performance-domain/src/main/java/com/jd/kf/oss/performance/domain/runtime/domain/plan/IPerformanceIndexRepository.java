package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;

import java.util.List;

public interface IPerformanceIndexRepository {
    /**
     * 查询指标信息
     * @param businessLineId
     * @param tenantCode
     * @param period
     * @return
     */
    List<PerformanceIndex> queryIndexesByBusinessId(String businessLineId, String tenantCode, String period);
}
