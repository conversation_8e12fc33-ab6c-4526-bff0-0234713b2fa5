package com.jd.kf.oss.performance.domain.runtime.domain.performanceuser;


import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class PerformanceUserDO extends RuntimeDomainBaseEntity {
    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 条线名称
     */
    private String businessLineName;

    /**
     * 绩效组目标
     */
    private PerformanceTargetDO performanceTargetDO;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 入职时间
     */
    private String entryDate;

    /**
     * 主管erp
     */
    private String managerErp;

    /**
     * 人资离职时间
     */
    private LocalDateTime quitTime;

    /**
     * 人员erp
     */
    private String erp;

    /**
     * 管理员/员工
     */
    private String type;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 绩效方案名称（从PerformanceTargetPO关联获取）
     */
    private PlanDO planDO;

    /**
     * 计算因子结果
     */
    private Map<String, PerformanceProcessResult> factorResultMap = Maps.newHashMap();

    /**
     * 计算过程
     */
    private PerformanceProcessResult performanceProcessResult;


    public int calculateServiceMonths() {
        if (StringUtils.isBlank(entryDate)) {
            return 12;
        }
        // 解析入职日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate hireDate = LocalDate.parse(entryDate, formatter);

        // 获取当前日期和当月20号
        LocalDate currentDate = LocalDate.now();
        LocalDate current20th = YearMonth.from(currentDate).atDay(20);

        // 如果当月20号已过，使用下个月20号
        if (currentDate.isAfter(current20th)) {
            current20th = YearMonth.from(currentDate).plusMonths(1).atDay(20);
        }

        // 计算月份差
        long monthsBetween = YearMonth.from(hireDate).until(YearMonth.from(current20th), java.time.temporal.ChronoUnit.MONTHS);

        return monthsBetween < 12 ? (int)monthsBetween : 12;
    }

    public void addProcess(PerformanceProcessResult resultDO) {
        if (Objects.nonNull(resultDO)) {
            factorResultMap.put(resultDO.buildKey(), resultDO);
        }
    }

    public List<String> getSupportBusinessLineIds() {
        Set<String> businessLineIdSets = factorResultMap.values().stream().map(PerformanceProcessResult::getBusinessLineId).collect(Collectors.toSet());
        businessLineIdSets.remove(businessLineId);
        if (CollectionUtils.isEmpty(businessLineIdSets)) {
            return Lists.newArrayList();
        }
        return new ArrayList<>(businessLineIdSets);
    }

}
