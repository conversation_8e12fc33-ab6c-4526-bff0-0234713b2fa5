package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.MatchCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.RankCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.SumCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.FactorSysEnum;
import com.jd.kf.oss.performance.enums.IndexConstant;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class PerformanceFactor extends RuntimeDomainBaseEntity {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("JavaScript");
    /**
     * code
     */
    private String code;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    private String type;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 绩效月
     */
    private String period;

    public String buildKey() {
        String key = "Factor_tenantCode:%s_period:%s_code:%s";
        return String.format(key, getTenantCode(), period, code);
    }

    public static String buildKey(String tenantCode, String period, String code) {
        String key = "Factor_tenantCode:%s_period:%s_code:%s";
        return String.format(key, tenantCode, period, code);
    }


    public String calc(PerformanceUserDO erp, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult resultDO = erp.getFactorResultMap().get(PerformanceFactorResultDO.buildKey(
                context.getPerformanceTargetDO().getTenantCode(),
                context.getPerformanceTargetDO().getPeriod(),
                context.getPerformanceTargetDO().getBusinessLineName(),
                getCode(),
                erp.getErp()));
        if (resultDO != null && resultDO.getStatus() == "已完成") {
            return resultDO.getResult();
        }
        if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.PLAN_RUNNING) && FactorSysEnum.getByCode(code) != null) {
            return calcSupport(erp, erp.getBusinessLineId(), parentProcessResult, context);
        }
        PerformanceProcessResult processResult = build(erp, context);
        FormulaParser parser = new FormulaParser();
        FormulaNode ast = parser.parse(formula);
        if (Objects.isNull(ast)) {
            return "";
        }
        Map<String, String> paramResultMap = Maps.newHashMap();
        for (FormulaParameter param : ast.getParams()) {
            String value = "";
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = factor.calc(erp, processResult, context);
            }
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                value = SpringUtils.getBean(PerformanceIndexService.class).calcIndex(param.getName(), erp, processResult, context);
            }
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.getType())) {
                PerformanceCoefficientDO coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
            }
            paramResultMap.put(param.getOriginText(), value);
        }

        for (FormulaNode node : ast.getFunctions()) {
            String value = "";
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(MatchCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(SumCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(RankCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            paramResultMap.put(node.getOriginText(), value);
        }
        String expression = String.valueOf(formula);
        for (Map.Entry<String, String> entry : paramResultMap.entrySet()) {
            String value = entry.getValue();
            if (StringUtils.equalsAnyIgnoreCase(entry.getValue(), "null") || StringUtils.isBlank(entry.getValue())) {
                value = "0";
            }
            expression = expression.replace(entry.getKey(), value);
        }
        Object result = null;
        Boolean status = true;
        try {
            result = engine.eval(expression);
        } catch (Exception e) {
            status = false;
            System.out.println("factor error");
        }
        addProcess(erp, parentProcessResult, processResult, expression, Objects.isNull(result) ? "" : String.valueOf(result), status, paramResultMap);
        return String.valueOf(result);
    }

    private void addProcess(PerformanceUserDO userDO, PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String expression, String result, Boolean status, Map<String, String> paramResultMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(formula).append(String.format("\n%s = %s\n", expression, result));
        paramResultMap.forEach((key, value) -> sb.append(String.format("\n%s = %s", key, value)));
        processResult.setResult(String.valueOf(result));
        processResult.setDetail(sb.toString());
        processResult.setStatus(status ? "已完成" : "未完成");
        parentProcessResult.getProcessResultList().add(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                getTenantCode(),
                period,
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }


    /**
     * 支援赛道单量
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportQuantity(PerformanceUserDO userDO, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceProcessResult processResult = userDO.getFactorResultMap().get(PerformanceProcessResult.buildKey(getTenantCode(), period, businessLineId, FactorSysEnum.SUPPORT_QUANTITY.getRelatedCode(), userDO.getErp()));
        if (Objects.isNull(processResult)) {
            return "";
        }
        return processResult.getResult();
    }

    /**
     * 支援折算总量
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportConversionTotal(PerformanceUserDO userDO, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        List<String> businessLineIds = userDO.getSupportBusinessLineIds();
        List<String> values = businessLineIds.stream().map(a -> {
            try {
                return calcSupportConversionQuantity(userDO, a, parentProcessResult, context);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());
        Double res = 0.0;
        for (String value : values) {
            if (StringUtils.isNotBlank(value)) {
                res += Double.valueOf(value);
            }
        }
        return String.valueOf(res);
    }

    private String calcSupport(PerformanceUserDO userDO, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception{
        FactorSysEnum factorSysEnum = FactorSysEnum.getByCode(code);
        if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.FACTOR_RUNNING)) {
            return "";
        } else if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.PLAN_RUNNING)) {
            if (FactorSysEnum.SUPPORT_BUSINESS_CPD.equals(factorSysEnum)) {
                return calcSupportBusinessLineCpd(userDO, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.equals(factorSysEnum)) {
                return calcSupportConversionQuantity(userDO, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_MAIN_PRICE.equals(factorSysEnum)) {
                return calcSupportPrice(userDO, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.COMPREHENSIVE_QUALITY_COEFFICIENT.equals(factorSysEnum)) {
                return calcComprehensiveQualityCoefficient(userDO, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_QUANTITY.equals(factorSysEnum)) {
                return calcSupportQuantity(userDO, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_CONVERSION_TOTAL.equals(factorSysEnum)) {
                return calcSupportConversionTotal(userDO, parentProcessResult, context);
            }
        }
        return "";
    }

    /**
     * 支援赛道CPD
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportBusinessLineCpd(PerformanceUserDO userDO, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceTargetDO targetDO = context.getTargetDOMap().get(businessLineId);
        return targetDO.getCpd();
    }

    /**
     * 支援折算量
     *
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportConversionQuantity(PerformanceUserDO userDO, String businessLineId,
                                                 PerformanceProcessResult parentProcessResult,
                                                 PerformanceTaskItemContext context) throws Exception {
        String supportQuantity = calcSupportQuantity(userDO, businessLineId, parentProcessResult, context);

        String primaryCpd = calcSupportBusinessLineCpd(userDO, userDO.getBusinessLineId(),null, context);

        String supportCpd = calcSupportBusinessLineCpd(userDO, businessLineId,null, context);
        Double cpd = 1.0;
        if (StringUtils.isBlank(primaryCpd) || StringUtils.isBlank(supportCpd)) {
            cpd = Double.parseDouble(primaryCpd) / Double.parseDouble(supportCpd);
        }
        FormulaParser parser = new FormulaParser();
        FormulaNode ast = parser.parse(formula);
        if (Objects.isNull(ast)) {
            return "";
        }
        String coefficient = "";
        for (FormulaParameter param : ast.getParams()) {
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.getType())) {
                PerformanceCoefficientDO coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(getTenantCode(), getPeriod(), param.getName()));
                coefficient = coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
            }
        }
        if (StringUtils.isBlank(supportQuantity) || StringUtils.isBlank(coefficient)) {
            return "0";
        }
        Double res = Double.parseDouble(supportQuantity) * cpd * Double.parseDouble(coefficient);
        return String.valueOf(res);
    }


    /**
     * 支援赛道单价
     * factor_support_main_price
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportPrice(PerformanceUserDO userDO, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceTargetDO targetDO = context.getTargetDOMap().get(businessLineId);
        return targetDO.getPrice();
    }


    /**
     * 综合质量系数
     * factor_support_main_price
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcComprehensiveQualityCoefficient(PerformanceUserDO userDO, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {

        List<String> businessLineIds = userDO.getSupportBusinessLineIds();
        PerformanceProcessResult processResult = userDO.getFactorResultMap().get(PerformanceProcessResult.buildKey(getTenantCode(), period, userDO.getBusinessLineId(), "factor_new_employee_accerleration_coefficient", userDO.getErp()));
        String coe = "1";
        if (Objects.nonNull(processResult)) {
            coe = processResult.getResult();
        }
        return "";
    }

    private String calcSingleQualityCoefficient(PerformanceUserDO userDO, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context, String coeStr) {
        String code = "factor_rank_business_line_quntity_coefficient";
        PerformanceProcessResult processResult = userDO.getFactorResultMap().get(PerformanceProcessResult.buildKey(getTenantCode(), period, businessLineId, code, userDO.getErp()));
        if (Objects.isNull(processResult)) {
            return "";
        }
        String rankCoeStr = processResult.getResult();
        // 分子
        String numeratorStr = calcSupportQuantity(userDO, businessLineId, parentProcessResult, context);
        String supportStr = calcSupportConversionTotal(userDO, parentProcessResult, context);

        Double numerator = StringUtils.isBlank(numeratorStr) ? 0.0 : Double.parseDouble(numeratorStr);
        Double support = StringUtils.isBlank(supportStr) ? 0.0 : Double.parseDouble(supportStr);
        Double rankCoe = StringUtils.isBlank(rankCoeStr) ? 0.0 : Double.parseDouble(rankCoeStr);
        Double coe = StringUtils.isBlank(coeStr) ? 1 : Double.parseDouble(coeStr);

        Double result = rankCoe * ( numerator / (numerator * coe + support *coe));
        return String.valueOf(result);
    }


}
