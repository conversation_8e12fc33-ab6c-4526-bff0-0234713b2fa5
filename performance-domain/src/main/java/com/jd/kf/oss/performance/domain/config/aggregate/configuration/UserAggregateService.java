package com.jd.kf.oss.performance.domain.config.aggregate.configuration;

import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLineComposite;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.UserComposite;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.converter.UserCompositeConverter;
import com.jd.kf.oss.performance.domain.config.domain.dept.DeptDO;
import com.jd.kf.oss.performance.domain.config.domain.dept.DeptDomainService;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/17
 * 用户聚合服务
 * 协调UserDomainService和DeptDomainService，提供完整的用户信息查询功能
 */
@Slf4j
@Service
public class UserAggregateService {

    @Resource
    private UserDomainService userDomainService;

    @Resource
    private DeptDomainService deptDomainService;

    /**
     * 分页查询用户信息（包含部门路径信息）
     * 替代原来的userDomainService.queryUserInfoByConditions方法
     *
     * @param tenantCode       租户标识
     * @param period           绩效月
     * @param managerErp       主管erp
     * @param deptId           部门ID
     * @param businessLineId   业务线ID
     * @param erp              用户erp
     * @param name             用户姓名
     * @param pageNum          页码
     * @param pageSize         页面大小
     * @return 分页结果（包含部门路径信息的UserComposite）
     */
    public CommonPage<UserComposite> queryUserPageInfo(String tenantCode, String period, String managerErp,
                                                       String deptId, String businessLineId, String planCode,
                                                       String erp, String name, int pageNum, int pageSize) {
        // 参数校验
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
        CheckUtil.notBlank(tenantCode, "租户标识不能为空");

        try {
            // 1. 通过UserDomainService查询用户基础信息
            CommonPage<UserDO> userPage = userDomainService.queryUserInfoByConditions(
                    tenantCode, period, managerErp, deptId, businessLineId,
                    planCode, erp, name, pageNum, pageSize);

            if (userPage == null || userPage.getData() == null || userPage.getData().isEmpty()) {
                return UserCompositeConverter.userDOPageToUserCompositePage(userPage);
            }

            List<UserDO> userList = userPage.getData();
            int userCount = userList.size();

            // 2. 提取所有涉及到的deptId（优化：避免Stream开销，使用传统循环）
            Set<String> deptIdSet = new HashSet<>(userCount);
            for (UserDO user : userList) {
                if (StringUtils.isNotBlank(user.getDeptId())) {
                    deptIdSet.add(user.getDeptId());
                }
            }

            // 3. 批量查询部门路径名称（如果没有部门ID则跳过查询）
            Map<String, String> deptIdToPathNameMap = deptIdSet.isEmpty() ?
                    Collections.emptyMap() :
                    deptDomainService.getDeptPathNamesByDeptIds(tenantCode, deptIdSet);

            // 4. 批量组装UserComposite（优化：预分配容量，减少扩容开销）
            List<UserComposite> userCompositeList = new ArrayList<>(userCount);
            for (UserDO user : userList) {
                UserComposite userComposite = createUserComposite(user, tenantCode, deptIdToPathNameMap);
                userCompositeList.add(userComposite);
            }

            // 5. 返回UserComposite分页结果
            return CommonPage.getCommonPage(
                    userPage.getPage(),
                    userPage.getSize(),
                    userPage.getTotal(),
                    userCompositeList
            );
        } catch (Exception e) {
            log.error("查询用户分页信息失败, tenantCode: {}, period: {}, pageNum: {}, pageSize: {}",
                    tenantCode, period, pageNum, pageSize, e);
            throw new RuntimeException("查询用户分页信息失败", e);
        }
    }
}
