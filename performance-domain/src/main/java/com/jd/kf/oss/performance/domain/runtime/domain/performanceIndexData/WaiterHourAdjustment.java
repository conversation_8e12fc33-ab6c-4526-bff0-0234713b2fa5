package com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class WaiterHourAdjustment {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 姓名
     */
    private String name;

    /**
     * 抽掉工时数
     */
    private Object mobilizeHours;

    /**
     * 带训&淘金者标识
     */
    private String trainerGolddiggerFlag;

    public String buildKey() {
        String key = "WaiterHour_tenantCode:%s_period:%s_golddigger:%s_erp:%s";
        return String.format(key, getTenantCode(), period, trainerGolddiggerFlag, erp);
    }

    public static String buildKey(String tenantCode, String period, String golddigger, String erp) {
        String key = "WaiterHour_tenantCode:%s_period:%s_golddigger:%s_erp:%s";
        return String.format(key, tenantCode, period, golddigger, erp);
    }
}
