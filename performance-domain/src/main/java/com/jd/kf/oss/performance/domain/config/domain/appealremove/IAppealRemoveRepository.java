package com.jd.kf.oss.performance.domain.config.domain.appealremove;

import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 无效数据剔除仓储接口
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface IAppealRemoveRepository {

    /**
     * 分页查询无效数据剔除（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令
     * @return 分页结果
     */
    // CommonPage<AppealRemoveDO> pageAppealRemove(PageCommand<AppealRemoveDO> pageCommand);

    /**
     * 根据条件分页查询无效数据剔除
     * @param period 绩效月
     * @param kpiName 指标名称，可选
     * @param ticketId 需剔除的业务单号，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    CommonPage<AppealRemoveDO> queryAppealRemoveByConditions(String period, String kpiName, String ticketId, int pageNum, int pageSize);

    /**
     * 批量删除无效数据剔除（软删除）
     * @param ids 待删除ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 根据条件查询无效数据剔除列表（用于导出）
     * @param period 绩效月
     * @param kpiCd 指标code，可选
     * @param erp 客服ERP，可选
     * @return 无效数据剔除列表
     */
    List<AppealRemoveDO> queryAppealRemoveList(String period, String kpiCd, String erp);

    /**
     * 批量保存无效数据剔除（用于导入）
     * @param appealRemoveList 无效数据剔除列表
     * @return 保存成功的数量
     */
    int batchSave(List<AppealRemoveDO> appealRemoveList);

    /**
     * 检查重复数据
     * @param period 绩效月
     * @param ticketId 单号
     * @param kpiName 指标Name
     * @return 是否存在重复
     */
    boolean existsDuplicate(String period, String ticketId, String kpiName);
}
