package com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite;

import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import lombok.Data;

import java.util.List;


@Data
public class TargetComposite {


    /**
     * 绩效目标
     */
    private PerformanceTargetDO targetDO;


    /**
     * 指标列表
     */
    private List<IndexDO> indexes;



}
