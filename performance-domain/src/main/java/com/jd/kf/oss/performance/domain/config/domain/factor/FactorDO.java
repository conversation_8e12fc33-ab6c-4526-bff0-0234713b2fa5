package com.jd.kf.oss.performance.domain.config.domain.factor;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;


@EqualsAndHashCode(callSuper = true)
@Data
public class FactorDO extends DomainBaseEntity {
    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    private String type;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;


    /**
     * 公式展示信息
     */
    private String formulaDisplayInfo;

    /**
     * 公式描述信息
     */
    private String displayInfo;


    public FactorDO(String tenantCode, String code, String period) {
        this.setTenantCode(tenantCode);
        this.setCode(code);
        this.period = period;
    }

    public FactorDO() {
    }

    /**
     * 生成
     * @return
     */
    public String buildCode() {
        if (StringUtils.isNotBlank(getCode())) {
            return getCode();
        }
        String code =  UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%s", "factor", code);
    }

    /**
     * 持久化
     * @return
     */
    public Long save() {
        IFactorRepository factorRepository = SpringUtils.getBean(IFactorRepository.class);
        factorRepository.save(this);
        return getId();
    }


    /**
     * 持久化
     * @return
     */
    public Boolean delete() {
        IFactorRepository factorRepository = SpringUtils.getBean(IFactorRepository.class);
        return factorRepository.delete(this);
    }

    /**
     * 查询因子
     * @param tenantCode
     * @param period
     * @param code
     * @return
     */
    public FactorDO loadByCode() {
        IFactorRepository factorRepository = SpringUtils.getBean(IFactorRepository.class);
        return factorRepository.queryByCode(this.getTenantCode(), this.getPeriod(), this.getCode());
    }
}
