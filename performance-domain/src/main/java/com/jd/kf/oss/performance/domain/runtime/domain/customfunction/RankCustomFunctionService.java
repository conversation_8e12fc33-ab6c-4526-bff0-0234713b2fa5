package com.jd.kf.oss.performance.domain.runtime.domain.customfunction;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;
import javafx.util.Pair;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
public class RankCustomFunctionService {
    public String calc(PerformanceUserDO erp, FormulaNode formulaNode, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        if (CollectionUtils.isEmpty(formulaNode.getParams()) || CollectionUtils.size(formulaNode.getParams()) != 1) {
            return "";
        }
        PerformanceProcessResult processResult = build(erp, formulaNode.getName(), formulaNode.originText, context);

        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTargetDO().getPeriod();
        FormulaParameter parameter = formulaNode.getParams().get(0);
        PerformanceIndex index = context.getPerformanceIndexMap().get(PerformanceIndex.buildKey(tenantCode, period, parameter.getName()));
        if (Objects.isNull(index)) {
            return "";
        }
        List<Pair<String, String>> pairs = Lists.newArrayList();
        for (PerformanceUserDO performanceUserDO : context.getUserDOList()) {
            String value = index.calc(performanceUserDO, context);
            pairs.add(new Pair<>(value, performanceUserDO.getErp()));
        }
        sortPairs(pairs);
        Integer pos = 0;
        String result = "";
        for (int i = 0; i < pairs.size(); i++) {
            if (StringUtils.equalsIgnoreCase(pairs.get(i).getValue(), erp.getErp())) {
                pos = i + 1;
                result = String.valueOf((Double.valueOf(i + 1)) / (Double.valueOf(pairs.size())));
                break;
            }
        }
        addProcess(parentProcessResult, processResult, result, String.format("%s / %s", pos, pairs.size()));
        return result;
    }

    private void addProcess(PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String result, String detail) {
        processResult.setResult(String.valueOf(result));
        processResult.setDetail(detail);
        parentProcessResult.getProcessResultList().add(processResult);
    }

    /**
     * 排序
     * @param pairs
     */
    private static void sortPairs(List<Pair<String, String>> pairs) {
        Collections.sort(pairs, new Comparator<Pair<String, String>>() {
            @Override
            public int compare(Pair<String, String> p1, Pair<String, String> p2) {
                // 处理空字符串分数为0的情况
                String score1 = p1.getKey().isEmpty() ? "0" : p1.getKey();
                String score2 = p2.getKey().isEmpty() ? "0" : p2.getKey();

                // 将字符串分数转换为整数比较
                return Double.compare(Double.parseDouble(score1), Double.parseDouble(score2));
            }
        });
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, String formula, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }
}
