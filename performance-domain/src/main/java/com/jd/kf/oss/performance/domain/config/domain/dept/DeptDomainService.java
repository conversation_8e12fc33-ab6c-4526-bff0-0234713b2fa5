package com.jd.kf.oss.performance.domain.config.domain.dept;

import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.utils.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/17
 * 部门领域服务
 */
@Slf4j
@Service
public class DeptDomainService {

    @Resource
    private IDeptRepository deptRepository;

    @Resource
    private DeptPathCache deptPathCache;

    /**
     * 批量获取部门路径名称（带缓存优化）
     *
     * @param tenantCode 租户标识
     * @param deptIds 部门ID列表
     * @return 部门ID到部门路径名称的映射
     */
    public Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }

        // 1. 从缓存中获取已存在的部门路径
        Map<String, String> result = new HashMap<>(deptIds.size());
        Set<String> missingDeptIds = new HashSet<>();

        for (String deptId : deptIds) {
            String cachedPathName = deptPathCache.getDeptPathName(tenantCode, deptId);
            if (cachedPathName != null) {
                result.put(deptId, cachedPathName);
            } else {
                missingDeptIds.add(deptId);
            }
        }

        // 2. 如果有缓存未命中的部门ID，从数据库查询
        if (!missingDeptIds.isEmpty()) {
            try {
                Map<String, String> dbResult = deptRepository.getDeptPathNamesByDeptIds(tenantCode, missingDeptIds);

                // 3. 将查询结果放入缓存并合并到最终结果
                if (!dbResult.isEmpty()) {
                    deptPathCache.putDeptPathNames(tenantCode, dbResult);
                    result.putAll(dbResult);
                }

                // 4. 对于数据库中也不存在的部门ID，缓存空字符串避免重复查询
                for (String missingId : missingDeptIds) {
                    if (!dbResult.containsKey(missingId)) {
                        deptPathCache.putDeptPathName(tenantCode, missingId, "");
                        result.put(missingId, "");
                    }
                }

                log.debug("部门路径查询 - 总数: {}, 缓存命中: {}, 数据库查询: {}",
                         deptIds.size(), deptIds.size() - missingDeptIds.size(), missingDeptIds.size());
            } catch (Exception e) {
                log.warn("从数据库查询部门路径失败, tenantCode: {}, missingDeptIds: {}", tenantCode, missingDeptIds, e);
                // 查询失败时，返回已缓存的结果
            }
        } else {
            log.debug("部门路径查询 - 全部命中缓存, 总数: {}", deptIds.size());
        }

        return result;
    }
}
