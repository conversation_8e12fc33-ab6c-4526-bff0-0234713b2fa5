package com.jd.kf.oss.performance.domain.config.domain.dept;

import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.utils.CheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/17
 * 部门领域服务
 */
@Slf4j
@Service
public class DeptDomainService {

    @Resource
    private IDeptRepository deptRepository;


    /**
     * 批量获取部门路径名称
     * @param tenantCode 租户标识
     * @param deptIds 部门ID列表
     * @return 部门ID到部门路径名称的映射
     */
    public Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }

        return deptRepository.getDeptPathNamesByDeptIds(tenantCode, deptIds);
    }
}
