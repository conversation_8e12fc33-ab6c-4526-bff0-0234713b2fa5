package com.jd.kf.oss.performance.domain.config.domain.plan;

import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 计划仓储接口
 * 提供计划相关的数据访问方法
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IPlanRepository {
    /**
     * 保存或更新计划信息
     * @param PlanDO 计划DO对象
     * @return 保存是否成功
     */
    boolean save(PlanDO PlanDO);

    /**
     * 保存或更新计划信息
     * @return 保存是否成功
     */
    boolean delete(String tenantCode, String period, String code);

    /**
     * 根据编码查询计划信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @param code 计划编码
     * @return 计划DO对象
     */
    PlanDO queryByCode(String tenantCode, String period, String code);

    /**
     * 查询所有计划信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @return 计划DO对象列表
     */
    List<PlanDO> queryAll(String tenantCode, String period);

    /**
     * 查询所有计划信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @return 计划DO对象列表
     */
    List<PlanDO> queryPlanByCodes(String tenantCode, String period, List<String> codes);

    /**
     * 根据计划编码和期间查询计划信息
     * @param planCode 计划编码
     * @param period 期间
     * @return 计划DO对象
     */
    PlanDO selectPlanByCodeAndPeriod(String planCode,String period);

    /**
     * 根据期间查询所有绩效方案
     * @param period 期间
     * @return 计划DO对象列表
     */
    List<PlanDO>  selectAllPlansByPeriod(String period);

    /**
     * 查询方案名
     * @param tenantCode
     * @param period
     * @param factorName
     * @param type
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<PlanDO> queryByName(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize);
}
