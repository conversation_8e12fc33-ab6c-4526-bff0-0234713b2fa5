package com.jd.kf.oss.performance.domain.runtime.domain.customfunction;

import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceFactor;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndexService;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SumCustomFunctionService {
    @Resource
    private MatchCustomFunctionService matchCustomFunctionService;
    @Resource
    private RankCustomFunctionService rankCustomFunctionService;

    @Resource
    private PerformanceIndexService performanceIndexService;

    public String calc(PerformanceUserDO erp, FormulaNode formulaNode, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult = build(erp, formulaNode.getName(), formulaNode.getOriginText(), context);
        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTargetDO().getPeriod();
        List<String> paramResults = Lists.newArrayList();
        for (FormulaParameter param : formulaNode.getParams()) {
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                paramResults.add(performanceIndexService.calcIndex(param.getName(), erp, parentProcessResult, context));
            }
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(tenantCode, period, param.getName()));
                paramResults.add(factor.calc(erp, processResult, context));

            }
        }
        for (FormulaNode node : formulaNode.getFunctions()) {
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                paramResults.add(matchCustomFunctionService.calc(erp, node, processResult, context));
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                paramResults.add(calc(erp, node, processResult, context));
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                paramResults.add(rankCustomFunctionService.calc(erp, node, processResult, context));
            }
        }
        Double result = paramResults.stream().filter(StringUtils::isNotBlank).mapToDouble(Double::parseDouble).sum();
        addProcess(parentProcessResult, processResult, String.valueOf(result));
        return String.valueOf(result);
    }

    private void addProcess(PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String result) {
        processResult.setResult(String.valueOf(result));
        parentProcessResult.getProcessResultList().add(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, String formula, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FUNCTION,
                erp.getErp(),
                code,
                formula
        );
    }
}
