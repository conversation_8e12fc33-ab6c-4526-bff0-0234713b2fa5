package com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/07/17
 * 绩效组聚合实体
 */
@Data
public class BusinessLineComposite {
    private BusinessLineDO businessLineDO;

    /**
     * 方案名称
     */
    private String planName;

    /**
     *方案实体Code
     */
    private String PlanCode;
}
