package com.jd.kf.oss.performance.domain.config.domain.appealmodify;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;


/**
 * 归属数据修改领域对象
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppealModifyDO extends DomainBaseEntity {

    /**
     * 客服ERP
     */
    private String erp;

    /**
     * 单号
     */
    private String ticketId;

    /**
     * 指标名
     */
    private String indexName;

    /**
     * 修改后技能组ID
     */
    private String skillId;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 绩效月
     */
    private String period;
    /**
     * 保存前校验
     */
    public void validateForSave() {
        if (this.erp == null || this.erp.trim().isEmpty()) {
            throw new IllegalArgumentException("客服ERP不能为空");
        }
        if (this.period == null || this.period.trim().isEmpty() ||
                !Objects.equals(this.period, DateUtils.getCurrentPerformancePeriod())) {
            throw new IllegalArgumentException("绩效月不能为空");
        }
        if (this.ticketId == null || this.ticketId.trim().isEmpty()) {
            throw new IllegalArgumentException("单号不能为空");
        }
        if (this.kpiName == null || this.kpiName.trim().isEmpty()) {
            throw new IllegalArgumentException("指标名称不能为空");
        }
        if (this.skillId == null || this.skillId.trim().isEmpty()) {
            throw new IllegalArgumentException("技能ID不能为空");
        }
    }
}

