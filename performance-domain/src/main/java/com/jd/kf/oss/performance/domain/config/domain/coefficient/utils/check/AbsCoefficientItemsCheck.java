package com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check;

import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系数项检查的抽象基类
 */
public abstract class AbsCoefficientItemsCheck {

    /**
     * 存储所有系数检查策略的静态Map
     * 键为模板类型，值为对应的检查策略实例
     */
    public static Map<CoefficientTemplateTypeEnum, AbsCoefficientItemsCheck> checkStrategyMap = new ConcurrentHashMap<>();

    /**
     * 执行系数检查的抽象方法
     * 具体的检查逻辑由子类实现
     *
     * @param coefficientDO 待检查的系数数据对象
     */
    public abstract void check(CoefficientDO coefficientDO);

    /**
     * 获取当前检查策略适用的模板类型
     * 由子类实现以返回特定的模板类型
     *
     * @return 模板类型的字符串标识
     */
    public abstract CoefficientTemplateTypeEnum getTemplateType();

    /**
     * 初始化方法，在bean创建完成后自动调用
     * 将当前检查策略实例添加到静态Map中
     */
    @PostConstruct
    public void init() {
        checkStrategyMap.put(this.getTemplateType(), this);
    }

    /**
     * 根据模板类型获取对应的检查策略
     *
     * @param templateType 模板类型
     * @return 对应的检查策略实例
     * @throws IllegalArgumentException 如果找不到对应的检查策略
     */
    public static AbsCoefficientItemsCheck getCheckStrategy(CoefficientTemplateTypeEnum templateType) {
        AbsCoefficientItemsCheck checkStrategy = checkStrategyMap.get(templateType);
        if (checkStrategy == null) {
            throw new IllegalArgumentException("不支持的系数模板类型: " + templateType.getType());
        }
        return checkStrategy;
    }
}
