package com.jd.kf.oss.performance.domain.config.domain.index;

import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.kf.oss.performance.utils.NumberExtUtils;
import com.jd.wormhole.util.StringUtils;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Optional;

@Data
public class IndexDO  {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * kpi 类型: type =1 数量指标, type=2  率指标
     */
    private Integer kpiType;

    /**
     * 指标映射code
     * 如<质量指标1,index_quality_1>
     */
    private IndexTemplate template;

    /**
     * 描述
     */
    private String description;


    /**
     * 指标平台url跳转id
     */
    private String indexPlatformUrlCode;

    /**
     * 指标平台url跳转url
     */
    private String indexPlatformUrl;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 样本下限
     */
    private String threshold;
    /**
     * 状态
     */
    private IndexStatusEnum status;

    /**
     * 权重
     */
    private String weight;
    /**
     * 绩效月
     */
    private String period;

    /**
     * 修改人
     */
    private String editor;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;


    public void checkDateRange() {
        Optional<String> startOpt = Optional.ofNullable(startDate).filter(StringUtils::isNotBlank);
        Optional<String> endOpt = Optional.ofNullable(endDate).filter(StringUtils::isNotBlank);
        if (!startOpt.isPresent() && !endOpt.isPresent()) {
            return;
        }
        if (startOpt.isPresent() != endOpt.isPresent()) {
            throw new IllegalArgumentException("考核周期的开始日期和结束日期必须同时提供");
        }
        CheckUtil.isTrue(DateUtils.isCompleteMonth(startDate,endDate), "考核周期开始时间和结束时间之间必须是一个完整的自然月");
        // 验证日期范围
//        CheckUtil.isTrue(DateUtils.isBetweenCurrentPeriod(startDate), "考核周期的开始日期不在当前绩效月内");
//        CheckUtil.isTrue(DateUtils.isBetweenCurrentPeriod(endDate), "考核周期的结束日期不在当前绩效月内");
        // 验证日期顺序
        CheckUtil.isTrue(DateUtils.isBefore(startDate, endDate),"考核周期的开始日期不能晚于结束日期");
    }

    public void checkWeight(){
        if(StringUtils.isBlank(weight)){
            return;
        }
        if(!NumberExtUtils.isNumber(weight)) {
            throw new IllegalArgumentException("权重必须是数字");
        }
        double weightDouble = Double.parseDouble(weight);
        if(weightDouble < 0 || weightDouble > 100){
            throw new IllegalArgumentException("权重必须在0-100之间");
        }
    }

    public void checkThreshold(){
        if(StringUtils.isBlank(threshold)){
            return;
        }
        if(template!=null && !template.isQualityIndex()){
            //兜底避免脏数据
            threshold = null;
            return;
        }
        if(!NumberExtUtils.isNumber(threshold)) {
            throw new IllegalArgumentException("质量下限必须是数字");
        }
        double thresholdDouble = Double.parseDouble(threshold);
        if(thresholdDouble < 0 ){
            throw new IllegalArgumentException("质量下限不能小于0");
        }

    }



    public void checkConfigs(){
        checkWeight();
        checkThreshold();
        checkDateRange();
    }

    /**
     *
     * @return
     */
    public static IndexDO copyFromPlanIndex(String templateCode,String period,String businessLineId){
        IndexDO copy = new IndexDO();
        copy.setPeriod(period);
        copy.setBusinessLineId(businessLineId);
        copy.setTemplate(IndexTemplate.getByCode(templateCode));
        return copy;
    }



}
