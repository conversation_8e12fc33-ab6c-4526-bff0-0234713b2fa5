package com.jd.kf.oss.performance.domain.runtime.domain.customfunction;

import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.*;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class MatchCustomFunctionService {
    @Resource
    private RankCustomFunctionService rankCustomFunctionService;
    @Autowired
    @Lazy
    private SumCustomFunctionService sumCustomFunctionService;

    @Resource
    private PerformanceIndexService performanceIndexService;

    public String calc(PerformanceUserDO erp, FormulaNode formulaNode, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult = build(erp, formulaNode.getName(), formulaNode.originText, context);
        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTargetDO().getPeriod();
        PerformanceCoefficientDO coefficientDO = null;
        String value = "";
        for (FormulaParameter param : formulaNode.getParams()) {
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.type)) {
                coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(tenantCode, period, param.getName()));
            }
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                value = performanceIndexService.calcIndex(param.getName(), erp, processResult, context);
            }
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(tenantCode, period, param.getName()));
                value = factor.calc(erp, processResult, context);
            }
        }

        for (FormulaNode node : formulaNode.getFunctions()) {
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                value = calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                value = sumCustomFunctionService.calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                value = rankCustomFunctionService.calc(erp, node, processResult, context);
            }
        }
        String result = match(coefficientDO, value);
        addProcess(parentProcessResult, processResult, result);
        return result;
    }

    private String match(PerformanceCoefficientDO coefficientDO, String value) {
        if (Objects.isNull(coefficientDO) || StringUtils.isBlank(value)) {
            return "";
        }
        if (CoefficientTemplateTypeEnum.CONSTANT.equals(coefficientDO.getType())) {
            return coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
        }
        List<PerformanceCoefficientItem>  items = coefficientDO.getCoefficientItems();
        sortByLeftEndpointLambda(items);

        PerformanceCoefficientItem result = matchItem(value, items);
        return Objects.isNull(result) || Objects.isNull(result.getCoefficientNum()) ? "" : result.getCoefficientNum();
    }

    @Nullable
    private static PerformanceCoefficientItem matchItem(String value, List<PerformanceCoefficientItem> items) {
        PerformanceCoefficientItem result = null;
        try {
            double target = Double.parseDouble(value);
            result = items.stream()
                    .filter(item -> {
                        try {
                            double left = item.getLeftEndpoint() == null || item.getLeftEndpoint().isEmpty()
                                    ? Double.NEGATIVE_INFINITY
                                    : Double.parseDouble(item.getLeftEndpoint());

                            double right = item.getRightEndpoint() == null || item.getRightEndpoint().isEmpty()
                                    ? Double.POSITIVE_INFINITY
                                    : Double.parseDouble(item.getRightEndpoint());

                            return left <= target && target <= right;
                        } catch (NumberFormatException e) {
                            return false;
                        }
                    })
                    .findFirst()
                    .orElse(null);
        } catch (NumberFormatException e) {
            return null;
        }
        return result;
    }

    /**
     * 排序
     * @param items
     */
    public static void sortByLeftEndpointLambda(List<PerformanceCoefficientItem> items) {
        items.sort((o1, o2) -> {
            String left1 = o1.getLeftEndpoint();
            String left2 = o2.getLeftEndpoint();

            if (left1 == null || left1.isEmpty()) {
                return (left2 == null || left2.isEmpty()) ? 0 : -1;
            }
            if (left2 == null || left2.isEmpty()) {
                return 1;
            }
            try {
                return Double.compare(
                        Double.parseDouble(left1),
                        Double.parseDouble(left2)
                );
            } catch (NumberFormatException e) {
                return left1.compareTo(left2);
            }
        });
    }

    private void addProcess(PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String result) {
        processResult.setResult(String.valueOf(result));
        parentProcessResult.getProcessResultList().add(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, String formula, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }
}
