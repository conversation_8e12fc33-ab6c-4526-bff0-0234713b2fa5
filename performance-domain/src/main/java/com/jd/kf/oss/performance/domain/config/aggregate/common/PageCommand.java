package com.jd.kf.oss.performance.domain.config.aggregate.common;

import lombok.Data;

@Data
public class PageCommand<T>{
    private Integer pageNo;
    private Integer pageSize;
    private T obj;


    public static <T> PageCommand<T> toPageCommand(T dto, Integer pageNo, Integer pageSize) {
        PageCommand<T> pageCommand = new PageCommand<T>();
        pageCommand.setPageNo(pageNo);
        pageCommand.setPageSize(pageSize);
        pageCommand.setObj(dto);
        return pageCommand;
    }
}