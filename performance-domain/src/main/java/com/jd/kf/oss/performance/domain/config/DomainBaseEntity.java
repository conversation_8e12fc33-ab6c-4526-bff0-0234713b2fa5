package com.jd.kf.oss.performance.domain.config;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DomainBaseEntity {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * code
     */
    private String code;

    /**
     * 租户标识
     */
    private String tenantCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String editor;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}