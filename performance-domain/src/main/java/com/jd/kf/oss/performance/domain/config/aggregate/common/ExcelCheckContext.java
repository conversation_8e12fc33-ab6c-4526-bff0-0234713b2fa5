package com.jd.kf.oss.performance.domain.config.aggregate.common;

import lombok.Data;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class ExcelCheckContext<T> {
    private int  errorMsgMaxLength=100;

    private LinkedHashMap<Integer,T> dataMap;
    private LinkedHashMap<Integer, String> errorMap= new LinkedHashMap<>(10);


    public ExcelCheckContext(LinkedHashMap<Integer,T> dataMap) {
        this.dataMap = dataMap;
    }


    /**
     * 遍历数据映射并对每个元素执行检查操作，将不符合要求的元素移至错误映射
     */
    public void iterateDataMap(){
        Iterator<Map.Entry<Integer,T>> iterator = dataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, T> entry = iterator.next();
            Integer rowNO = entry.getKey();
            T obj = entry.getValue();
            try {
                checkFunction(iterator,rowNO,obj);
            } catch (Exception e) {
                errorMap.put(rowNO, truncateErrorMsg(e.getMessage()));
                iterator.remove();
            }
        }
    }

    /**
     * 截断错误信息字符串至指定最大长度
     * @param str 待处理的错误信息字符串，允许为null
     * @return 当输入为null时返回null，否则返回截断后或原字符串（未超长时）
     */
    private  String truncateErrorMsg(String str) {
        if (str == null) {
            return null;
        }
        return str.length() > errorMsgMaxLength ? str.substring(0, errorMsgMaxLength) : str;
    }

    /**
     * 检查迭代器中的元素是否符合特定条件
     * @param iterator 包含Map.Entry<Integer,T>的迭代器，用于遍历键值对集合
     * @param rowNO 行号标识，用于定位检查位置
     * @param obj 泛型对象
     */
    public void checkFunction( Iterator<Map.Entry<Integer,T>> iterator,int rowNO, T obj){

    }



}
