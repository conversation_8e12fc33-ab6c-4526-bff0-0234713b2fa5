package com.jd.kf.oss.performance.domain.config.aggregate.configuration;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDomainService;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexBasicDomainService;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.wormhole.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TargetAggregateService {

    @Autowired
    private IndexDomainService indexDomainService;

    @Autowired
    private PerformanceTargetDomainService targetDomainService;

    @Resource
    private BusinessLineDomainService businessLineDomainService;

    @Resource
    private PlanDomainService planDomainService;

    @Resource
    private PlanAggregateService planAggregateService;

    @Resource
    private FactorDomainService factorDomainService;
    @Resource
    private IndexBasicDomainService indexBasicDomainService;

    /**
     * 根据绩效组ID、租户代码和绩效月获取目标详情及关联指标列表
     * @param period 绩效月
     * @param tenantCode 租户代码
     * @param businessLineId 绩效组ID
     * @return 包含绩效目标对象和指标列表的组合对象
     */
    public TargetComposite getTargetDetail(String period, String tenantCode, String businessLineId) {
        PerformanceTargetDO targetDO = new PerformanceTargetDO().queryByBusinessLineId(businessLineId, tenantCode, period);
        List<IndexDO> indexDOs = indexDomainService.queryIndexesByBusinessId(businessLineId, tenantCode, period);
        TargetComposite targetComposite = new TargetComposite();
        targetComposite.setTargetDO(targetDO);
        targetComposite.setIndexes(indexDOs);
        return targetComposite;
    }

    /**
     * 根据绩效组code和绩效月查询聚合的绩效目标（包括指标）
     */
    public List<TargetComposite> listAggTargetByBusinessCodeAndPeriod(List<String> businessLineIds, String period) {
        if (CollectionUtils.isEmpty(businessLineIds) || period == null) {
            throw new IllegalArgumentException("绩效月/绩效组编码不能为空");
        }
        List<PerformanceTargetDO> performanceTargetDO = new ArrayList<>();
        List<IndexDO> indexDOs = new ArrayList<>();
        CompletableFuture<List<PerformanceTargetDO>> performanceTargetFuture = CompletableFuture.supplyAsync(() ->
                targetDomainService.queryTargetListByBusinessLineCodeAndPeriod(period, businessLineIds));
        CompletableFuture<List<IndexDO>> indexDOsFuture = CompletableFuture.supplyAsync(() ->
                indexDomainService.queryIndexDOsByPeriodAndBusinessLineId(businessLineIds, period));
        CompletableFuture.allOf(performanceTargetFuture, indexDOsFuture).join();
        performanceTargetDO = performanceTargetFuture.join();
        indexDOs = indexDOsFuture.join();
        Map<String, List<IndexDO>> indexOfDifferentBusinessLine = indexDOs.stream().collect(Collectors.groupingBy(IndexDO::getBusinessLineId));
        return combineListTargetAndIndex(performanceTargetDO, indexOfDifferentBusinessLine);
    }

    private List<TargetComposite> combineListTargetAndIndex(List<PerformanceTargetDO> targetDOS, Map<String, List<IndexDO>> indexOfDifferentBusinessLine) {
        if (CollectionUtils.isEmpty(targetDOS)) {
            return new ArrayList<>(0);
        }
        ArrayList<TargetComposite> targetCompositeList = new ArrayList<>(targetDOS.size());
        for (PerformanceTargetDO targetDO : targetDOS) {
            List<IndexDO> indexDOs = indexOfDifferentBusinessLine.get(targetDO.getBusinessLineId());
            targetCompositeList.add(combineTargetAndIndex(targetDO, indexDOs));
        }
        return targetCompositeList;
    }

    private TargetComposite combineTargetAndIndex(PerformanceTargetDO targetDO, List<IndexDO> indexDOs) {
        if (targetDO == null) {
            return null;
        }
        TargetComposite targetComposite = new TargetComposite();
        targetComposite.setTargetDO(targetDO);
        targetComposite.setIndexes(indexDOs);
        return targetComposite;
    }

    /**
     * 更新target composite聚合对象前的预检查
     * 校验cpd 绩效月 单价 天数
     */
    private void preCheckBeforeUpdateTargetAndIndex(TargetComposite targetComposite) {
        Objects.requireNonNull(targetComposite, "绩效目标不能为空");
        PerformanceTargetDO targetDO = targetComposite.getTargetDO();
        Objects.requireNonNull(targetDO, "绩效目标不能为空");
        // 校验绩效组是否存在
        Optional.ofNullable(businessLineDomainService.queryBusinessLineByLineId(targetDO.getBusinessLineId()))
                .orElseThrow(() -> new IllegalArgumentException("绩效组不存在"));
        //校验绩效方案和指标同时存在或者同时不存在
        targetDO.checkTargetConfigs();
    }



    /**
     * 更新target composite聚合对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTargetComposite(TargetComposite targetComposite) {
        // 校验targetDO indexDOs等
        preCheckBeforeUpdateTargetAndIndex(targetComposite);
        PerformanceTargetDO targetDO = targetComposite.getTargetDO();
        // 绩效方案不为空，校验并更新指标
        if(StringUtils.isNotBlank(targetDO.getEvaluationPlanCode())) {
            PlanDO planDO = planDomainService.checkAndGetEvaluationPlan(targetDO.getEvaluationPlanCode(), targetDO.getPeriod());
            // 校验并插入新的index配置
            List<IndexDO> indexes = targetComposite.getIndexes();
            checkIndexesAndPopulateForUpdate(indexes, planDO);
            indexDomainService.deleteIndexesByLineIdAndPeriod(targetDO.getBusinessLineId(), targetDO.getPeriod());
            indexDomainService.saveBatchIndexes(indexes, targetDO.getBusinessLineId(), targetDO.getPeriod());
        }else{
            // 置空绩效方案
            targetDO.resetEvaluationPlan();
            indexDomainService.deleteIndexesByLineIdAndPeriod(targetDO.getBusinessLineId(), targetDO.getPeriod());
        }
        targetDomainService.updateTargetByLineIdAndPeriod(targetDO);
    }


    private void checkIndexesAndPopulateForUpdate(List<IndexDO> indexes, PlanDO planDO){
        // 校验指标
        checkTemplateCodesByPlan(indexes,planDO);
        // 校验指标配置是否正确
        indexDomainService.checkAndPopulateIndexesConfig(indexes);
    }

    /**
     * 校验指标是否存在以及配置是否正确
     * @param indexes
     */
    public void checkTemplateCodesByPlan(List<IndexDO> indexes, PlanDO planDO){
        String tenantCode = UserContextHolder.getTenantCode();
        String period=planDO.getPeriod();
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        Set<String> indexTemplateCodeOfPlan=planDomainService.parseFormulaIndexTemplates(planDO.getFormula(),factorDOS);
        // 校验指标模板code是否和绩效方案中的指标模板code一致
        checkIndexesTemplateCodeMatchPlan(indexes, indexTemplateCodeOfPlan);
    }
    private void checkIndexesTemplateCodeMatchPlan(List<IndexDO> indexDOs,Set<String> templateCodesOfPlan) {
        if(CollectionUtils.isEmpty(indexDOs)&& CollectionUtils.isEmpty(templateCodesOfPlan)){
            return;
        }
        if(CollectionUtils.isEmpty(indexDOs) || CollectionUtils.isEmpty(templateCodesOfPlan)){
            throw new IllegalArgumentException("指标模板和绩效方案中的指标模板不一致");
        }
        Set<String> templateCodes = new HashSet<>();
        for (IndexDO index : indexDOs) {
            templateCodes.add(index.getTemplate().getCode());
        }
        if (!templateCodes.equals(templateCodesOfPlan)) {
            throw new IllegalArgumentException(String.format("指标模板和绩效方案中的指标模板不一致，方案中的模板: [%s]",
                            String.join(", ", templateCodesOfPlan)));
        }
    }


    /**
     * 检查导入的prices  days cpd
     * @param context
     * @return
     */
    public ExcelCheckContext<PerformanceTargetDO> checkForImportedTarget(ExcelCheckContext<PerformanceTargetDO> context,
                                                                         Consumer<PerformanceTargetDO> checkFunction) {
        Map<Integer, PerformanceTargetDO> originDataMap = context.getDataMap();
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();
        List<String> businessIds = new ArrayList<>(originDataMap.size());
        originDataMap.forEach((rowNO, targetDO) -> {
            String businessLineId = targetDO.getBusinessLineId();
            if (StringUtils.isNotBlank(businessLineId)) {
                businessIds.add(businessLineId);
            }
        });
        List<BusinessLineDO> linesInDB = businessLineDomainService.getBusinessLineByLineIds(businessIds);
        Map<String, BusinessLineDO> lineIdAndDOMap = linesInDB.stream().collect(Collectors.toMap(BusinessLineDO::getBusinessLineId,
                Function.identity(), (existing, replacement) -> existing));
        Iterator<Map.Entry<Integer, PerformanceTargetDO>> iterator = originDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, PerformanceTargetDO> entry = iterator.next();
            Integer rowNO = entry.getKey();
            PerformanceTargetDO targetDO = entry.getValue();
            try {
                if (!lineIdAndDOMap.containsKey(targetDO.getBusinessLineId())) {
                    errorMap.put(rowNO, "绩效组不存在，请检查绩效组ID");
                    iterator.remove();
                    continue;
                }
                // 兜底上传的绩效组名称
                targetDO.setBusinessLineName(lineIdAndDOMap.get(targetDO.getBusinessLineId()).getName());
                checkFunction.accept(targetDO);
            } catch (IllegalArgumentException e) {
                errorMap.put(rowNO, e.getMessage());
                iterator.remove();
            }
        }
        return context;
    }

    /**
     * 检查导入的绩效目标的价格和天数
     * @param context
     * @return
     */
    public void checkForImportedTargetPriceAndDays(ExcelCheckContext<PerformanceTargetDO> context) {
        Consumer<PerformanceTargetDO> checkFunction = targetDO -> {
            targetDO.checkDays();
            targetDO.checkPrice();
            targetDO.checkIsCurrentPeriod();
        };
        checkForImportedTarget(context, checkFunction);
    }

    public void checkForImportedTargetCPD(ExcelCheckContext<PerformanceTargetDO> context) {
        Consumer<PerformanceTargetDO> checkFunction = targetDO -> {
            targetDO.checkCPD();
            targetDO.checkIsCurrentPeriod();
        };
        checkForImportedTarget(context, checkFunction);
    }


    /**
     * 导入当前绩效月的指标更新数据库中的指标
     * @param targetComposites
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateImportIndexes(List<TargetComposite> targetComposites) {
        if(CollectionUtils.isEmpty(targetComposites)) {
            return;
        }
        Map<String,List<IndexDO>> importIndexes= new HashMap<>(targetComposites.size());
        List<String> businessLineIds = new ArrayList<>(targetComposites.size());
        targetComposites.forEach((targetComposite) -> {
            PerformanceTargetDO targetDO = targetComposite.getTargetDO();
            importIndexes.put(targetDO.getBusinessLineId(), targetComposite.getIndexes());
            businessLineIds.add(targetDO.getBusinessLineId());
        });
        String period=DateUtils.getCurrentPerformancePeriod();
        List<IndexDO> indexDOsInDB = indexDomainService.queryIndexDOsByPeriodAndBusinessLineId(businessLineIds,period);
        Map<String, List<IndexDO>> indexesOfLineInDB = indexDOsInDB.stream().collect(Collectors.groupingBy(IndexDO::getBusinessLineId));
        indexDomainService.diffUpdateByImportIndexes(indexesOfLineInDB, importIndexes);
        targetDomainService.updateModifyTimeAndOperator(businessLineIds, period);
    }

    /**
     * 获取绩效组id
     */
    private String getBusinessIdOfTargetComposite(TargetComposite targetComposite) {
        if(targetComposite==null||targetComposite.getTargetDO()==null) {
            return null;
        }
        return targetComposite.getTargetDO().getBusinessLineId();
    }

    /**
     * 获取绩效月
     */
    private String getPeriodOfTargetComposite(TargetComposite targetComposite) {
        if(targetComposite==null||targetComposite.getTargetDO()==null) {
            return null;
        }
        return targetComposite.getTargetDO().getPeriod();
    }

    private List<String> getBusinessLineIdFromTargetComposite(ExcelCheckContext<TargetComposite> context ) {
        Map<Integer,TargetComposite> originDataMap = context.getDataMap();
        if(CollectionUtils.isEmpty(originDataMap)) {
            return new ArrayList<>(0);
        }
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();
        List<String> businessIds = new ArrayList<>(originDataMap.size());
        Iterator<Map.Entry<Integer, TargetComposite>> iterator = originDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, TargetComposite> entry = iterator.next();
            Integer rowNO = entry.getKey();
            TargetComposite targetComposite = entry.getValue();
            String businessLineId = getBusinessIdOfTargetComposite(targetComposite);
            String period = getPeriodOfTargetComposite(targetComposite);
            if (StringUtils.isNotBlank(businessLineId) &&
                    Objects.equals(period, DateUtils.getCurrentPerformancePeriod())) {
                businessIds.add(businessLineId);
            } else {
                errorMap.put(rowNO, "绩效组ID为空或者不是当前绩效月");
                iterator.remove();
            }
        }
        return businessIds;
    }


    /**
     * 校验导入的绩效组是否存在，是否配置了绩效方案
     * @param context
     * @param businessIds
     */
    private void checkBusinessExists(ExcelCheckContext<TargetComposite> context, List<String> businessIds) {
        Map<Integer,TargetComposite> originDataMap = context.getDataMap();
        if(CollectionUtils.isEmpty(originDataMap)) {
            return;
        }
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();
        List<BusinessLineDO> linesInDB = businessLineDomainService.getBusinessLineByLineIds(businessIds);
        if(linesInDB==null){
            linesInDB=new ArrayList<>();
        }
        Map<String, BusinessLineDO> lineIdAndDOMap = linesInDB.stream()
                .collect(Collectors.toMap(BusinessLineDO::getBusinessLineId, Function.identity(), (existing, replacement) -> existing));
        Iterator<Map.Entry<Integer, TargetComposite>> iterator = originDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, TargetComposite> entry = iterator.next();
            Integer rowNO = entry.getKey();
            TargetComposite targetComposite = entry.getValue();
            // 校验绩效组是否存在
            if (!lineIdAndDOMap.containsKey(getBusinessIdOfTargetComposite(targetComposite))) {
                iterator.remove();
                errorMap.put(rowNO, "绩效组不存在");
            }
        }
    }

    /**
     * 检查导入的方案以及指标
     */
    public void checkForImportedPlanAndIndex(ExcelCheckContext<TargetComposite> context ) {
        // 校验并获取上传的绩效组id
        List<String> businessIds = getBusinessLineIdFromTargetComposite(context);
        // 校验绩效组是否存在
        checkBusinessExists(context,businessIds);
        // 校验绩效方案以及绩效指标
        checkAndProcessPlanAndIndex(context) ;
    }

    /**
     * 解析方案里面的指标模版code ，根据code提取上传的index，避免插入多余的指标
     * 校验上传的指标code是否存在
     * @param context
     */
    private void checkAndProcessPlanAndIndex(ExcelCheckContext<TargetComposite> context){
        Map<Integer,TargetComposite> originDataMap = context.getDataMap();
        if(CollectionUtils.isEmpty(originDataMap)) {
            return;
        }
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();
        List<String> businessIds = originDataMap.values().stream().map(this::getBusinessIdOfTargetComposite)
                .distinct().collect(Collectors.toList());
        // 指标模板code 如index_capacity_1
        Map<String, Set<String>> lineAndPlanTemplateCodeMap = planAggregateService.queryTemplateCodeOfPlan(businessIds, DateUtils.getCurrentPerformancePeriod());
        // 填充到指标模板中的指标code，具体的指标
        List<IndexDO> indexBasicDOS = indexDomainService.queryAllIndexBasic();
        Map<String, IndexDO> kpiNameAndIndexBasicMap= indexBasicDOS.stream()
                .collect(Collectors.toMap(IndexDO::getKpiName, Function.identity(), (existing, replacement) -> existing));
        // 校验方案指标是否都配置了
        Iterator<Map.Entry<Integer, TargetComposite>> iterator = originDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, TargetComposite> entry = iterator.next();
            Integer rowNO = entry.getKey();
            TargetComposite targetComposite = entry.getValue();
            List<IndexDO> indexDOs = targetComposite.getIndexes();
            try {
                String businessIdOfTargetComposite = getBusinessIdOfTargetComposite(targetComposite);
                Set<String> templateCodes = lineAndPlanTemplateCodeMap.get(businessIdOfTargetComposite);
                CheckUtil.notEmpty(templateCodes, "绩效组ID为" + businessIdOfTargetComposite + "的绩效组没有配置绩效方案，或者绩效方案没有使用指标模板");
                // 过滤一些没有出现在方案里面的模板，避免插入脏数据
                indexDomainService.extractIndexByTemplateNameOfPlan(indexDOs,templateCodes);
                // 校验指标的权重、样本下限、考核周期
                indexDomainService.checkIndexPropertiesConfig(indexDOs);
                indexDomainService.checkKpiNameExists(indexDOs, kpiNameAndIndexBasicMap);
                indexDomainService.populateImportIndexByIndexBasic(indexDOs, kpiNameAndIndexBasicMap);
            } catch (Exception e) {
                errorMap.put(rowNO, e.getMessage());
                iterator.remove();
            }
        }
    }



}
