package com.jd.kf.oss.performance.domain.config.domain.formula;


import com.jd.kf.oss.performance.exception.FormulaParseException;

/**
 * 公式解析器
 */
public class FormulaParser {
    /**
     * 公式原文
     */
    private String input;

    /**
     * 位置
     */
    private int pos;

    /**
     * 当前优先级
     */
    private int currentPriority;

    /**
     * 解析公式
     * @param formula
     * @return
     * @throws FormulaParseException
     */
    public FormulaNode parse(String formula) throws FormulaParseException {
        this.input = formula;
        this.pos = 0;
        this.currentPriority = 0;

        FormulaNode root = new FormulaNode();
        root.originText = formula;
        root.priority = 0;

        try {
            parseFormula(root);
            return root;
        } catch (Exception e) {
            throw new FormulaParseException("解析公式失败，当前位置：" + pos + "，上下文：" +
                    getContextString(), e);
        }
    }

    /**
     * 获取解析公式报错内容
     * @return
     */
    private String getContextString() {
        int start = Math.max(0, pos - 10);
        int end = Math.min(input.length(), pos + 10);
        return input.substring(start, end);
    }

    /**
     * 解析公式
     * @param parent
     */
    private void parseFormula(FormulaNode parent) {
        while (pos < input.length()) {
            char c = input.charAt(pos);

            if (c == '{') {
                parseParameter(parent);
            } else if (Character.isUpperCase(c)) {
                parseFunction(parent);
            } else {
                pos++;
            }
        }
    }

    /**
     * 解析参数
     * @param node
     */
    private void parseParameter(FormulaNode node) {
        int start = pos;
        while (pos < input.length() && input.charAt(pos) != '}') {
            pos++;
        }
        if (pos >= input.length()) {
            throw new IllegalArgumentException("参数未闭合，缺少右大括号");
        }
        pos++;

        String paramText = input.substring(start, pos);
        FormulaParameter param = new FormulaParameter();
        param.originText = paramText;
        param.priority = currentPriority;
        param.name = paramText.substring(1, paramText.length() - 1);

        if (paramText.startsWith("{factor_")) {
            param.type = "factor";
        } else if (paramText.startsWith("{coefficient_")) {
            param.type = "coefficient";
        } else if (paramText.startsWith("{index_")) {
            param.type = "index";
        } else {
            throw new IllegalArgumentException("未知参数类型: " + paramText);
        }

        node.params.add(param);
    }

    /**
     * 解析函数
     * @param parent
     */
    private void parseFunction(FormulaNode parent) {
        int start = pos;
        while (pos < input.length() && Character.isUpperCase(input.charAt(pos))) {
            pos++;
        }
        String funcName = input.substring(start, pos);

        if (pos >= input.length() || input.charAt(pos) != '(') {
            throw new IllegalArgumentException("函数缺少左括号: " + funcName);
        }
        pos++;
        currentPriority++;

        FormulaNode funcNode = new FormulaNode();
        funcNode.name = funcName;
        funcNode.priority = currentPriority;

        parseFunctionArguments(funcNode);

        if (pos >= input.length() || input.charAt(pos) != ')') {
            throw new IllegalArgumentException("函数缺少右括号: " + funcName);
        }
        pos++;

        funcNode.originText = input.substring(start, pos);
        validateFunction(funcNode);
        parent.functions.add(funcNode);
        currentPriority--;
    }

    /**
     * 解析函数参数
     * @param funcNode
     */
    private void parseFunctionArguments(FormulaNode funcNode) {
        while (pos < input.length() && input.charAt(pos) != ')') {
            char c = input.charAt(pos);

            if (c == '{') {
                parseParameter(funcNode);
            } else if (Character.isUpperCase(c)) {
                parseFunction(funcNode);
            } else if (c == ',') {
                pos++;
                if (pos < input.length() && input.charAt(pos) == ' ') {
                    pos++;
                }
            } else {
                pos++;
            }
        }
    }

    /**
     * 校验函数参数数量
     * @param funcNode
     */
    private void validateFunction(FormulaNode funcNode) {
        int totalArgs = funcNode.params.size() + funcNode.functions.size();
        switch (funcNode.name) {
            case "RANK":
                if (totalArgs != 1) {
                    throw new IllegalArgumentException("RANK函数只能有一个参数，实际有: " +
                            totalArgs + "，函数内容: " + funcNode.originText);
                }
                break;
            case "MATCH":
                if (totalArgs != 2) {
                    throw new IllegalArgumentException("MATCH函数必须有两个参数，实际有: " +
                            totalArgs + "，函数内容: " + funcNode.originText);
                }
                break;
            case "SUM":
                if (totalArgs < 1) {
                    throw new IllegalArgumentException("SUM函数至少需要一个参数，实际有: " +
                            totalArgs + "，函数内容: " + funcNode.originText);
                }
                break;
            default:
                throw new IllegalArgumentException("未知函数: " + funcNode.name);
        }
    }

    public static void main(String[] args) {
        String formula = "{factor_aaa} * (1 - {index_bbb}) + {coefficient_aaaa} * RANK({index_bbb}) + " +
                "MATCH(RANK({factor_ccc}), RANK({coefficient_ccc})) - " +
                "SUM({coefficient_aaaa}, SUM({index_111}, MATCH({index_112}, {coefficient_c}), RANK(RANK({index_43q}))))";

        try {
            FormulaParser parser = new FormulaParser();
            FormulaNode ast = parser.parse(formula);
            System.out.println(ast);
        } catch (FormulaParseException e) {
            System.err.println("公式解析错误: " + e.getMessage());
        }
    }

}
