package com.jd.kf.oss.performance.domain.config.aggregate.configuration;


import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDomainService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CoefficientAggregateService {

    @Resource
    private CoefficientDomainService coefficientDomainService;

    @Resource
    private PlanAggregateService planAggregateService;

    public boolean deleteCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode,period,"系数编码和绩效月不能为空");
        if( planAggregateService.isCoefficientUsedByPlanOrFactor(coefficientCode, period)) {
            throw new IllegalArgumentException("系数已被绩效方案或者因子引用，无法删除");
        }
        return coefficientDomainService.deleteCoefficientDOByCodeAndPeriod(coefficientCode, period);
    }

}
