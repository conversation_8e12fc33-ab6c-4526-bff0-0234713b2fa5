package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public  class PerformanceCoefficientItem extends RuntimeDomainBaseEntity {
    /**
     * 方案名称
     */
    private String code;

    /**
     * 系数id
     */
    private String coefficientCode;

    /**
     * 系数名称
     */
    private String coefficientName;

    /**
     * 左边界
     */
    private String leftEndpoint;

    /**
     * 右边界
     */
    private String rightEndpoint;

    /**
     * 系数值
     */
    private String coefficientNum;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
