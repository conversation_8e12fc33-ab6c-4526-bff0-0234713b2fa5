package com.jd.kf.oss.performance.domain.config.domain.dept;

import java.util.Map;
import java.util.Set;

/**
 * 部门路径缓存接口
 * 用于缓存部门路径名称，减少重复的数据库查询
 * 
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface IDeptPathCache {

    /**
     * 从缓存中获取部门路径名称
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 部门路径名称，如果缓存中不存在则返回null
     */
    String getDeptPathName(String tenantCode, String deptId);

    /**
     * 将部门路径名称放入缓存
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @param deptPathName 部门路径名称
     */
    void putDeptPathName(String tenantCode, String deptId, String deptPathName);

    /**
     * 批量将部门路径名称放入缓存
     * 
     * @param tenantCode 租户标识
     * @param deptPathMap 部门ID到路径名称的映射
     */
    void putDeptPathNames(String tenantCode, Map<String, String> deptPathMap);

    /**
     * 检查缓存中是否包含指定的部门路径
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 是否包含
     */
    boolean containsDeptPath(String tenantCode, String deptId);

    /**
     * 从缓存中移除指定的部门路径
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     */
    void removeDeptPath(String tenantCode, String deptId);

    /**
     * 清空指定租户的所有部门路径缓存
     * 
     * @param tenantCode 租户标识
     */
    void clearTenantCache(String tenantCode);

    /**
     * 清空所有缓存
     */
    void clearAll();

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息字符串
     */
    String getCacheStats();

    /**
     * 批量检查哪些部门ID在缓存中不存在
     * 
     * @param tenantCode 租户标识
     * @param deptIds 部门ID集合
     * @return 缓存中不存在的部门ID集合
     */
    Set<String> getMissingDeptIds(String tenantCode, Set<String> deptIds);
}
