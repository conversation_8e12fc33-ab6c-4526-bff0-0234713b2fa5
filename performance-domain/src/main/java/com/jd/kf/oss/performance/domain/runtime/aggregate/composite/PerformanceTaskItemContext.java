package com.jd.kf.oss.performance.domain.runtime.aggregate.composite;

import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceCoefficientDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceFactor;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskItemDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformancePlanDO;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class PerformanceTaskItemContext {

    /**
     * 自任务
     */
    private PerformanceTaskItemDO performanceTaskItemDO;

    /**
     * 方案
     */
    private PerformancePlanDO performancePlanDO;

    /**
     * 业务线
     */
    private PerformanceTargetDO performanceTargetDO;

    /**
     * 用户
     */
    private List<PerformanceUserDO> userDOList;

    /**
     * 用户
     */
    private List<PerformanceUserDO> assistUserDOList;

    /**
     * 抽掉工时
     */
    private Map<String, WaiterHourAdjustment> waiterHourAdjustmentMap = Maps.newHashMap();

    /**
     * 数据
     */
    private Map<String, PerformanceIndexData> indexDataDOMap = Maps.newHashMap();

    /**
     * 指标信息
     */
    private Map<String, PerformanceIndex> performanceIndexMap= Maps.newHashMap();

    /**
     * 系数
     */
    private Map<String, PerformanceCoefficientDO> coefficientDOMap;

    /**
     * 所有因子
     */
    private Map<String, PerformanceFactor> performanceFactorMap;

    /**
     * 目标
     */
    private Map<String, PerformanceTargetDO> targetDOMap;

    public List<FactorDO> calc() {
        try {
            List<FormulaParameter> parameters = Lists.newArrayList();
            FormulaParser parser = new FormulaParser();
            FormulaNode ast = parser.parse(performancePlanDO.getFormula());
            if (Objects.nonNull(ast)) {
                parameters = ast.getAllFactor();
                parameters.stream().map(FormulaParameter::getName).collect(Collectors.toList());
            }
        } catch (Exception e) {
        }

        return Lists.newArrayList();
    }
}
