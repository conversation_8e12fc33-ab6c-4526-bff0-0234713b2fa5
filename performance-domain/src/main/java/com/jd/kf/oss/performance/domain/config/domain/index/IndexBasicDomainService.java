package com.jd.kf.oss.performance.domain.config.domain.index;

import com.jd.dal.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标基础信息领域服务
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class IndexBasicDomainService {

    @Autowired
    private IIndexRepository  indexRepository;

    /**
     * 根据指标编码列表查询指标基础信息
     * @param kpiCds 指标编码列表，不能为空
     * @return 指标基础信息DO对象列表
     */
    public List<IndexBasicDO> queryIndexBasicByKpiCds(List<String> kpiCds) {
        if(CollectionUtils.isEmpty(kpiCds) ) {
            throw new IllegalArgumentException("查询指标基础信息失败，指标编码以及绩效月不能为空");
        }
        return indexRepository.selectIndexBasicByKpiCds(kpiCds);
    }




}
