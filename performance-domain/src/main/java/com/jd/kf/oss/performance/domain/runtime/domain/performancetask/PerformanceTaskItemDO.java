package com.jd.kf.oss.performance.domain.runtime.domain.performancetask;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskStatusEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;

import java.util.Objects;

/**
 * 绩效子任务领域模型
 */
@Data
public class PerformanceTaskItemDO extends RuntimeDomainBaseEntity {
    /**
     * 绩效月
     */
    private String period;

    /**
     * 业务条线ID
     */
    private String businessLineId;

    /**
     * 业务条线名称
     */
    private String businessLineName;

    /**
     * 关联的任务ID
     */
    private Long taskId;

    /**
     * 子任务状态
     */
    private TaskItemStatusEnum status;

    /**
     * 构建任务详情
     * @param tenantCode
     * @param period
     * @param businessLine
     * @param taskId
     * @return
     */
    public static PerformanceTaskItemDO build(String tenantCode, String period, BusinessLineDO businessLine, Long taskId) {
        PerformanceTaskItemDO item = new PerformanceTaskItemDO();
        item.setTenantCode(tenantCode);
        item.setPeriod(period);
        item.setBusinessLineId(businessLine.getBusinessLineId());
        item.setBusinessLineName(businessLine.getName());
        item.setTaskId(taskId);
        item.setStatus(TaskItemStatusEnum.NOT_STARTED);
        return item;
    }

    /**
     * 将状态修改为因子运行
     * @return
     */
    public boolean changeStatusToFactorRunning() {
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskItemStatusEnum.NOT_STARTED, TaskItemStatusEnum.FACTOR_RUNNING);
    }

    /**
     * 将状态修改为因子运行结束
     * @return
     */
    public boolean changeStatusToFactorRunningCompleted() {
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskItemStatusEnum.FACTOR_RUNNING, TaskItemStatusEnum.FACTOR_RUNNING_COMPLETED);
    }

    /**
     * 将状态修改为方案运行开始
     * @return
     */
    public boolean changeStatusToPlanRunning() {
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskItemStatusEnum.FACTOR_RUNNING_COMPLETED, TaskItemStatusEnum.PLAN_RUNNING);
    }

    /**
     * 将状态修改为方案运行开始
     * @return
     */
    public boolean changeStatusToPlanCompleted() {
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskItemStatusEnum.PLAN_RUNNING, TaskItemStatusEnum.COMPLETED);
    }


}
