package com.jd.kf.oss.performance.domain.config.aggregate.configuration.converter;

import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.UserComposite;
import com.jd.kf.oss.performance.domain.config.domain.dept.DeptDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserComposite转换器
 * 负责UserDO和UserComposite之间的转换
 * 
 * <AUTHOR>
 * @date 2025/07/17
 */
public class UserCompositeConverter {

    /**
     * 将UserDO转换为UserComposite
     * 
     * @param userDO 用户领域对象
     * @return 用户组合对象
     */
    public static UserComposite userDOToUserComposite(UserDO userDO) {
        if (userDO == null) {
            return null;
        }

        UserComposite userComposite = new UserComposite();
        
        // 设置UserDO
        userComposite.setUserDO(userDO);
        
        // 创建DeptDO并设置部门信息
        DeptDO deptDO = new DeptDO();
        deptDO.setDeptId(userDO.getDeptId());
        deptDO.setDeptName(userDO.getDeptName());
        deptDO.setTenantCode(userDO.getTenantCode());
        // 注意：deptPathName需要在调用方设置
        
        userComposite.setDeptDO(deptDO);
        
        return userComposite;
    }

    /**
     * 将UserDO列表转换为UserComposite列表
     * 
     * @param userDOList 用户领域对象列表
     * @return 用户组合对象列表
     */
    public static List<UserComposite> userDOListToUserCompositeList(List<UserDO> userDOList) {
        if (CollectionUtils.isEmpty(userDOList)) {
            return Collections.emptyList();
        }

        return userDOList.stream()
                .map(UserCompositeConverter::userDOToUserComposite)
                .collect(Collectors.toList());
    }

    /**
     * 将UserDO分页结果转换为UserComposite分页结果
     * 
     * @param userDOPage 用户领域对象分页结果
     * @return 用户组合对象分页结果
     */
    public static CommonPage<UserComposite> userDOPageToUserCompositePage(CommonPage<UserDO> userDOPage) {
        if (userDOPage == null) {
            return null;
        }

        List<UserComposite> userCompositeList = userDOListToUserCompositeList(userDOPage.getData());
        
        return CommonPage.getCommonPage(
                userDOPage.getPage(),
                userDOPage.getSize(),
                userDOPage.getTotal(),
                userCompositeList
        );
    }

    /**
     * 将UserComposite转换为UserDO
     * 
     * @param userComposite 用户组合对象
     * @return 用户领域对象
     */
    public static UserDO userCompositeToUserDO(UserComposite userComposite) {
        if (userComposite == null || userComposite.getUserDO() == null) {
            return null;
        }

        // 直接返回UserDO，不修改其内容
        return userComposite.getUserDO();
    }

    /**
     * 将UserComposite列表转换为UserDO列表
     * 
     * @param userCompositeList 用户组合对象列表
     * @return 用户领域对象列表
     */
    public static List<UserDO> userCompositeListToUserDOList(List<UserComposite> userCompositeList) {
        if (CollectionUtils.isEmpty(userCompositeList)) {
            return Collections.emptyList();
        }

        return userCompositeList.stream()
                .map(UserCompositeConverter::userCompositeToUserDO)
                .collect(Collectors.toList());
    }
}
