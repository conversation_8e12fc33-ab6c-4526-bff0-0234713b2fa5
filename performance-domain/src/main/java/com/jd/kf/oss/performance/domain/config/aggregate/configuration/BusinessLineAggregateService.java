package com.jd.kf.oss.performance.domain.config.aggregate.configuration;

import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLineComposite;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.converter.BusinessLineCompositeConverter;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.wormhole.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/06/25
 * 绩效组聚合服务
 */
@Service
public class BusinessLineAggregateService {
    @Resource
    private BusinessLineDomainService businessLineDomainService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;

    /**
     * 根据条件分页查询绩效组，并关联绩效方案信息
     *
     * @param tenantCode 租户标识
     * @param editor 编辑人，可选
     * @param name 绩效组名称，可选
     * @param planCode 绩效方案ID，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果，包含绩效方案信息
     */
    public CommonPage<BusinessLineComposite> queryBusinessLineWithPlanInfo(String tenantCode, String editor, String name,
                                                                           String planCode, int pageNum, int pageSize) {
        // 先查询绩效组分页结果
        CommonPage<BusinessLineDO> businessLinePage = businessLineDomainService.queryBusinessLineByTenantAndEditorAndName(
                tenantCode, editor, name, null, pageNum, pageSize);

        // 如果planId为空，直接将DO分页结果转换为Composite分页结果并返回
        if (StringUtils.isBlank(planCode) || businessLinePage == null || businessLinePage.getData().isEmpty()) {
            return BusinessLineCompositeConverter.INSTANCE.pageDO2CompositePage(businessLinePage);
        }

        // 获取当前绩效月
        String currentPeriod = DateUtils.getCurrentPerformancePeriod();

        // 提取绩效组ID列表
        List<String> businessLineIds = businessLinePage.getData().stream()
                .map(BusinessLineDO::getBusinessLineId)
                .collect(Collectors.toList());

        // 查询这些绩效组的绩效目标信息
        List<PerformanceTargetDO> targets = performanceTargetDomainService.queryTargetListByBusinessLineCodeAndPeriod(
                currentPeriod, businessLineIds);

        // 构建绩效组ID到绩效目标的映射
        Map<String, PerformanceTargetDO> businessLineToTargetMap = targets.stream()
                .collect(Collectors.toMap(
                        PerformanceTargetDO::getBusinessLineId,
                        target -> target,
                        (existing, replacement) -> existing
                ));

        // 聚合businessLineDO跟TargetDO
        List<BusinessLineComposite> compositeList = businessLinePage.getData().stream().map(businessLine -> {
            BusinessLineComposite composite = new BusinessLineComposite();
            composite.setBusinessLineDO(businessLine);
            PerformanceTargetDO target = businessLineToTargetMap.get(businessLine.getBusinessLineId());
            if (target != null && StringUtils.isNotBlank(target.getEvaluationPlanCode())) {
                composite.setPlanCode(target.getEvaluationPlanCode());
                composite.setPlanName(target.getEvaluationPlan());
            }
            return composite;
        }).collect(Collectors.toList());

        // 组装分页对象
        CommonPage<BusinessLineComposite> resultPage = new CommonPage<>();
        resultPage.setData(compositeList);
        resultPage.setPage(businessLinePage.getPage());
        resultPage.setSize(businessLinePage.getSize());
        resultPage.setTotal(businessLinePage.getTotal());
        return resultPage;
    }


    /**
     * @param businessLineDO
     * @return boolean
     * 新建绩效组，并创建当前绩效月绩效目标
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBusinessLineAndCreateTarget(BusinessLineDO businessLineDO) {
        if (businessLineDO == null) {
            throw new IllegalArgumentException("绩效组不能为空");
        }
        boolean result = businessLineDO.save();
        if (result) {
            // 创建当前绩效月的绩效目标
            PerformanceTargetDO targetDO = new PerformanceTargetDO();
            targetDO.setTenantCode(businessLineDO.getTenantCode());
            targetDO.setBusinessLineId(businessLineDO.getBusinessLineId());
            targetDO.setPeriod(DateUtils.getCurrentPerformancePeriod());
            targetDO.save();
        }
        return result;

    }
}
