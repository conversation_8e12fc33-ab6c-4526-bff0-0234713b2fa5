package com.jd.kf.oss.performance.domain.config.domain.businessLine;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessLineDO extends DomainBaseEntity {


    /**
     * 条线id
     */
    private String businessLineId;

    /**
     * 条线名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 持久化
     *
     * @return
     */
    public boolean save() {
        IBusinessLineRepository businessLineRepository = SpringUtils.getBean(IBusinessLineRepository.class);
        return businessLineRepository.save(this);
    }


    /**
     * 更新
     *
     * @return boolean
     */
    public boolean update() {
        IBusinessLineRepository businessLineRepository = SpringUtils.getBean(IBusinessLineRepository.class);
        return businessLineRepository.updateBusinessLine(this);
    }
}