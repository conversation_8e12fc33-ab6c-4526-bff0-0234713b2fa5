package com.jd.kf.oss.performance.domain.runtime.domain.performancetask;

import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;

public interface IPerformanceTaskRepository {
    /**
     * 保存任务
     */
    void save(PerformanceTaskDO task);

    /**
     * 查询任务
     */
    PerformanceTaskDO query(String tenantCode, String period);

    /**
     * 取消同周期未开始的任务
     */
    void cancelUnstartedTasks(String tenantCode, String period);

    /**
     * 修改状态
     * @param performanceTaskItemDO
     * @param pre
     * @param after
     * @return
     */
    boolean changeStatus(PerformanceTaskItemDO performanceTaskItemDO, TaskItemStatusEnum pre, TaskItemStatusEnum after);

}
