package com.jd.kf.oss.performance.domain.config.domain.dept;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 部门路径缓存
 * 用于缓存部门路径名称，减少重复的数据库查询
 * 
 * <AUTHOR>
 * @date 2025/07/25
 */
@Slf4j
@Component
public class DeptPathCache {

    /**
     * 部门路径缓存
     * Key: tenantCode:deptId
     * Value: 部门路径名称
     */
    private final Cache<String, String> deptPathCache = CacheBuilder.newBuilder()
            .maximumSize(10000) // 最大缓存10000个部门路径
            .expireAfterWrite(30, TimeUnit.MINUTES) // 30分钟后过期
            .build();

    /**
     * 生成缓存Key
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 缓存Key
     */
    private String buildCacheKey(String tenantCode, String deptId) {
        return tenantCode + ":" + deptId;
    }

    /**
     * 从缓存中获取部门路径名称
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 部门路径名称，如果缓存中不存在则返回null
     */
    public String getDeptPathName(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return null;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        return deptPathCache.getIfPresent(cacheKey);
    }

    /**
     * 将部门路径名称放入缓存
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @param deptPathName 部门路径名称
     */
    public void putDeptPathName(String tenantCode, String deptId, String deptPathName) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        deptPathCache.put(cacheKey, StringUtils.isBlank(deptPathName) ? "" : deptPathName);
    }

    /**
     * 批量将部门路径名称放入缓存
     * 
     * @param tenantCode 租户标识
     * @param deptPathMap 部门ID到路径名称的映射
     */
    public void putDeptPathNames(String tenantCode, Map<String, String> deptPathMap) {
        if (StringUtils.isBlank(tenantCode) || deptPathMap == null || deptPathMap.isEmpty()) {
            return;
        }
        
        for (Map.Entry<String, String> entry : deptPathMap.entrySet()) {
            putDeptPathName(tenantCode, entry.getKey(), entry.getValue());
        }
    }

    /**
     * 检查缓存中是否包含指定的部门路径
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 是否包含
     */
    public boolean containsDeptPath(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return false;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        return deptPathCache.getIfPresent(cacheKey) != null;
    }

    /**
     * 从缓存中移除指定的部门路径
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     */
    public void removeDeptPath(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        deptPathCache.invalidate(cacheKey);
    }

    /**
     * 清空指定租户的所有部门路径缓存
     * 
     * @param tenantCode 租户标识
     */
    public void clearTenantCache(String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            return;
        }
        
        // 遍历所有缓存Key，移除指定租户的缓存
        String prefix = tenantCode + ":";
        deptPathCache.asMap().keySet().removeIf(key -> key.startsWith(prefix));
    }

    /**
     * 清空所有缓存
     */
    public void clearAll() {
        deptPathCache.invalidateAll();
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息字符串
     */
    public String getCacheStats() {
        return String.format("缓存大小: %d, 命中率: %.2f%%, 命中次数: %d, 未命中次数: %d",
                deptPathCache.size(),
                deptPathCache.stats().hitRate() * 100,
                deptPathCache.stats().hitCount(),
                deptPathCache.stats().missCount());
    }

    /**
     * 批量检查哪些部门ID在缓存中不存在
     * 
     * @param tenantCode 租户标识
     * @param deptIds 部门ID集合
     * @return 缓存中不存在的部门ID集合
     */
    public Set<String> getMissingDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || deptIds == null || deptIds.isEmpty()) {
            return deptIds;
        }
        
        Set<String> missingIds = new java.util.HashSet<>();
        for (String deptId : deptIds) {
            if (!containsDeptPath(tenantCode, deptId)) {
                missingIds.add(deptId);
            }
        }
        
        return missingIds;
    }
}
