package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import com.jd.kf.oss.performance.utils.IDUtils;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 系数信息实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PerformanceCoefficientDO extends RuntimeDomainBaseEntity {
    /**
     * 方案名称
     */
    private String code;

    /**
     * 系数名称
     */
    private String name;

    /**
     * 系数类型：分段、月周期、常量
     */
    private CoefficientTemplateTypeEnum type;

    /**
     * 描述
     */
    private String description;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 系数items
     */
    private List<PerformanceCoefficientItem> coefficientItems;

    public String buildKey() {
        String key = "coefficient_tenantCode:%s_period:%s_code:%s";
        return String.format(key, getTenantCode(), period, code);
    }

    public static String buildKey(String tenantCode, String period, String code) {
        String key = "coefficient_tenantCode:%s_period:%s_code:%s";
        return String.format(key, tenantCode, period, code);
    }
}

