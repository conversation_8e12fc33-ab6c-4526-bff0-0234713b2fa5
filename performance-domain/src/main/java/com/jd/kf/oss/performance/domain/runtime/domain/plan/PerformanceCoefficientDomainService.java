package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 绩效系数
 */
@Service
public class PerformanceCoefficientDomainService {

    @Autowired
    private IPerformanceCoefficientRepository coefficientRepository;

    /**
     * 查询所有系数
     * @param tenantCode
     * @param period
     * @return
     */
    public List<PerformanceCoefficientDO> loadAll(String tenantCode, String period) {
        return coefficientRepository.queryAllCoefficientDO(tenantCode, period);
    }
}
