package com.jd.kf.oss.performance.domain.runtime.domain.performancetask;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskStatusEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 绩效任务领域模型
 */
@Data
public class PerformanceTaskDO extends RuntimeDomainBaseEntity {
    /**
     * 绩效周期(格式:yyyyMM)
     */
    private String period;

    /**
     * 任务状态
     */
    private TaskStatusEnum status;

    /**
     * 子任务项列表
     */
    private List<PerformanceTaskItemDO> items;

    /**
     * 创建绩效任务
     */
    public PerformanceTaskDO create(String tenantCode, String period,
                                           List<BusinessLineDO> businessLines) {
        IPerformanceTaskRepository repository = SpringUtils.getBean(IPerformanceTaskRepository.class);
        // 取消同周期未开始的任务
        repository.cancelUnstartedTasks(tenantCode, period);

        PerformanceTaskDO task = new PerformanceTaskDO();
        task.setTenantCode(tenantCode);
        task.setPeriod(period);
        task.setStatus(TaskStatusEnum.NOT_STARTED);
        task.setItems(createTaskItems(tenantCode, period, businessLines, task.getId()));
        repository.save(task);
        return task;
    }

    private static List<PerformanceTaskItemDO> createTaskItems(String tenantCode,
                                                               String period, List<BusinessLineDO> businessLines, Long taskId) {
        return businessLines.stream()
                .map(b -> PerformanceTaskItemDO.build(tenantCode, period, b, taskId))
                .collect(Collectors.toList());
    }

    public PerformanceTaskDO load(String tenantCode, String period) {
        IPerformanceTaskRepository repository = SpringUtils.getBean(IPerformanceTaskRepository.class);
        return repository.query(tenantCode, period);
    }
}