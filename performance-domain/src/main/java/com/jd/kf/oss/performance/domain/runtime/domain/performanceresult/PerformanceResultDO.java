package com.jd.kf.oss.performance.domain.runtime.domain.performanceresult;

import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import lombok.Data;

import java.util.Map;

@Data
public class PerformanceResultDO extends RuntimeDomainBaseEntity {

    /**
     * 绩效条线
     */
    private String businessLineId;

    /**
     * 绩效条线名称
     */
    private String businessLineName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 类型：首次，申诉
     */
    private String type;

    /**
     * 绩效结果
     */
    private String result;

    /**
     * 详情
     */
    private String detail;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 因子结果
     */
    private Map<String, PerformanceFactorResultDO> factorResultMap = Maps.newHashMap();
    /**
     * 状态
     */
    private String status;
}
