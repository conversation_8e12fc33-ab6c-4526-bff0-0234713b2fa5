package com.jd.kf.oss.performance.domain.config.aggregate.configuration.converter;

import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TargetConverter {
    TargetConverter INSTANCE = Mappers.getMapper(TargetConverter.class);

    TargetComposite DO2Target(PerformanceTargetDO targetDO);
    PerformanceTargetDO targetComposite2DO(TargetComposite targetComposite) ;
}
