package com.jd.kf.oss.performance.domain.config.domain.user;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息领域对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDO extends DomainBaseEntity {

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 条线名称
     */
    private String businessLineName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;



    /**
     * 入职时间
     */
    private String entryDate;

    /**
     * 主管erp
     */
    private String managerErp;

    /**
     * 人资离职时间
     */
    private LocalDateTime quitTime;

    /**
     * 人员erp
     */
    private String erp;

    /**
     * 管理员/员工
     */
    private String type;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 时间戳
     */
    private LocalDateTime ts;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 绩效方案名称（从PerformanceTargetPO关联获取）
     */
    private String evaluationPlan;
}
