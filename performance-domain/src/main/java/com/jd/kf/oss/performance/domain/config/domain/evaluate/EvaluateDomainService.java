package com.jd.kf.oss.performance.domain.config.domain.evaluate;

import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 领域服务：上级评价
 * 提供上级评价相关的业务逻辑
 */
@Service
public class EvaluateDomainService {

    @Autowired
    private IEvaluateRepository evaluateRepository;

    /**
     * 分页查询上级评价数据（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令对象，包含查询条件和分页参数
     * @return 分页结果
     */
    /*
    public CommonPage<EvaluateDO> pageEvaluate(PageCommand<EvaluateDO> pageCommand) {
        return evaluateRepository.pageEvaluate(pageCommand);
    }
    */

    /**
     * 根据条件分页查询上级评价数据
     * @param period 绩效月
     * @param managerErp 主管ERP，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    public CommonPage<EvaluateDO> queryEvaluateByConditions(String period, String managerErp,
                                                            int pageNum, int pageSize) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        return evaluateRepository.queryEvaluateByConditions(period, managerErp, pageNum, pageSize);
    }

    /**
     * 批量删除上级评价
     * @param evaluateIds 待删除评价ID列表
     * @param period 绩效月
     */
    public void deleteEvaluate(List<Long> evaluateIds, String period) {
        evaluateRepository.deleteEvaluate(evaluateIds, period);
    }

    /**
     * 根据条件查询评价数据列表（用于导出）
     * @param period 绩效月
     * @param managerErp 主管ERP
     * @return 评价数据列表
     */
    public List<EvaluateDO> queryEvaluateList(String period, String managerErp) {
        return evaluateRepository.queryEvaluateList(period, managerErp);
    }

    /**
     * 批量保存评价数据（用于导入）
     * @param evaluateList 评价数据列表
     * @return 保存成功的数量
     */
    public int batchSaveEvaluate(List<EvaluateDO> evaluateList) {
        return evaluateRepository.batchSaveEvaluate(evaluateList);
    }

}