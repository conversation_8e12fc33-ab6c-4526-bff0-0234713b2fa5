package com.jd.kf.oss.performance.domain.config.domain.formula;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 公式节点
 */
@Data
public class FormulaNode {
    /**
     * 节点名称
     */
    public String name;

    /**
     * 公式参数
     */
    public List<FormulaParameter> params = new ArrayList<>();

    /**
     * 函数
     */
    public List<FormulaNode> functions = new ArrayList<>();

    /**
     * 原始信息
     */
    public String originText;

    /**
     * 优先级
     */
    public int priority;

    /**
     * 获取所有指标
     * @return
     */
    public List<FormulaParameter> getAllIndex() {
        List<FormulaParameter> results = Lists.newArrayList();
        getParamsByType(this, results, "index");
        return results;
    }

    /**
     * 获取所有系数
     * @return
     */
    public List<FormulaParameter> getAllCoefficient() {
        List<FormulaParameter> results = Lists.newArrayList();
        getParamsByType(this, results, "coefficient");
        return results;
    }

    /**
     * 获取所有系数
     * @return
     */
    public List<FormulaParameter> getAllFactor() {
        List<FormulaParameter> results = Lists.newArrayList();
        getParamsByType(this, results, "factor");
        return results;
    }

    private void getParamsByType(FormulaNode formulaNode, List<FormulaParameter> results, String type) {
        if (Objects.isNull(formulaNode)) {
            return;
        }
        if (!CollectionUtils.isEmpty(formulaNode.params)) {
            List<FormulaParameter> parameters = formulaNode.params.stream().filter(a -> StringUtils.equalsAnyIgnoreCase(a.getType(), type)).collect(Collectors.toList());
            results.addAll(parameters);
        }
        formulaNode.functions.forEach(a -> getParamsByType(a, results, type));
    }
}