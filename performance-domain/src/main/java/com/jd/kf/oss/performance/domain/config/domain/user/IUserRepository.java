package com.jd.kf.oss.performance.domain.config.domain.user;

import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息仓储接口
 */
public interface IUserRepository {

    /**
     * 根据条件分页查询用户信息（涉及多表关联查询）
     *
     * @param tenantCode       租户标识
     * @param period           绩效月
     * @param managerErp       主管erp
     * @param deptId           部门ID
     * @param planCode   绩效方案Code
     * @param erp
     * @param pageNum          页码
     * @param pageSize         页面大小
     * @return 分页结果
     */
    CommonPage<UserDO> queryUserInfoByConditions(String tenantCode, String period, String managerErp,
                                                 String deptId, String businessLineId, String planCode,
                                                 String erp, String name, int pageNum, int pageSize);

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    List<UserDO> queryAllUserByPeriod(String period);

    /**
     * 从WFC表中查询所有有效用户数据（关联wfc_user、wfc_user_ext、wfc_dept）
     * @return 用户列表
     */
    List<UserDO> queryAllWfcUserData();

    /**
     * 批量保存用户数据
     * @param userList 用户列表
     * @return 保存成功的数量
     */
    int saveBatchUsers(List<UserDO> userList);

    /**
     * @param erps
     * @param currentPerformancePeriod
     * @return {@link List }<{@link UserDO }>
     *     根据erp
     */
    List<UserDO> queryUsersByErp(List<String> erps, String currentPerformancePeriod);

    /**
     * @param users
     * @return 更新成功的数量
     *  批量更新用户数据
     */
    int updateBatchUsers(List<UserDO> users);
}
