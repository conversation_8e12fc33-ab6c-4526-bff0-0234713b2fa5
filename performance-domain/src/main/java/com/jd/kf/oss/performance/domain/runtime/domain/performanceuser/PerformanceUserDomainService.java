package com.jd.kf.oss.performance.domain.runtime.domain.performanceuser;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PerformanceUserDomainService {
    @Resource
    private IPerformanceUserRepository performanceUserRepository;

    /**
     * 使用业务条线和绩效月查询用户
     * @param businessLineId
     * @param period
     * @return
     */
    public List<PerformanceUserDO> queryUserByBusinessLineId(String businessLineId, String period) {
        if (StringUtils.isAnyEmpty(businessLineId, period)) {
            return Lists.newArrayList();
        }
        return performanceUserRepository.queryAllUserByPeriodAndBusinessLineId(businessLineId, period);
    }

    /**
     * 使用业务条线和绩效月查询用户
     * @param businessLineId
     * @param period
     * @return
     */
    public List<PerformanceUserDO> queryUserByBusinessLineIdAndErps(String businessLineId, String period, List<String> erps) {
        if (StringUtils.isAnyEmpty(businessLineId, period)) {
            return Lists.newArrayList();
        }
        return performanceUserRepository.queryUserByBusinessLineIdAndErps(businessLineId, period, erps);
    }
}
