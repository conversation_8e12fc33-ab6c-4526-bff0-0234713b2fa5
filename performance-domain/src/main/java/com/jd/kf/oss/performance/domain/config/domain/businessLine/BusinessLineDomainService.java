package com.jd.kf.oss.performance.domain.config.domain.businessLine;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.wormhole.util.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/01
 * 绩效组领域服务
 */
@Service
public class BusinessLineDomainService {
    @Resource
    private IBusinessLineRepository businessLineRepository;

    /**
     * 根据租户标识、编辑人和名称分页查询绩效组
     *
     * @param tenantCode      租户标识
     * @param editor          编辑人，可选
     * @param name            绩效组名称，可选
     * @param businessLineIds
     * @param pageNum         页码
     * @param pageSize        页面大小
     * @return 包含分页结果的通用分页对象
     */
    public CommonPage<BusinessLineDO> queryBusinessLineByTenantAndEditorAndName(String tenantCode, String editor, String name,
                                                                                List<String> businessLineIds, int pageNum, int pageSize) {
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        return businessLineRepository.queryBusinessLineByTenantAndEditorAndName(tenantCode, editor, name, businessLineIds, pageNum, pageSize);
    }

    public BusinessLineDO queryBusinessLineByLineId(String businessLineId) {
        if (StringUtils.isBlank(businessLineId)) {
            throw new IllegalArgumentException("Business Line ID cannot be null or empty");
        }
        return businessLineRepository.queryBusinessLineByLineId(businessLineId);
    }
    /**
     * 校验绩效组是否存在
     */
    public void checkBusinessLineExits(String businessLineId,String businessLineName) {
        if (StringUtils.isBlank(businessLineId) || StringUtils.isBlank(businessLineName)) {
            throw new IllegalArgumentException("绩效组ID以及绩效组名称不能为空");
        }
        BusinessLineDO businessLineDO = queryBusinessLineByLineId(businessLineId);
        Optional.ofNullable(businessLineDO).filter(lineDO-> Objects.equals(lineDO.getName(),businessLineName) ).orElseThrow(() -> new BizException("绩效组不存在，请检查绩效组ID"));
    }




    /**
     * 查询所有绩效组
     * @return 包含所有绩效组
     */
    public List<BusinessLineDO> queryAllBusinessLine() {
        return businessLineRepository.queryAllBusinessLine();
    }

    /**
     * 查询所有带有租户编码的业务线信息
     * @return 包含所有业务线信息的列表，每个业务线对象都包含租户编码
     */
    public List<BusinessLineDO> queryAllBusinessLineWithTenantCode() {
        return businessLineRepository.queryAllBusinessLineWithTenantCode();
    }


    /**
     * 根据业务线ID列表查询业务线信息
     * @param businessLineIds 业务线ID列表，不能为空
     * @return 查询到的业务线信息列表
     */
    public List<BusinessLineDO> getBusinessLineByLineIds(List<String> businessLineIds) {
        if(CollectionUtils.isEmpty(businessLineIds)) {
            throw new IllegalArgumentException("绩效组ID列表不能为空");
        }
        return businessLineRepository.selectBusinessLineByLineIds(businessLineIds);
    }

    /**
     * 检查所有业务线ID是否都存在，若存在不存在的业务线ID则抛出异常
     * @param businessLineIds 需要检查的业务线ID列表
     */
    public List<BusinessLineDO> checkAllBusinessLineExits(List<String> businessLineIds) {
        if(CollectionUtils.isEmpty(businessLineIds)) {
            return new ArrayList<>(0);
        }
        List<BusinessLineDO> lineDOs = getBusinessLineByLineIds(businessLineIds);
        Set<String> existingLineIds = lineDOs.stream()
                .map(BusinessLineDO::getBusinessLineId)
                .collect(Collectors.toSet());
        Set<String> toCheckLineIds = new HashSet<>(businessLineIds);
        if(!existingLineIds.containsAll(toCheckLineIds)) {
            Set<String> notFoundLineIds = new HashSet<>(toCheckLineIds);
            notFoundLineIds.removeAll(existingLineIds);
            throw new BizException("以下绩效组ID不存在: " + String.join(", ", notFoundLineIds));
        }
        return lineDOs;
    }

}
