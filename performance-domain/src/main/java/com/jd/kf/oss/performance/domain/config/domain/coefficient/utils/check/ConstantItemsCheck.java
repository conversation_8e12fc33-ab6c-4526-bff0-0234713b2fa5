package com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check;

import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientItem;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.NumberExtUtils;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class ConstantItemsCheck extends AbsCoefficientItemsCheck {
    @Override
    public void check(CoefficientDO coefficientDO) {
        List<CoefficientItem> coefficientItems = coefficientDO.getCoefficientItems();
        if (coefficientItems == null || coefficientItems.size() != 1) {
            throw new IllegalArgumentException("系数模板类型为常量时，系数条目必须为1");
        }
        CoefficientItem item = coefficientItems.get(0);
        checkItem(item);

    }
    /**
     * 检查单个系数项的有效性
     * @param item 待检查的系数项
     * @throws IllegalArgumentException 如果系数值不是数字或小于等于0
     */
    public void checkItem(CoefficientItem item) {
       if(!(NumberExtUtils.isNumber(item.getCoefficientNum())) || Double.parseDouble(item.getCoefficientNum())<= 0){
           throw new IllegalArgumentException("常量模板系数值必须为数字且大于0");
       }
       //避免插入脏数据
       item.setLeftEndpoint(null);
       item.setRightEndpoint(null);
    }

    @Override
    public CoefficientTemplateTypeEnum getTemplateType() {
        return CoefficientTemplateTypeEnum.CONSTANT;
    }
}

