package com.jd.kf.oss.performance.domain.config.domain.user;

import com.jd.global.utils.CollectionUtils;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户领域服务
 */
@Slf4j
@Service
public class UserDomainService {

    @Resource
    private IUserRepository userRepository;

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    public List<UserDO> queryAllUserByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        return userRepository.queryAllUserByPeriod(period);
    }

    /**
     * 从WFC表中查询所有有效用户数据
     * @return 用户列表
     */
    public List<UserDO> queryAllWfcUserData() {
        return userRepository.queryAllWfcUserData();
    }

    /**
     * 批量保存用户数据
     * @param userList 用户列表
     * @return 保存成功的数量
     */
    public int saveBatchUsers(List<UserDO> userList) {
        CheckUtil.notEmpty(userList, "用户列表不能为空");
        return userRepository.saveBatchUsers(userList);
    }

    public void updateBatchUsers(List<UserDO> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        userRepository.updateBatchUsers(users);
    }

    /**
     * 根据条件分页查询用户信息（仅查询用户基础信息，不包含部门路径）
     * 涉及UserPO、PerformanceTargetPO等多个实体的关联查询
     * 注意：此方法不会设置部门路径信息，如需完整信息请使用UserAggregateService.queryUserPageInfo
     *
     * @param tenantCode       租户标识
     * @param period           绩效月
     * @param managerErp       主管erp
     * @param deptId           部门ID
     * @param businessLineId   业务线ID
     * @param planCode   绩效方案Code
     * @param erp              用户erp
     * @param name             用户姓名
     * @param pageNum          页码
     * @param pageSize         页面大小
     * @return 分页结果（不包含部门路径信息）
     */
    public CommonPage<UserDO> queryUserInfoByConditions(String tenantCode, String period, String managerErp,
                                                        String deptId, String businessLineId, String planCode,
                                                        String erp, String name, int pageNum, int pageSize) {
        // 参数校验
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
        CheckUtil.notBlank(tenantCode, "租户标识不能为空");

        return userRepository.queryUserInfoByConditions(tenantCode, period, managerErp,
                deptId, businessLineId, planCode,
                erp, name, pageNum, pageSize);
    }


    public List<UserDO> getUsersByErp(List<String> erps, String currentPerformancePeriod) {
        if (CollectionUtils.isEmpty(erps)) {
            return Collections.emptyList();
        }
        return userRepository.queryUsersByErp(erps, currentPerformancePeriod);
    }
}
