package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;

/**
 * 计划仓储接口
 * 提供计划相关的数据访问方法
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IPerformancePlanRepository {
    /**
     * 根据编码查询计划信息
     * @param tenantCode 租户编码
     * @param period 期间
     * @param code 计划编码
     * @return 计划DO对象
     */
    PerformancePlanDO queryByCode(String tenantCode, String period, String code);
}
