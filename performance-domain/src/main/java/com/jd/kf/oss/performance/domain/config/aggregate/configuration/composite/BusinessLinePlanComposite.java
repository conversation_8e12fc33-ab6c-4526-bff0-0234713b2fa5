package com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite;

import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@Data
public class BusinessLinePlanComposite {

    /**
     * 绩效目标
     */
    private PerformanceTargetDO targetDO;
    /**
     * 方案
     */
    private PlanDO planDO;

    /**
     * 因子codes
     */
    List<String> factorCodes = Lists.newArrayList();

    /**
     * 因子
     */
    private List<FactorDO> factorDOS = Lists.newArrayList();

    /**
     * 指标模版名
     */
    List<String> indexTemplateNames = Lists.newArrayList();

    /**
     * 指标
     */
    private List<IndexDO> indexDOS = Lists.newArrayList();

    /**
     * 系数Code
     */
    List<String> coefficientCodes = Lists.newArrayList();
    /**
     * 系数
     */
    private List<CoefficientDO> coefficientDOS = Lists.newArrayList();
}
