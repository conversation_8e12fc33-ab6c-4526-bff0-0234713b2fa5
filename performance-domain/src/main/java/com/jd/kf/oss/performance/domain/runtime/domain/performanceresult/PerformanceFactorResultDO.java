package com.jd.kf.oss.performance.domain.runtime.domain.performanceresult;

import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import lombok.Data;

import java.util.List;

@Data
public class PerformanceFactorResultDO extends RuntimeDomainBaseEntity {

    /**
     * 绩效条线
     */
    private String businessLineId;

    /**
     * 绩效条线名称
     */
    private String businessLineName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 类型：首次，申诉
     */
    private String type;

    /**
     * 绩效因子
     */
    private String factorId;

    /**
     * 详情
     */
    private String detail;

    /**
     * 绩效结果
     */
    private String result;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 构建key
     * @return
     */
    public String buildKey() {
        String key = "factorResult_tenantCode:%s_period:%s_businessName:%s_factorId:%s_erp:%s";
        return String.format(key, getTenantCode(), period, businessLineName, factorId,erp);
    }

    public static String buildKey(String tenantCode, String period, String businessLineName, String factorId, String erp) {
        String key = "factorResult_tenantCode:%s_period:%s_businessName:%s_factorId:%s_erp:%s";
        return String.format(key, tenantCode, period, businessLineName, factorId,erp);
    }
}
