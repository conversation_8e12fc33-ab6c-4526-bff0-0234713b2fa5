package com.jd.kf.oss.performance.domain.config.domain.appealmodify;

import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * 归属数据修改仓储接口
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface IAppealModifyRepository {

    /**
     * 分页查询归属数据修改（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令
     * @return 分页结果
     */
    // CommonPage<AppealModifyDO> pageAppealModify(PageCommand<AppealModifyDO> pageCommand);

    /**
     * 根据条件分页查询归属数据修改
     *
     * @param period     绩效月
     * @param kpiCd      指标code，可选
     * @param kpiName    指标名称，可选
     * @param skillId    技能ID，可选
     * @param ticketId   单号，可选
     * @param pageNum    页码
     * @param pageSize   页面大小
     * @return 分页结果
     */
    CommonPage<AppealModifyDO> queryAppealModifyByConditions(String period, String kpiName, String skillId, String ticketId,
                                                             int pageNum, int pageSize);




    /**
     * 批量删除归属数据修改（软删除）
     * @param ids 待删除ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 根据条件查询归属数据修改列表（用于导出）
     *
     * @param tenantCode 租户标识
     * @param period     绩效月
     * @param erp        客服ERP，可选
     * @return 归属数据修改列表
     */
    List<AppealModifyDO> queryAppealModifyList(String tenantCode, String period, String erp);

    /**
     * 根据条件查询归属数据修改列表（用于导出，支持更多查询条件）
     *
     * @param period     绩效月
     * @param kpiName    指标名称，可选
     * @param skillId    技能ID，可选
     * @param ticketId   单号，可选
     * @return 归属数据修改列表
     */
    List<AppealModifyDO> queryAppealModifyListByConditions(String period, String kpiName, String skillId, String ticketId);

    /**
     * 批量保存归属数据修改（用于导入）
     * @param appealModifyList 归属数据修改列表
     * @return 保存成功的数量
     */
    int saveOrUpdateBatch(List<AppealModifyDO> appealModifyList);
}
