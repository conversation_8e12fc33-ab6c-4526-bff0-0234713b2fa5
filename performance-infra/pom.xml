<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.kf-oss</groupId>
        <artifactId>performance</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>performance-infra</artifactId>
    <name>performance-infra</name>
    <description>infra 模块，用于外部服务访问防腐层</description>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>actuator-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>cache-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.kf-oss</groupId>
            <artifactId>performance-domain</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.kf-oss</groupId>
            <artifactId>performance-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>context-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>dal-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>ducc-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>http-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jim-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jmq-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>jsf-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>log-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>registry-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>schedule-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.framework</groupId>
            <artifactId>thread-dong-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.4.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>


        <!-- JUnit Jupiter API -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>

        <!-- JUnit Jupiter Engine -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


</project>
