package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.EvaluatePO;
import com.jd.kf.oss.performance.infra.mybatis.service.IEvaluatePOService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EvaluatePOServiceImpl测试类
 * 测试批量保存或更新功能
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EvaluatePOServiceImplTest {

    @Resource
    private IEvaluatePOService evaluatePOService;

    @Test
    public void testSaveOrUpdateBatch_Insert() {
        // 准备测试数据 - 新增场景
        List<EvaluatePO> evaluateList = new ArrayList<>();
        
        EvaluatePO evaluate1 = new EvaluatePO();
        evaluate1.setPeriod("2025-07");
        evaluate1.setErp("test.erp1");
        evaluate1.setScore("85.5");
        evaluate1.setTenantCode("tenant001");
        evaluate1.setYn(YnEnum.VALID.getValue());
        evaluateList.add(evaluate1);
        
        EvaluatePO evaluate2 = new EvaluatePO();
        evaluate2.setPeriod("2025-07");
        evaluate2.setErp("test.erp2");
        evaluate2.setScore("90.0");
        evaluate2.setTenantCode("tenant001");
        evaluate2.setYn(YnEnum.VALID.getValue());
        evaluateList.add(evaluate2);

        // 执行批量保存
        boolean result = evaluatePOService.saveOrUpdateBatch(evaluateList);
        
        // 验证结果
        assertTrue(result, "批量保存应该成功");
        
        // 验证数据是否正确保存
        List<EvaluatePO> savedList = evaluatePOService.list();
        assertEquals(2, savedList.size(), "应该保存了2条记录");
    }

    @Test
    public void testSaveOrUpdateBatch_Update() {
        // 先插入一条记录
        EvaluatePO existingEvaluate = new EvaluatePO();
        existingEvaluate.setPeriod("2025-07");
        existingEvaluate.setErp("test.erp1");
        existingEvaluate.setScore("80.0");
        existingEvaluate.setTenantCode("tenant001");
        existingEvaluate.setYn(YnEnum.VALID.getValue());
        evaluatePOService.save(existingEvaluate);
        
        // 准备更新数据
        List<EvaluatePO> evaluateList = new ArrayList<>();
        
        EvaluatePO updateEvaluate = new EvaluatePO();
        updateEvaluate.setPeriod("2025-07");
        updateEvaluate.setErp("test.erp1");
        updateEvaluate.setScore("95.0"); // 更新分数
        updateEvaluate.setTenantCode("tenant001");
        updateEvaluate.setYn(YnEnum.VALID.getValue());
        evaluateList.add(updateEvaluate);

        // 执行批量保存或更新
        boolean result = evaluatePOService.saveOrUpdateBatch(evaluateList);
        
        // 验证结果
        assertTrue(result, "批量更新应该成功");
        
        // 验证数据是否正确更新
        List<EvaluatePO> updatedList = evaluatePOService.list();
        assertEquals(1, updatedList.size(), "应该只有1条记录");
        assertEquals("95.0", updatedList.get(0).getScore(), "分数应该被更新为95.0");
    }

    @Test
    public void testSaveOrUpdateBatch_Mixed() {
        // 先插入一条记录
        EvaluatePO existingEvaluate = new EvaluatePO();
        existingEvaluate.setPeriod("2025-07");
        existingEvaluate.setErp("existing.erp");
        existingEvaluate.setScore("80.0");
        existingEvaluate.setTenantCode("tenant001");
        existingEvaluate.setYn(YnEnum.VALID.getValue());
        evaluatePOService.save(existingEvaluate);
        
        // 准备混合数据（既有更新也有新增）
        List<EvaluatePO> evaluateList = new ArrayList<>();
        
        // 更新已存在的记录
        EvaluatePO updateEvaluate = new EvaluatePO();
        updateEvaluate.setPeriod("2025-07");
        updateEvaluate.setErp("existing.erp");
        updateEvaluate.setScore("95.0");
        updateEvaluate.setTenantCode("tenant001");
        updateEvaluate.setYn(YnEnum.VALID.getValue());
        evaluateList.add(updateEvaluate);
        
        // 新增记录
        EvaluatePO newEvaluate = new EvaluatePO();
        newEvaluate.setPeriod("2025-07");
        newEvaluate.setErp("new.erp");
        newEvaluate.setScore("88.0");
        newEvaluate.setTenantCode("tenant001");
        newEvaluate.setYn(YnEnum.VALID.getValue());
        evaluateList.add(newEvaluate);

        // 执行批量保存或更新
        boolean result = evaluatePOService.saveOrUpdateBatch(evaluateList);
        
        // 验证结果
        assertTrue(result, "批量保存或更新应该成功");
        
        // 验证数据
        List<EvaluatePO> resultList = evaluatePOService.list();
        assertEquals(2, resultList.size(), "应该有2条记录");
        
        // 验证更新的记录
        EvaluatePO updatedRecord = resultList.stream()
                .filter(e -> "existing.erp".equals(e.getErp()))
                .findFirst()
                .orElse(null);
        assertNotNull(updatedRecord, "应该找到更新的记录");
        assertEquals("95.0", updatedRecord.getScore(), "分数应该被更新");
        
        // 验证新增的记录
        EvaluatePO newRecord = resultList.stream()
                .filter(e -> "new.erp".equals(e.getErp()))
                .findFirst()
                .orElse(null);
        assertNotNull(newRecord, "应该找到新增的记录");
        assertEquals("88.0", newRecord.getScore(), "新记录分数应该正确");
    }

    @Test
    public void testSaveOrUpdateBatch_EmptyList() {
        // 测试空列表
        List<EvaluatePO> emptyList = new ArrayList<>();
        boolean result = evaluatePOService.saveOrUpdateBatch(emptyList);
        
        assertTrue(result, "空列表应该返回成功");
    }

    @Test
    public void testSaveOrUpdateBatch_NullList() {
        // 测试null列表
        boolean result = evaluatePOService.saveOrUpdateBatch(null);
        
        assertTrue(result, "null列表应该返回成功");
    }
}
