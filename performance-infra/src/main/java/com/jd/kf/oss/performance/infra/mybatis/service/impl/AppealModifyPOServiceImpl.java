package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.AppealModifyPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.AppealModifyPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IAppealModifyPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 调整单号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class AppealModifyPOServiceImpl extends ServiceImpl<AppealModifyPOMapper, AppealModifyPO> implements IAppealModifyPOService {
    /**
     * 批量保存或更新申诉修改数据
     * 根据erp+ticketId+kpiName+period字段为唯一键来判断是insert还是update
     *
     * @param appealModifyPOs 申诉修改数据集合
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(Collection<AppealModifyPO> appealModifyPOs) {
        if (CollectionUtils.isEmpty(appealModifyPOs)) {
            log.info("AppealModifyPOServiceImpl saveOrUpdateBatch appealModifyPOs is empty");
            return true;
        }

        try {
            List<AppealModifyPO> appealModifyList = new ArrayList<>(appealModifyPOs);

            // 分批处理，避免SQL过长
            int batchSize = 500;
            for (int i = 0; i < appealModifyList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, appealModifyList.size());
                List<AppealModifyPO> batch = appealModifyList.subList(i, endIndex);
                processBatch(batch);
            }

            return true;
        } catch (Exception e) {
            log.error("AppealModifyPOServiceImpl saveOrUpdateBatch error: {}", e.getMessage(), e);
            throw new RuntimeException("批量保存或更新申诉修改数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个批次的数据
     *
     * @param batch 批次数据
     */
    private void processBatch(List<AppealModifyPO> batch) {
        // 查询数据库中已存在的记录
        Map<String, AppealModifyPO> existingRecords = queryExistingRecords(batch);

        // 分离新增和更新的数据
        List<AppealModifyPO> toInsert = new ArrayList<>();
        List<AppealModifyPO> toUpdate = new ArrayList<>();

        for (AppealModifyPO appealModify : batch) {
            String uniqueKey = buildUniqueKey(appealModify);
            AppealModifyPO existing = existingRecords.get(uniqueKey);

            if (existing != null) {
                // 存在记录，需要更新
                appealModify.setId(existing.getId());
                toUpdate.add(appealModify);
            } else {
                // 不存在记录，需要新增
                appealModify.setId(null);
                toInsert.add(appealModify);
            }
        }

        // 执行批量新增
        if (!CollectionUtils.isEmpty(toInsert)) {
            saveBatch(toInsert);
            log.info("批量新增申诉修改数据 {} 条", toInsert.size());
        }

        // 执行批量更新
        if (!CollectionUtils.isEmpty(toUpdate)) {
            updateBatchById(toUpdate);
            log.info("批量更新申诉修改数据 {} 条", toUpdate.size());
        }
    }

    /**
     * 构建唯一键：erp+ticketId+kpiName+period
     *
     * @param appealModify 申诉修改对象
     * @return 唯一键字符串
     */
    private String buildUniqueKey(AppealModifyPO appealModify) {
        return String.format("%s_%s_%s",
                appealModify.getTicketId(),
                appealModify.getKpiName(),
                appealModify.getPeriod());
    }

    /**
     * 查询数据库中已存在的记录
     *
     * @param batch 批次数据
     * @return 已存在记录的Map，key为唯一键，value为记录对象
     */
    private Map<String, AppealModifyPO> queryExistingRecords(List<AppealModifyPO> batch) {
        // 提取所有的erp、ticketId、kpiName、period组合
        Set<String> erps = batch.stream()
                .map(AppealModifyPO::getErp)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> ticketIds = batch.stream()
                .map(AppealModifyPO::getTicketId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> kpiNames = batch.stream()
                .map(AppealModifyPO::getKpiName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> periods = batch.stream()
                .map(AppealModifyPO::getPeriod)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (erps.isEmpty() || ticketIds.isEmpty() || kpiNames.isEmpty() || periods.isEmpty()) {
            return new HashMap<>();
        }

        // 查询数据库中已存在的记录
        LambdaQueryWrapper<AppealModifyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppealModifyPO::getTicketId, ticketIds)
                   .in(AppealModifyPO::getKpiName, kpiNames)
                   .in(AppealModifyPO::getPeriod, periods)
                   .eq(AppealModifyPO::getYn, YnEnum.VALID.getValue());

        List<AppealModifyPO> existingList = list(queryWrapper);

        // 转换为Map，便于快速查找
        // 如果有重复key，保留第一个
        return existingList.stream()
                .collect(Collectors.toMap(
                        this::buildUniqueKey,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }
}
