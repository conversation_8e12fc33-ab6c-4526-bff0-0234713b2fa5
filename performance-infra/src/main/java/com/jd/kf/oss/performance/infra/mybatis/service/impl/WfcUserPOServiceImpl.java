package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.WfcUserPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcUserPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class WfcUserPOServiceImpl extends ServiceImpl<WfcUserPOMapper, WfcUserPO> implements IWfcUserPOService {

    @Resource
    private WfcUserPOMapper wfcUserPOMapper;

    @Override
    public List<WfcUserPO> getWfcUserListByErps(List<String> erpList) {
        if (CollectionUtils.isEmpty(erpList)) {
            return Collections.emptyList();
        }

        String tenantCode = UserContextHolder.getTenantCode();

        LambdaQueryWrapper<WfcUserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WfcUserPO::getTenantCode, tenantCode)
                .in(WfcUserPO::getErp, erpList)
                .eq(WfcUserPO::getYn, true);

        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpsertUser(List<WfcUserPO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            log.info("WfcUserPOServiceImpl batchUpsertUser userList is empty");
            return;
        }
        
        try {
            saveOrUpdateBatch(userList);
        } catch (Exception e) {
            log.error("WfcUserPOServiceImpl batchUpsertUser error: {}", e.getMessage(), e);
            throw new RuntimeException("批量保存用户信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRemoveUserById(List<WfcUserPO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            log.info("WfcUserPOServiceImpl batchRemoveUserById userList is empty");
            return;
        }

        try {
            List<Long> idList = userList.stream()
                    .map(WfcUserPO::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            wfcUserPOMapper.batchDeleteByIdList(idList);
        } catch (Exception e) {
            log.error("WfcUserPOServiceImpl batchRemoveUserById error: {}", e.getMessage(), e);
            throw new RuntimeException("批量逻辑删除用户信息失败: " + e.getMessage(), e);
        }
    }

}
