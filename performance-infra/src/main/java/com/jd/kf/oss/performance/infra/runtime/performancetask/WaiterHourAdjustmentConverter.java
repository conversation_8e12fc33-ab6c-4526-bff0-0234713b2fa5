package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexDataPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.WaiterHourAdjustmentPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息PO与DO转换器
 */
@Mapper
public interface WaiterHourAdjustmentConverter {
    
    WaiterHourAdjustmentConverter INSTANCE = Mappers.getMapper(WaiterHourAdjustmentConverter.class);

    /**
     * PO转DO
     */
    WaiterHourAdjustment po2DO(WaiterHourAdjustmentPO adjustmentPO);

    /**
     * PO列表转DO列表
     */
    List<WaiterHourAdjustment> poList2DOList(List<WaiterHourAdjustmentPO> userPOList);
}
