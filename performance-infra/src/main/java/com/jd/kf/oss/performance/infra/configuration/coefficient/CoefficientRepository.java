package com.jd.kf.oss.performance.infra.configuration.coefficient;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.ICoefficientRepository;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientItemPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientPO;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientItemPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class CoefficientRepository implements ICoefficientRepository {

    @Autowired
    private ICoefficientPOService coefficientPOService;

    @Autowired
    private ICoefficientItemPOService coefficientItemPOService;


    /**
     * 新增系数配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO, "系数配置不能为空");
        CoefficientPO coefficientPO = DOConverter.INSTANCE.do2PO(coefficientDO);
        coefficientPOService.save(coefficientPO);
        if (CollectionUtils.isNotEmpty(coefficientDO.getCoefficientItems())) {
            List<CoefficientItemPO> coefficientItemPOS = DOConverter.INSTANCE.itemDO2PO(coefficientDO.getCoefficientItems());
            coefficientItemPOService.saveBatch(coefficientItemPOS);
        }
        return true;
    }

    /**
     * 根据系数编码和绩效月查询聚合DO
     */
    public CoefficientDO selectCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CoefficientPO coefficientPO = selectCoefficientByCodeAndPeriod(coefficientCode, period);
        CoefficientDO coefficientDO = DOConverter.INSTANCE.po2DO(coefficientPO);
        if (coefficientDO != null) {
            List<CoefficientItemPO> coefficientItemPOS = selectItemByCodeAndPeriod(coefficientCode, period);
            coefficientDO.setCoefficientItems(DOConverter.INSTANCE.itemPO2DO(coefficientItemPOS));
        }
        return coefficientDO;
    }

    /**
     * 更新系数配置
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO, "系数配置不能为空");
        CoefficientPO coefficientPO = DOConverter.INSTANCE.do2PO(coefficientDO);
        updateCoefficientByCodeAndPeriod(coefficientPO);
        List<CoefficientItemPO> coefficientItemPOS = DOConverter.INSTANCE.itemDO2PO(coefficientDO.getCoefficientItems());
        deleteItemByCodeAndPeriod(coefficientPO.getCode(), coefficientPO.getPeriod());
        if (CollectionUtils.isNotEmpty(coefficientItemPOS)) {
            coefficientItemPOService.saveBatch(coefficientItemPOS);
        }
        return true;
    }

    /**
     * 根据系数编码和绩效月删除系数配置
     *
     * @param coefficientCode
     * @param period
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, period, "系数编码和绩效月不能为空");
        boolean isDeleted= deleteCoefficientByCodeAndPeriod(coefficientCode, period);
        if(!isDeleted){
            return false;
        }
        return deleteItemByCodeAndPeriod(coefficientCode, period);
    }


    /**
     * 查询所有的系数
     */
    @Override
    public List<CoefficientDO> queryAllCoefficientDO(String tenantCode, String period) {
        CheckUtil.notBlank(tenantCode, period, "租户code和绩效月不能为空");
        List<CoefficientPO> coefficientPOS = coefficientPOService.lambdaQuery()
                .eq(CoefficientPO::getTenantCode, tenantCode)
                .eq(CoefficientPO::getPeriod, period)
                .eq(CoefficientPO::getYn, YnEnum.VALID.getValue())
                .list();

        List<CoefficientItemPO> coefficientItemPOS = coefficientItemPOService.lambdaQuery()
                .eq(CoefficientItemPO::getTenantCode, tenantCode)
                .eq(CoefficientItemPO::getPeriod, period)
                .eq(CoefficientItemPO::getYn, YnEnum.VALID.getValue())
                .list();

        return convert(coefficientPOS, coefficientItemPOS);
    }

    /**
     * 将PO转换为聚合DO
     */
    public List<CoefficientDO> convert(List<CoefficientPO> coefficientPOS, List<CoefficientItemPO> coefficientItemPOS) {
        if (CollectionUtils.isEmpty(coefficientPOS) || CollectionUtils.isEmpty(coefficientItemPOS)) {
            return Lists.newArrayList();
        }
        Multimap<String, CoefficientItemPO> coefficientItemPOMultimap = ArrayListMultimap.create();
        for (CoefficientItemPO coefficientItemPO : coefficientItemPOS) {
            coefficientItemPOMultimap.put(coefficientItemPO.getCoefficientCode(), coefficientItemPO);
        }
        List<CoefficientDO> coefficientDOS = Lists.newArrayList();
        for (CoefficientPO coefficientPO : coefficientPOS) {
            CoefficientDO coefficientDO = DOConverter.INSTANCE.po2DO(coefficientPO);
            List<CoefficientItemPO> itemPOS = Lists.newArrayList(coefficientItemPOMultimap.get(coefficientDO.getCode()));
            if (CollectionUtils.isNotEmpty(itemPOS)) {
                coefficientDO.setCoefficientItems(DOConverter.INSTANCE.itemPO2DO(coefficientItemPOS));
            }
            coefficientDOS.add(coefficientDO);
        }
        return coefficientDOS;
    }

    /**
     * 根据系数编码和绩效月删除系数配置
     * @param coefficientCode
     * @param period
     * @return
     */
    public boolean deleteCoefficientByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, period, "系数编码和绩效月不能为空");
        // 物理删除
        LambdaQueryWrapper<CoefficientPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoefficientPO::getCode, coefficientCode)
                .eq(CoefficientPO::getPeriod, period)
                .eq(CoefficientPO::getYn, YnEnum.VALID.getValue());
        return coefficientPOService.remove(queryWrapper);
    }

    /**
     * 根据系数code和绩效月查询系数
     *
     * @param coefficientCode
     * @param period
     * @return
     */
    public CoefficientPO selectCoefficientByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, period, "系数编码和绩效月不能为空");
        LambdaQueryWrapper<CoefficientPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoefficientPO::getCode, coefficientCode)
                .eq(CoefficientPO::getPeriod, period)
                .eq(CoefficientPO::getYn, YnEnum.VALID.getValue());
        return coefficientPOService.getOne(queryWrapper);

    }

    /**
     * 更新系数
     *
     * @param coefficientPO
     * @return
     */
    public boolean updateCoefficientByCodeAndPeriod(CoefficientPO coefficientPO) {
        CheckUtil.notNull(coefficientPO, "系数配置不能为空");
        LambdaUpdateWrapper<CoefficientPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CoefficientPO::getCode, coefficientPO.getCode())
                .eq(CoefficientPO::getPeriod, coefficientPO.getPeriod())
                .eq(CoefficientPO::getYn,YnEnum.VALID.getValue())
                .set(CoefficientPO::getName, coefficientPO.getName())
                .set(CoefficientPO::getDescription, coefficientPO.getDescription())
                .set(CoefficientPO::getStatus, coefficientPO.getStatus())
                .set(CoefficientPO::getEditor, UserContextHolder.getOperator())
                .set(CoefficientPO::getModified, LocalDateTime.now());
        return coefficientPOService.update(updateWrapper);
    }

    /**
     * 分页查询系数配置
     *
     * @param pageCommand
     * @return
     */
    public Page<CoefficientPO> page(PageCommand<CoefficientPO> pageCommand) {
        CheckUtil.notNull(pageCommand, pageCommand.getPageNo(), pageCommand.getPageSize(), pageCommand.getObj(), "分页查询参数不能为空");
        Page<CoefficientPO> mybatisPage = PageUtil.toMybatisPage(pageCommand.getPageNo(), pageCommand.getPageSize());
        LambdaQueryWrapper<CoefficientPO> queryWrapper = new LambdaQueryWrapper<>();
        CoefficientPO coefficientPO = pageCommand.getObj();
        queryWrapper.eq(CoefficientPO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(coefficientPO.getPeriod()), CoefficientPO::getPeriod, coefficientPO.getPeriod())
                .eq(Objects.nonNull(coefficientPO.getType()), CoefficientPO::getType, coefficientPO.getType())
                .like(StringUtils.isNotBlank(coefficientPO.getName()), CoefficientPO::getName, coefficientPO.getName());
        return coefficientPOService.page(mybatisPage, queryWrapper);
    }

    /**
     * 删除系数配置,根据系数编码和绩效月
     *
     * @param coefficientCode
     * @param period
     * @return
     */
    public boolean deleteItemByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, "系数编码不能为空");
        CheckUtil.notBlank(period, "绩效月不能为空");
        LambdaQueryWrapper<CoefficientItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoefficientItemPO::getCoefficientCode, coefficientCode)
                .eq(CoefficientItemPO::getPeriod, period);
        return coefficientItemPOService.remove(queryWrapper);
    }

    /**
     * 根据系数code以及绩效月查询系数配置项
     *
     * @param coefficientCode
     * @param period
     * @return
     */
    public List<CoefficientItemPO> selectItemByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, "系数编码不能为空");
        CheckUtil.notBlank(period, "绩效月不能为空");
        LambdaQueryWrapper<CoefficientItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoefficientItemPO::getCoefficientCode, coefficientCode)
                .eq(CoefficientItemPO::getPeriod, period)
                .eq(CoefficientItemPO::getYn, YnEnum.VALID.getValue());
        return coefficientItemPOService.list(queryWrapper);
    }

    /**
     * 根据名称、租户代码和期间查询系数信息
     * @param name 系数名称
     * @param tenantCode 租户代码
     * @param period 期间
     * @return 查询到的系数数据对象
     */
    public CoefficientDO selectCoefficientByName(String name,String tenantCode,String period){
        // 参数校验
        CheckUtil.notBlank(name,tenantCode,period,"系数名称、租户和绩效月都不能为空");
        LambdaQueryWrapper<CoefficientPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoefficientPO::getName, name.trim())
                .eq(CoefficientPO::getTenantCode, tenantCode)
                .eq(CoefficientPO::getPeriod, period)
                .eq(CoefficientPO::getYn,YnEnum.VALID.getValue());
        CoefficientPO po = coefficientPOService.getOne(queryWrapper);
        return DOConverter.INSTANCE.po2DO(po);
    }


    /**
     * 根据系数名称和类型分页查询系数配置
     *
     * @param name     系数名称，可选
     * @param period   绩效月，可选
     * @param type     系数类型，可选
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    @Override
    public CommonPage<CoefficientDO> queryCoefficientByNameAndType(String name, String period, CoefficientTemplateTypeEnum type, int pageNum, int pageSize) {
        // 保持与原有DO对象版本完全一致的校验逻辑
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        Page<CoefficientPO> mybatisPage = PageUtil.toMybatisPage(pageNum, pageSize);
        LambdaQueryWrapper<CoefficientPO> queryWrapper = new LambdaQueryWrapper<>();
        // 保持与原有版本完全一致的查询逻辑
        queryWrapper.eq(CoefficientPO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(period), CoefficientPO::getPeriod, period)
                .eq(Objects.nonNull(type), CoefficientPO::getType, type==null?null:type.getType())
                .like(StringUtils.isNotBlank(name), CoefficientPO::getName, name)
                .orderByDesc(CoefficientPO::getModified)
                .orderByDesc(CoefficientPO::getCreated);

        Page<CoefficientPO> page = coefficientPOService.page(mybatisPage, queryWrapper);
        return DOConverter.INSTANCE.pagePO2DO(page);
    }


}

