package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("wfc_dept")
public class WfcDeptPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 用于做唯一索引单独加字段，当有效时，该字段为0，删除时为主键id的值
     */
    private Long uniqId;

    /**
     * 接入外部系统数据的租户标识
     */
    private String bizTenantCode;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 上级部门id
     */
    private String deptPid;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门层级
     */
    private Integer deptLevel;

    /**
     * 部门id路径
     */
    private String deptPath;

    /**
     * 部门类型
     */
    private String deptType;

    /**
     * 是否允许删除，1-是，0-否
     */
    private Boolean allowDel;

    /**
     * 部门备注
     */
    private String deptNote;

    /**
     * 是否属于离职部门，1-是，0-否
     */
    private Boolean departDept;

    /**
     * 部门主管erp
     */
    private String deptManager;

    /**
     * 部门渠道属性
     */
    private String deptChannel;

    /**
     * 部门业务属性
     */
    private String deptBusiness;

    /**
     * 时间戳
     */
    private Timestamp ts;
}
