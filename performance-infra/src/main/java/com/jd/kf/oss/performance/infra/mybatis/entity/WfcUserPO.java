package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("wfc_user")
public class WfcUserPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 用于做唯一索引单独加字段，当有效时，该字段为0，删除时为主键id的值
     */
    private Long uniqId;

    /**
     * 绩效组Id
     */
    private String businessLineId;

    /**
     * 接入外部系统数据的租户标识
     */
    private String bizTenantCode;

    /**
     * 用户erp
     */
    private String erp;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 业务部门id
     */
    private String bizDeptId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 时间戳
     */
    private Timestamp ts;
}
