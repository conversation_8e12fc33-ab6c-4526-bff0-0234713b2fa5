package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系数条目
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("coefficient_item")
public class CoefficientItemPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 系数id
     */
    private String coefficientCode;

    /**
     * 系数名称
     */
    private String coefficientName;

    /**
     * 左边界
     */
    private String leftEndpoint;

    /**
     * 右边界
     */
    private String rightEndpoint;

    /**
     * 系数值
     */
    private String coefficientNum;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
