package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.CoefficientPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系数信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class CoefficientPOServiceImpl extends ServiceImpl<CoefficientPOMapper, CoefficientPO> implements ICoefficientPOService {

}
