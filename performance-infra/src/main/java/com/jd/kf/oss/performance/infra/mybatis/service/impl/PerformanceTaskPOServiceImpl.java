package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTaskPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTaskPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTaskPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 绩效任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Service
public class PerformanceTaskPOServiceImpl extends ServiceImpl<PerformanceTaskPOMapper, PerformanceTaskPO> implements IPerformanceTaskPOService {

}
