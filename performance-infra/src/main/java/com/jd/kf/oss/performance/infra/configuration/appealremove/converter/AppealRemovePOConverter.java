package com.jd.kf.oss.performance.infra.configuration.appealremove.converter;

import com.jd.kf.oss.performance.domain.config.domain.appealremove.AppealRemoveDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.AppealRemovePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 无效数据剔除PO转换器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface AppealRemovePOConverter {
    
    AppealRemovePOConverter INSTANCE = Mappers.getMapper(AppealRemovePOConverter.class);

    /**
     * DO转PO
     */
    AppealRemovePO do2PO(AppealRemoveDO appealRemoveDO);

    /**
     * PO转DO
     */
    AppealRemoveDO po2DO(AppealRemovePO appealRemovePO);

    /**
     * DO列表转PO列表
     */
    List<AppealRemovePO> doList2POList(List<AppealRemoveDO> appealRemoveDOList);

    /**
     * PO列表转DO列表
     */
    List<AppealRemoveDO> poList2DOList(List<AppealRemovePO> appealRemovePOList);
}
