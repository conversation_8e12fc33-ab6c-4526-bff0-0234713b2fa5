package com.jd.kf.oss.performance.infra.mybatis.mapper;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface WfcDeptPOMapper extends BaseMapper<WfcDeptPO> {

    /**
     * 批量新增部门
     *
     * @param deptList 部门列表
     * @return 影响行数
     */
    int batchSaveOrUpdate(@Param("deptList") List<WfcDeptPO> deptList);

    /**
     * 批量逻辑删除部门信息根据ID列表
     * @param idList 部门ID列表
     * @return 删除的记录数
     */
    int batchDeleteByIdList(@Param("idList") List<Long> idList);

    /**
     * 根据上级部门ID查询所有下级部门ID列表（包含自身）
     * 通过deptPath字段进行层级查询，deptPath格式如：1@5@19843@19844@21753@21754@21755@
     * @param tenantCode 租户标识
     * @param parentDeptId 上级部门ID
     * @return 包含该部门及其所有下级部门的ID列表
     */
    List<String> querySubDeptIdsByParentDeptId(@Param("tenantCode") String tenantCode, @Param("parentDeptId") String parentDeptId);

}
