package com.jd.kf.oss.performance.infra.configuration.evaluate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.domain.config.aggregate.common.Constants;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDO;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.IEvaluateRepository;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.EvaluatePO;
import com.jd.kf.oss.performance.infra.mybatis.service.IEvaluatePOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Repository实现：上级评价
 * 提供上级评价相关的数据访问实现
 */
@Service
public class EvaluateRepository implements IEvaluateRepository {

    @Autowired
    private IEvaluatePOService evaluatePOService;

    /**
     * 根据条件分页查询评估信息
     * @param period 评估周期
     * @param managerErp 管理员ERP账号
     * @param pageNum 页码，必须大于0
     * @param pageSize 每页大小，必须大于0
     * @return 包含评估信息的分页结果
     */
    @Override
    public CommonPage<EvaluateDO> queryEvaluateByConditions(String period, String managerErp,
                                                            int pageNum, int pageSize) {
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        Page<EvaluatePO> evaluatePOPage = PageUtil.toMybatisPage(pageNum, pageSize);
        LambdaQueryWrapper<EvaluatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluatePO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(managerErp), EvaluatePO::getErp, managerErp)
                .eq(StringUtils.isNotBlank(period), EvaluatePO::getPeriod, period)
                .orderByDesc(EvaluatePO::getModified)
                .orderByDesc(EvaluatePO::getCreated);

        Page<EvaluatePO> page = evaluatePOService.page(evaluatePOPage, queryWrapper);
        return EvaluatePOConverter.INSTANCE.pageP02DO(page);
    }

    /**
     * 批量软删除指定绩效周期和租户的评价记录
     * @param evaluateIds 待删除的评价ID列表，不能为空
     * @param period 绩效月份，不能为空
     */
    @Override
    public void deleteEvaluate(List<Long> evaluateIds, String period) {
        // 参数校验
        CheckUtil.notEmpty(evaluateIds, "待删除的评价ID列表不能为空");
        CheckUtil.notBlank(period, "绩效月不能为空");

        // 构造软删除条件：id in (evaluateIds) and period = period and tenantCode = tenantCode and yn = true
        LambdaUpdateWrapper<EvaluatePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(EvaluatePO::getId, evaluateIds)
                    .eq(EvaluatePO::getPeriod, period)
                    .eq(EvaluatePO::getYn, YnEnum.VALID.getValue())
                    .set(EvaluatePO::getYn, YnEnum.INVALID.getValue());
        evaluatePOService.update(updateWrapper);
    }


    /**
     * 根据租户标识、绩效月和主管ERP列表查询评估记录列表
     *
     * @param period     绩效月，不能为空
     * @param managerErp 主管ERP列表，可为空
     * @return 评估记录DO对象列表
     */
    @Override
    public List<EvaluateDO> queryEvaluateList(String period, String managerErp) {
        // 参数校验
        CheckUtil.notBlank(period, "绩效月不能为空");

        // 构造查询条件
        LambdaQueryWrapper<EvaluatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluatePO::getYn, YnEnum.VALID.getValue())
                .eq(EvaluatePO::getPeriod, period)
                .eq(StringUtils.isNotBlank(managerErp), EvaluatePO::getErp, managerErp);

        List<EvaluatePO> evaluatePOList = evaluatePOService.list(queryWrapper);
        return evaluatePOList.stream()
                .map(EvaluatePOConverter.INSTANCE::po2DO)
                .collect(Collectors.toList());
    }

    /**
     * 批量保存评价数据
     * @param evaluateList 待保存的评价数据列表，不能为空
     * @return 成功保存的记录数量，若保存失败返回0
     */
    @Override
    public int batchSaveEvaluate(List<EvaluateDO> evaluateList) {
        // 参数校验
        CheckUtil.notEmpty(evaluateList, "评价数据列表不能为空");

        // 转换为PO对象
        List<EvaluatePO> evaluatePOList = evaluateList.stream()
                .map(EvaluatePOConverter.INSTANCE::do2PO)
                .collect(Collectors.toList());

        // 批量保存
        boolean success = evaluatePOService.saveOrUpdateBatch(evaluatePOList);
        return success ? evaluatePOList.size() : 0;
    }
}