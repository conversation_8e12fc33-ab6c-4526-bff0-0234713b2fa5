package com.jd.kf.oss.performance.infra.configuration.appealremove;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.domain.appealremove.AppealRemoveDO;
import com.jd.kf.oss.performance.domain.config.domain.appealremove.IAppealRemoveRepository;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.appealremove.converter.AppealRemovePOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.AppealRemovePO;
import com.jd.kf.oss.performance.infra.mybatis.service.IAppealRemovePOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import com.jd.kf.oss.performance.domain.config.aggregate.common.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 无效数据剔除仓储实现
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
@Slf4j
public class AppealRemoveRepository implements IAppealRemoveRepository {

    @Resource
    private IAppealRemovePOService appealRemovePOService;


    /**
     * 根据指定条件分页查询申诉移除记录
     * @param period 考核周期
     * @param kpiName KPI指标名称(模糊匹配)
     * @param ticketId 工单ID(模糊匹配)
     * @param pageNum 页码(必须大于0)
     * @param pageSize 每页数量(必须大于0)
     * @return 包含分页信息的申诉移除记录列表
     */
    @Override
    public CommonPage<AppealRemoveDO> queryAppealRemoveByConditions(String period, String kpiName, String ticketId,
                                                                    int pageNum, int pageSize) {
        // 参数校验
        validatePaginationParams(pageNum, pageSize);

        Page<AppealRemovePO> page = PageUtil.toMybatisPage(pageNum, pageSize);
        LambdaQueryWrapper<AppealRemovePO> queryWrapper = buildQueryWrapper(period, kpiName, ticketId, null, null);

        Page<AppealRemovePO> resultPage = appealRemovePOService.page(page, queryWrapper);
        List<AppealRemoveDO> appealRemoveDOList = resultPage.getRecords().stream()
                .map(AppealRemovePOConverter.INSTANCE::po2DO)
                .collect(Collectors.toList());

        return CommonPage.getCommonPage(Long.valueOf(pageNum), Long.valueOf(pageSize),
                                       resultPage.getTotal(), appealRemoveDOList);
    }

    /**
     * 批量逻辑删除指定租户下的申诉移除记录
     * @param ids 待删除记录ID列表，不能为空
     * @return 返回更新操作结果，成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids) {
        CheckUtil.notEmpty(ids, "待删除ID列表不能为空");

        LambdaUpdateWrapper<AppealRemovePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AppealRemovePO::getId, ids)
                    .eq(AppealRemovePO::getYn, YnEnum.VALID.getValue())
                    .set(AppealRemovePO::getYn, YnEnum.INVALID.getValue())
                    .set(AppealRemovePO::getModified, LocalDateTime.now());

        boolean result = appealRemovePOService.update(updateWrapper);
        return result;
    }


    /**
     * 查询申诉移除列表
     * @param period 绩效月
     * @param kpiCd KPI代码
     * @param erp ERP系统标识
     * @return 申诉移除数据对象列表
     */
    @Override
    public List<AppealRemoveDO> queryAppealRemoveList(String period, String kpiCd, String erp) {

        CheckUtil.notBlank(period, "绩效月不能为空");

        LambdaQueryWrapper<AppealRemovePO> queryWrapper = buildQueryWrapper(period, null, null, kpiCd, erp);
        List<AppealRemovePO> appealRemovePOList = appealRemovePOService.list(queryWrapper);
        return AppealRemovePOConverter.INSTANCE.poList2DOList(appealRemovePOList);
    }


    /**
     * 批量保存申诉剔除数据
     * @param appealRemoveList 待保存的申诉剔除数据列表，不可为空
     * @return 实际成功保存的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<AppealRemoveDO> appealRemoveList) {
        CheckUtil.notEmpty(appealRemoveList, "无效数据剔除列表不能为空");

        List<AppealRemovePO> appealRemovePOList = AppealRemovePOConverter.INSTANCE.doList2POList(appealRemoveList);

        boolean success = appealRemovePOService.saveBatch(appealRemovePOList);
        int savedCount = success ? appealRemovePOList.size() : 0;
        return savedCount;
    }

    /**
     * 检查指定条件下是否存在重复的申诉移除记录
     * @param period 绩效月，不能为空
     * @param ticketId 单号，不能为空
     * @return 若存在符合条件的记录则返回true，否则返回false
     */
    @Override
    public boolean existsDuplicate(String period, String ticketId, String kpiName) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        CheckUtil.notBlank(ticketId, "单号不能为空");

        LambdaQueryWrapper<AppealRemovePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppealRemovePO::getYn, YnEnum.VALID.getValue())
                .eq(AppealRemovePO::getPeriod, period)
                .eq(AppealRemovePO::getKpiName, kpiName)
                .eq(AppealRemovePO::getTicketId, ticketId);

        return appealRemovePOService.count(queryWrapper) > 0;
    }

    /**
     * 构建通用查询条件
     * @param period 绩效月
     * @param kpiName 指标名称(模糊匹配)
     * @param ticketId 工单ID(模糊匹配)
     * @param kpiCd 指标code(精确匹配)
     * @param erp 客服ERP(精确匹配)
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<AppealRemovePO> buildQueryWrapper(String period, String kpiName, String ticketId, String kpiCd, String erp) {
        LambdaQueryWrapper<AppealRemovePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppealRemovePO::getYn, YnEnum.VALID.getValue());

        if (StringUtils.isNotBlank(period)) {
            queryWrapper.eq(AppealRemovePO::getPeriod, period);
        }
        if (StringUtils.isNotBlank(kpiName)) {
            queryWrapper.like(AppealRemovePO::getKpiName, kpiName);
        }
        if (StringUtils.isNotBlank(ticketId)) {
            queryWrapper.like(AppealRemovePO::getTicketId, ticketId);
        }

        queryWrapper.orderByDesc(AppealRemovePO::getModified);
        queryWrapper.orderByDesc(AppealRemovePO::getCreated);
        return queryWrapper;
    }

    /**
     * 校验分页参数
     * @param pageNum 页码
     * @param pageSize 页面大小
     */
    private void validatePaginationParams(int pageNum, int pageSize) {
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
    }
}
