package com.jd.kf.oss.performance.infra.mybatis.mapper;

import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface UserPOMapper extends BaseMapper<UserPO> {

    /**
     * 根据条件查询用户信息（多表关联）
     * @param params 查询参数
     * @return 用户信息列表
     */
    List<UserDO> queryUserInfoByConditions(@Param("params") Map<String, Object> params);

    /**
     * 根据条件统计用户信息总数（多表关联）
     * @param params 查询参数
     * @return 总数
     */
    Long countUserInfoByConditions(@Param("params") Map<String, Object> params);

    /**
     * 从WFC表中查询所有有效用户数据（关联wfc_user、wfc_user_ext、wfc_dept）
     * @return 用户信息列表
     */
    List<UserDO> queryAllWfcUserData();

    /**
     * 批量更新用户数据（通过erp、tenantCode、period条件）
     * @param userList 用户列表
     * @return 更新的记录数
     */
    int batchUpdateByCondition(@Param("userList") List<UserPO> userList);
}
