package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTargetPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTargetPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTargetPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 绩效目标 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class PerformanceTargetPOServiceImpl extends ServiceImpl<PerformanceTargetPOMapper, PerformanceTargetPO> implements IPerformanceTargetPOService {

}
