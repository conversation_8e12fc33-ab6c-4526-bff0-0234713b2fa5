package com.jd.kf.oss.performance.infra.runtime.performanceresult;

import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.IPerformanceResultRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceResultDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceFactorResultPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceResultPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceFactorResultPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceResultPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PerformanceResultRepository implements IPerformanceResultRepository {

    @Resource
    private IPerformanceFactorResultPOService performanceFactorResultPOService;

    @Resource
    private IPerformanceResultPOService performanceResultPOService;
    @Override
    public void save(PerformanceFactorResultDO resultDO) {
        PerformanceFactorResultDO existing = query(resultDO.getTenantCode(),resultDO.getPeriod(), resultDO.getTaskId(),
                resultDO.getBusinessLineId(), resultDO.getErp(), resultDO.getFactorId());
        PerformanceFactorResultPO po = convertToPO(existing, resultDO);
        performanceFactorResultPOService.save(po);
        resultDO.setId(po.getId());
    }

    @Override
    public void batchSave(List<PerformanceFactorResultDO> resultDOS) {
        if (CollectionUtils.isEmpty(resultDOS)) {
            return;
        }
        List<PerformanceFactorResultDO> preResults = query(resultDOS.get(0).getTenantCode(), resultDOS.get(0).getPeriod(), resultDOS.get(0).getTaskId(), resultDOS.get(0).getBusinessLineId());
        Map<String, PerformanceFactorResultDO> preResultMap = preResults.stream().collect(Collectors.toMap(PerformanceFactorResultDO::buildKey, PerformanceFactorResultDO->PerformanceFactorResultDO));
        List<PerformanceFactorResultDO> toUpdate = Lists.newArrayList();
        List<PerformanceFactorResultDO> toInsert = Lists.newArrayList();
        for (PerformanceFactorResultDO resultDO : resultDOS) {
            if (preResultMap.get(resultDO.buildKey()) != null) {
                resultDO.setId(preResultMap.get(resultDO.buildKey()).getId());
                toUpdate.add(resultDO);
            } else {
                toInsert.add(resultDO);
            }
        }
        batchUpdate(toUpdate);
        batchInsert(toInsert);
    }

    @Override
    public void batchSavePlan(List<PerformanceResultDO> resultDOS) {
        if (CollectionUtils.isEmpty(resultDOS)) {
            return;
        }
        List<PerformanceResultDO> toInsert = Lists.newArrayList();
        for (PerformanceResultDO resultDO : resultDOS) {
            toInsert.add(resultDO);
        }
        batchInsertPlan(toInsert);
    }

    private void batchUpdate(List<PerformanceFactorResultDO> toUpdate) {
        if (CollectionUtils.isEmpty(toUpdate)) {
            return;
        }
        List<PerformanceFactorResultPO> resultPOS = toUpdate.stream().map(a -> convertToPO(null, a)).collect(Collectors.toList());
        for (List<PerformanceFactorResultPO> performanceFactorResultPOS : Lists.partition(resultPOS, 100)) {
            performanceFactorResultPOService.saveOrUpdateBatch(performanceFactorResultPOS);
        }
    }

    private void batchInsert(List<PerformanceFactorResultDO> toInsert) {
        if (CollectionUtils.isEmpty(toInsert)) {
            return;
        }
        List<PerformanceFactorResultPO> resultPOS = toInsert.stream().map(a -> convertToPO(null, a)).collect(Collectors.toList());
        for (List<PerformanceFactorResultPO> performanceFactorResultPOS : Lists.partition(resultPOS, 100)) {
            performanceFactorResultPOService.saveBatch(performanceFactorResultPOS);
        }
    }

    private void batchInsertPlan(List<PerformanceResultDO> toInsert) {
        if (CollectionUtils.isEmpty(toInsert)) {
            return;
        }
        List<PerformanceResultPO> resultPOS = toInsert.stream().map(a -> convertToPO(null, a)).collect(Collectors.toList());
        for (List<PerformanceResultPO> performanceResultPOS : Lists.partition(resultPOS, 100)) {
            performanceResultPOService.saveBatch(performanceResultPOS);
        }
    }


    public List<PerformanceFactorResultDO> query(String tenantCode, String period, Long taskId, String businessLineId) {
        List<PerformanceFactorResultPO> resultPOS = performanceFactorResultPOService.lambdaQuery()
                .eq(PerformanceFactorResultPO::getTenantCode, tenantCode)
                .eq(PerformanceFactorResultPO::getPeriod, period)
                .eq(PerformanceFactorResultPO::getBusinessLineId, businessLineId)
                .eq(PerformanceFactorResultPO::getTaskId, taskId)
                .list();
        return resultPOS.stream().map(a -> convertToDO(a)).collect(Collectors.toList());
    }

    @Override
    public PerformanceFactorResultDO query(String tenantCode, String period, Long taskId, String businessLineId, String erp, String factorId) {
        PerformanceFactorResultPO resultPO = performanceFactorResultPOService.lambdaQuery()
                .eq(PerformanceFactorResultPO::getTenantCode, tenantCode)
                .eq(PerformanceFactorResultPO::getPeriod, period)
                .eq(PerformanceFactorResultPO::getBusinessLineId, businessLineId)
                .eq(PerformanceFactorResultPO::getTaskId, taskId)
                .eq(PerformanceFactorResultPO::getErp, erp)
                .eq(PerformanceFactorResultPO::getFactorId, factorId)
                .one();
        return convertToDO(resultPO);
    }

    @Override
    public void save(PerformanceResultDO resultDO) {
        PerformanceResultDO existing = query(resultDO.getTenantCode(),resultDO.getPeriod(), resultDO.getTaskId(),
                resultDO.getBusinessLineId(), resultDO.getErp());
        PerformanceResultPO po = convertToPO(existing, resultDO);
        performanceResultPOService.save(po);
        resultDO.setId(po.getId());
    }

    @Override
    public PerformanceResultDO query(String tenantCode, String period, Long taskId, String businessLineId, String erp) {
        PerformanceResultPO resultPO = performanceResultPOService.lambdaQuery()
                .eq(PerformanceResultPO::getTenantCode, tenantCode)
                .eq(PerformanceResultPO::getPeriod, period)
                .eq(PerformanceResultPO::getBusinessLineId, businessLineId)
                .eq(PerformanceResultPO::getTaskId, taskId)
                .eq(PerformanceResultPO::getErp, erp)
                .one();
        return convertToDO(resultPO);
    }

    @Override
    public List<PerformanceFactorResultDO> query(String tenantCode, String period, Long taskId, List<String> erps) {
        if (StringUtils.isAnyEmpty(tenantCode, period) || Objects.isNull(taskId) || CollectionUtils.isEmpty(erps)) {
            return Lists.newArrayList();
        }
        List<PerformanceFactorResultPO> resultPOS = performanceFactorResultPOService.lambdaQuery()
                .eq(PerformanceFactorResultPO::getTenantCode, tenantCode)
                .eq(PerformanceFactorResultPO::getPeriod, period)
                .eq(PerformanceFactorResultPO::getTaskId, taskId)
                .in(PerformanceFactorResultPO::getErp, erps)
                .list();
        return resultPOS.stream().map(PerformanceResultRepository::convertToDO).collect(Collectors.toList());
    }

    private static PerformanceFactorResultPO convertToPO(PerformanceFactorResultDO old, PerformanceFactorResultDO doObj) {
        if (doObj == null) {
            return null;
        }

        PerformanceFactorResultPO po = new PerformanceFactorResultPO();
        // 基础字段映射
        po.setId(doObj.getId());
        if (old != null) {
            po.setId(old.getId());
        }
        po.setTenantCode(doObj.getTenantCode());
        po.setCreator(doObj.getCreator());
        po.setEditor(doObj.getEditor());
        po.setCreated(doObj.getCreated());
        po.setModified(doObj.getModified());

        // 业务字段映射
        po.setBusinessLineId(doObj.getBusinessLineId());
        po.setBusinessLineName(doObj.getBusinessLineName());
        po.setTaskId(doObj.getTaskId());
        po.setErp(doObj.getErp());
        po.setType(doObj.getType());
        po.setFactorId(doObj.getFactorId());
        po.setResult(doObj.getResult());
        po.setDetail(doObj.getDetail());
        po.setPeriod(doObj.getPeriod());
        po.setStatus(doObj.getStatus());
        po.setYn(true);
        return po;
    }

    private static PerformanceFactorResultDO convertToDO(PerformanceFactorResultPO resultPO) {
        if (resultPO == null) {
            return null;
        }
        PerformanceFactorResultDO resultDO = new PerformanceFactorResultDO();
        // 基础字段映射
        resultDO.setId(resultPO.getId());
        resultDO.setTenantCode(resultPO.getTenantCode());
        resultDO.setCreator(resultPO.getCreator());
        resultDO.setEditor(resultPO.getEditor());
        resultDO.setCreated(resultPO.getCreated());
        resultDO.setModified(resultPO.getModified());

        // 业务字段映射
        resultDO.setBusinessLineId(resultPO.getBusinessLineId());
        resultDO.setBusinessLineName(resultPO.getBusinessLineName());
        resultDO.setTaskId(resultPO.getTaskId());
        resultDO.setErp(resultPO.getErp());
        resultDO.setType(resultPO.getType());
        resultDO.setFactorId(resultPO.getFactorId());
        resultDO.setResult(resultPO.getResult());
        resultDO.setDetail(resultPO.getDetail());
        resultDO.setPeriod(resultPO.getPeriod());
        resultDO.setStatus(resultPO.getStatus());
        return resultDO;
    }

    private static PerformanceResultPO convertToPO(PerformanceResultDO old, PerformanceResultDO doObj) {
        if (doObj == null) {
            return null;
        }

        PerformanceResultPO po = new PerformanceResultPO();
        // 基础字段映射
        po.setId(doObj.getId());
        if (old != null) {
            po.setId(old.getId());
        }
        po.setTenantCode(doObj.getTenantCode());
        po.setCreator(doObj.getCreator());
        po.setEditor(doObj.getEditor());
        po.setCreated(doObj.getCreated());
        po.setModified(doObj.getModified());

        // 业务字段映射
        po.setBusinessLineId(doObj.getBusinessLineId());
        po.setBusinessLineName(doObj.getBusinessLineName());
        po.setTaskId(doObj.getTaskId());
        po.setErp(doObj.getErp());
        po.setType(doObj.getType());
        po.setResult(doObj.getResult());
        po.setDetail(doObj.getDetail());
        po.setPeriod(doObj.getPeriod());
        po.setStatus(doObj.getStatus());
        po.setYn(true);
        return po;
    }

    private static PerformanceResultDO convertToDO(PerformanceResultPO resultPO) {
        if (resultPO == null) {
            return null;
        }
        PerformanceResultDO resultDO = new PerformanceResultDO();
        // 基础字段映射
        resultDO.setId(resultPO.getId());
        resultDO.setTenantCode(resultPO.getTenantCode());
        resultDO.setCreator(resultPO.getCreator());
        resultDO.setEditor(resultPO.getEditor());
        resultDO.setCreated(resultPO.getCreated());
        resultDO.setModified(resultPO.getModified());

        // 业务字段映射
        resultDO.setBusinessLineId(resultPO.getBusinessLineId());
        resultDO.setBusinessLineName(resultPO.getBusinessLineName());
        resultDO.setTaskId(resultPO.getTaskId());
        resultDO.setErp(resultPO.getErp());
        resultDO.setType(resultPO.getType());
        resultDO.setResult(resultPO.getResult());
        resultDO.setDetail(resultPO.getDetail());
        resultDO.setPeriod(resultPO.getPeriod());
        resultDO.setStatus(resultPO.getStatus());
        return resultDO;
    }
}
