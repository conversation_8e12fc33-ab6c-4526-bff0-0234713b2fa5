package com.jd.kf.oss.performance.infra.configuration.appealmodify;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.AppealModifyDO;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.IAppealModifyRepository;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.appealmodify.converter.AppealModifyPOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.AppealModifyPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IAppealModifyPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 归属数据修改仓储实现
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class AppealModifyRepository implements IAppealModifyRepository {

    @Resource
    private IAppealModifyPOService appealModifyPOService;

    /**
     * 根据条件分页查询申诉修改记录
     *
     * @param period     期间/周期条件
     * @param kpiName    KPI名称条件(模糊匹配)
     * @param skillId    技能ID条件
     * @param ticketId   单号条件(模糊匹配)
     * @param pageNum    当前页码(必须大于0)
     * @param pageSize   每页记录数(必须大于0)
     * @return 包含分页信息的申诉修改记录列表
     */
    @Override
    public CommonPage<AppealModifyDO> queryAppealModifyByConditions(String period, String kpiName, String skillId, String ticketId,
                                                                    int pageNum, int pageSize) {
        // 保持与原有方法完全一致的校验逻辑
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        Page<AppealModifyPO> page = PageUtil.toMybatisPage(pageNum, pageSize);

        LambdaQueryWrapper<AppealModifyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppealModifyPO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(period), AppealModifyPO::getPeriod, period)
                .like(StringUtils.isNotBlank(kpiName), AppealModifyPO::getKpiName, kpiName)
                .eq(StringUtils.isNotBlank(skillId), AppealModifyPO::getSkillId, skillId)
                .eq(StringUtils.isNotBlank(ticketId), AppealModifyPO::getTicketId, ticketId)
                .orderByDesc(AppealModifyPO::getModified)
                .orderByDesc(AppealModifyPO::getCreated);

        Page<AppealModifyPO> resultPage = appealModifyPOService.page(page, queryWrapper);
        List<AppealModifyDO> appealModifyDOList = resultPage.getRecords().stream()
                .map(AppealModifyPOConverter.INSTANCE::po2DO)
                .collect(Collectors.toList());

        return CommonPage.getCommonPage(Long.valueOf(pageNum), resultPage.getTotal(),
                                       Long.valueOf(pageSize), appealModifyDOList);
    }



    /**
     * 批量逻辑删除申诉修改记录
     * @param ids 待删除记录ID列表，不能为空
     * @return 返回批量删除操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids) {
        CheckUtil.notEmpty(ids, "待删除ID列表不能为空");

        LambdaUpdateWrapper<AppealModifyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(AppealModifyPO::getId, ids)
                    .eq(AppealModifyPO::getYn, YnEnum.VALID.getValue())
                    .set(AppealModifyPO::getYn, YnEnum.INVALID.getValue());

        return appealModifyPOService.update(updateWrapper);
    }

    /**
     * 查询指定租户、绩效月和ERP账号的申诉修改记录列表
     * @param tenantCode 租户标识，不能为空
     * @param period 绩效月，不能为空
     * @param erp ERP账号，可为空
     * @return 申诉修改记录DO对象列表，按创建时间降序排列
     */
    @Override
    public List<AppealModifyDO> queryAppealModifyList(String tenantCode, String period, String erp) {
        CheckUtil.notBlank(tenantCode, "租户标识不能为空");
        CheckUtil.notBlank(period, "绩效月不能为空");

        LambdaQueryWrapper<AppealModifyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppealModifyPO::getYn, YnEnum.VALID.getValue())
                .eq(AppealModifyPO::getTenantCode, tenantCode)
                .eq(AppealModifyPO::getPeriod, period)
                .eq(StringUtils.isNotBlank(erp), AppealModifyPO::getErp, erp)
                .orderByDesc(AppealModifyPO::getCreated);

        List<AppealModifyPO> appealModifyPOList = appealModifyPOService.list(queryWrapper);
        return appealModifyPOList.stream()
                .map(AppealModifyPOConverter.INSTANCE::po2DO)
                .collect(Collectors.toList());
    }

    /**
     * 根据条件查询归属数据修改列表（用于导出，支持更多查询条件）
     *
     * @param period     绩效月
     * @param kpiName    指标名称，可选
     * @param skillId    技能ID，可选
     * @param ticketId   单号，可选
     * @return 归属数据修改列表
     */
    @Override
    public List<AppealModifyDO> queryAppealModifyListByConditions(String period, String kpiName, String skillId, String ticketId) {
        CheckUtil.notBlank(period, "绩效月不能为空");

        LambdaQueryWrapper<AppealModifyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppealModifyPO::getYn, YnEnum.VALID.getValue())
                .eq(AppealModifyPO::getPeriod, period)
                .like(StringUtils.isNotBlank(kpiName), AppealModifyPO::getKpiName, kpiName)
                .eq(StringUtils.isNotBlank(skillId), AppealModifyPO::getSkillId, skillId)
                .like(StringUtils.isNotBlank(ticketId), AppealModifyPO::getTicketId, ticketId)
                .orderByDesc(AppealModifyPO::getCreated);

        List<AppealModifyPO> appealModifyPOList = appealModifyPOService.list(queryWrapper);
        return appealModifyPOList.stream()
                .map(AppealModifyPOConverter.INSTANCE::po2DO)
                .collect(Collectors.toList());
    }

    /**
     * 批量保存归属数据修改记录
     * @param appealModifyList 归属数据修改列表，不能为空
     * @return 实际成功保存的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateBatch(List<AppealModifyDO> appealModifyList) {
        CheckUtil.notEmpty(appealModifyList, "归属数据修改列表不能为空");
        
        List<AppealModifyPO> appealModifyPOList = appealModifyList.stream()
                .map(AppealModifyPOConverter.INSTANCE::do2PO)
                .collect(Collectors.toList());
        
        boolean success = appealModifyPOService.saveOrUpdateBatch(appealModifyPOList);
        return success ? appealModifyPOList.size() : 0;
    }
}
