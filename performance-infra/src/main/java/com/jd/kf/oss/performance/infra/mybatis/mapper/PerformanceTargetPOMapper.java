package com.jd.kf.oss.performance.infra.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTargetPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 绩效目标 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface PerformanceTargetPOMapper extends BaseMapper<PerformanceTargetPO> {

    /**
     * 联表查询绩效目标信息，直接返回TargetDO
     * @param tenantCode 租户标识
     * @param period 绩效月
     * @param businessLineId 绩效组ID
     * @param planCode 方案编码
     * @param businessLineName 绩效组名称
     * @param offset 偏移量
     * @param limit 限制数量
     * @return TargetDO列表
     */
    List<PerformanceTargetDO> queryTargetWithJoin(@Param("tenantCode") String tenantCode,
                                                  @Param("period") String period,
                                                  @Param("businessLineId") String businessLineId,
                                                  @Param("planCode") String planCode,
                                                  @Param("businessLineName") String businessLineName,
                                                  @Param("offset") Integer offset,
                                                  @Param("limit") Integer limit);

    /**
     * 统计绩效目标总数（联表查询）
     * @param tenantCode 租户标识
     * @param period 绩效月
     * @param businessLineId 绩效组ID
     * @param planCode 方案编码
     * @param businessLineName 绩效组名称
     * @return 总数
     */
    Long countTargetWithJoin(@Param("tenantCode") String tenantCode,
                            @Param("period") String period,
                            @Param("businessLineId") String businessLineId,
                            @Param("planCode") String planCode,
                            @Param("businessLineName") String businessLineName);

    /**
     * 根据业务线ID、租户标识和绩效月查询单个绩效目标详情（联表查询）
     * @param businessLineId 业务线ID
     * @param tenantCode 租户标识
     * @param period 绩效月
     * @return 绩效目标详情
     */
    PerformanceTargetDO queryTargetDetailByBusinessLineId(@Param("businessLineId") String businessLineId,
                                                          @Param("tenantCode") String tenantCode,
                                                          @Param("period") String period);

    /**
     * 根据业务线ID列表和绩效月批量查询绩效目标（联表查询）
     * @param businessLineIds 业务线ID列表
     * @param period 绩效月
     * @return 绩效目标详情列表
     */
    List<PerformanceTargetDO> queryTargetListWithJoin(@Param("businessLineIds") List<String> businessLineIds,
                                                     @Param("period") String period);

}
