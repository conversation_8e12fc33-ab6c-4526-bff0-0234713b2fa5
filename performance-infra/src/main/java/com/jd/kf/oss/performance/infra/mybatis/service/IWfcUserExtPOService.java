package com.jd.kf.oss.performance.infra.mybatis.service;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserExtPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 人员扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IWfcUserExtPOService extends IService<WfcUserExtPO> {

    /**
     * 根据ERP列表批量获取用户扩展信息
     * @param erpList ERP列表
     * @return 用户扩展信息列表
     */
    List<WfcUserExtPO> getWfcUserExtListByErps(List<String> erpList);

    /**
     * 批量插入或更新用户扩展信息
     * @param userExtList 用户扩展信息列表
     */
    void batchUpsertUserExt(List<WfcUserExtPO> userExtList);

    /**
     * 批量逻辑删除用户扩展信息
     * @param userExtList 用户扩展信息列表
     */
    void batchRemoveUserExtById(List<WfcUserExtPO> userExtList);

}
