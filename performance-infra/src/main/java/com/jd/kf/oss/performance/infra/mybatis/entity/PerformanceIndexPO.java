package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("performance_index")
public class PerformanceIndexPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * kpi 类型: type =1 数量指标, type=2  率指标
     */
    private Integer kpiType;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 指标类型
     */
    private String template;

    /**
     * 描述
     */
    private String description;

    /**
     * 开始时间：格式
     */
    private String startDate;

    /**
     * 结束时间：格式
     */
    private String endDate;

    /**
     * 样本下限
     */
    private String threshold;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 权重
     */
    private String weight;

    /**
     * 状态
     */
    private String status;

    /**
     * 指标平台url跳转id
     */
    private String indexPlatformUrlCode;
}
