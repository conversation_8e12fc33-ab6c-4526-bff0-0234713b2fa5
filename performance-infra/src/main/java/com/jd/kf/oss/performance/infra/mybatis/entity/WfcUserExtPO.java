package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("wfc_user_ext")
public class WfcUserExtPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 用于做唯一索引单独加字段，当有效时，该字段为0，删除时为主键id的值
     */
    private Long uniqId;

    /**
     * 接入外部系统数据的租户标识
     */
    private String bizTenantCode;

    /**
     * 用户erp
     */
    private String erp;

    /**
     * 人资工作岗位编码
     */
    private String positionCode;

    /**
     * 人资工作岗位名称
     */
    private String positionName;

    /**
     * 人资用工性质
     */
    private String employmentNature;

    /**
     * 人资人员状态
     */
    private Integer personnelState;

    /**
     * 人资入职时间
     */
    private LocalDateTime entryTime;

    /**
     * 人资离职时间
     */
    private LocalDateTime quitTime;

    /**
     * 人员备注
     */
    private String note;

    /**
     * 生产状态
     */
    private String prodStatus;

    /**
     * 生产状态开始时间
     */
    private LocalDateTime prodStatusStartTime;

    /**
     * 生产状态结束时间
     */
    private LocalDateTime prodStatusEndTime;

    /**
     * 预离职时间
     */
    private LocalDateTime preQuitTime;

    /**
     * 预异动时间
     */
    private LocalDateTime preTransTime;

    /**
     * 上线时间
     */
    private LocalDateTime onlineTime;

    /**
     * 是否生产人力:1-是,0-否
     */
    private Boolean prodFlag;

    /**
     * 时间戳
     */
    private Timestamp ts;
}
