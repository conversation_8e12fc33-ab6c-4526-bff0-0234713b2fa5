package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.IndexDataAppealPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.IndexDataAppealPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexDataAppealPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指标数据申诉 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class IndexDataAppealPOServiceImpl extends ServiceImpl<IndexDataAppealPOMapper, IndexDataAppealPO> implements IIndexDataAppealPOService {

}
