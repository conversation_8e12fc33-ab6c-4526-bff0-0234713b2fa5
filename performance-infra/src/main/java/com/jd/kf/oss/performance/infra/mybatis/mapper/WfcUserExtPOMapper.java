package com.jd.kf.oss.performance.infra.mybatis.mapper;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserExtPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface WfcUserExtPOMapper extends BaseMapper<WfcUserExtPO> {

    /**
     * 批量逻辑删除部门信息根据ID列表
     * @param idList 部门ID列表
     * @return 删除的记录数
     */
    int batchDeleteByIdList(@Param("idList") List<Long> idList);

}
