package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceResultPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceResultPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceResultPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 绩效结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class PerformanceResultPOServiceImpl extends ServiceImpl<PerformanceResultPOMapper, PerformanceResultPO> implements IPerformanceResultPOService {

}
