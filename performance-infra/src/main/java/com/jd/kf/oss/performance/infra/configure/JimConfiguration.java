package com.jd.kf.oss.performance.infra.configure;

import org.springframework.context.annotation.Configuration;


@Configuration
public class JimConfiguration {
//    @Bean
//    @ConfigurationProperties(prefix = "dong.jim.jim1")
//    public ConfigurableJimCluster cluster1() {
//        return JimClusterBuilder.create().build();
//    }
//
//    @Bean
//    @ConfigurationProperties(prefix = "dong.jim.jim2")
//    public ConfigurableJimCluster cluster2() {
//        return JimClusterBuilder.create().build();
//    }
}
