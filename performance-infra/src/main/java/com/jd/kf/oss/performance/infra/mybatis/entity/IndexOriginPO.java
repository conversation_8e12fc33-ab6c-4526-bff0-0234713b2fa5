package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标基础信息，数据平台推表使用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("index_origin")
public class IndexOriginPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 描述
     */
    private String description;

    /**
     * 指标平台url跳转id
     */
    private String indexPlatformUrlCode;

    /**
     * 指标类型
     */
    private Integer kpiType;
}
