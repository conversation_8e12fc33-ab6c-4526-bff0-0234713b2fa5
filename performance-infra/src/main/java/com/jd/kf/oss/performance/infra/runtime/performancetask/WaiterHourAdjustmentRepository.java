package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IWaiterHourAdjustmentRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.infra.mybatis.entity.WaiterHourAdjustmentPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IWaiterHourAdjustmentPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WaiterHourAdjustmentRepository implements IWaiterHourAdjustmentRepository {
    @Resource
    private IWaiterHourAdjustmentPOService waiterHourAdjustmentPOService;
    @Override
    public List<WaiterHourAdjustment> query(String tenantCode, String period, List<String> erps) {
        if (CollectionUtils.isEmpty(erps)) {
            return Lists.newArrayList();
        }
        List<WaiterHourAdjustmentPO> adjustmentPOS = waiterHourAdjustmentPOService.lambdaQuery()
                .eq(WaiterHourAdjustmentPO::getTenantCode, tenantCode)
                .eq(WaiterHourAdjustmentPO::getPeriod, period)
                .in(WaiterHourAdjustmentPO::getErp, erps)
                .list();
        if (CollectionUtils.isEmpty(adjustmentPOS)) {
            return Lists.newArrayList();
        }
        return WaiterHourAdjustmentConverter.INSTANCE.poList2DOList(adjustmentPOS);
    }
}
