package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexDataPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息PO与DO转换器
 */
@Mapper
public interface PerformanceIndexDataConverter {
    
    PerformanceIndexDataConverter INSTANCE = Mappers.getMapper(PerformanceIndexDataConverter.class);

    /**
     * PO转DO
     */
    PerformanceIndexData po2DO(IndexDataPO userPO);

    /**
     * PO列表转DO列表
     */
    List<PerformanceIndexData> poList2DOList(List<IndexDataPO> userPOList);
}
