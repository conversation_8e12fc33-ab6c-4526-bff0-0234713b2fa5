package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 绩效方案
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Getter
@Setter
@TableName("plan")
public class PlanPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户标识
     */
    private String tenantCode;

    /**
     * 因子code
     */
    private String code;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 方案类型
     */
    private String type;

    private String formula;

    /**
     * 保留小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    /**
     * 公式展示信息
     */
    private String formulaDisplayInfo;

    /**
     * 公式描述信息
     */
    private String displayInfo;
}
