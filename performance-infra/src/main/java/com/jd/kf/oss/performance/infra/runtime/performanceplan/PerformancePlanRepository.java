package com.jd.kf.oss.performance.infra.runtime.performanceplan;

import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.IPerformancePlanRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformancePlanDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PlanPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IPlanPOService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class PerformancePlanRepository implements IPerformancePlanRepository {
    @Resource
    private IPlanPOService planPOService;

    @Override
    public PerformancePlanDO queryByCode(String tenantCode, String period, String code) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period, code)) {
            return null;
        }

        PlanPO planPO = planPOService.lambdaQuery()
                .eq(PlanPO::getTenantCode, tenantCode)
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getCode, code)
                .eq(PlanPO::getYn, true) // 查询未删除的记录
                .one();

        // 转换为领域对象
        return planPO != null ? convertToDO(planPO) : null;
    }

    private PerformancePlanDO convertToDO(PlanPO planPO) {
        if (Objects.isNull(planPO)) {
            return null;
        }
        PerformancePlanDO planDO = new PerformancePlanDO();

        // 手动映射基类属性
        planDO.setId(planPO.getId());
        planDO.setCode(planPO.getCode());
        planDO.setTenantCode(planPO.getTenantCode());
        planDO.setCreator(planPO.getCreator());
        planDO.setEditor(planPO.getEditor());
        planDO.setCreated(planPO.getCreated());
        planDO.setModified(planPO.getModified());

        // 映射PlanPO特有属性
        planDO.setName(planPO.getName());
        planDO.setType(planPO.getType());
        planDO.setFormula(planPO.getFormula());
        planDO.setDecimalPlaces(planPO.getDecimalPlaces());
        planDO.setRoundType(planPO.getRoundType());
        planDO.setPeriod(planPO.getPeriod());
        return planDO;
    }
}
