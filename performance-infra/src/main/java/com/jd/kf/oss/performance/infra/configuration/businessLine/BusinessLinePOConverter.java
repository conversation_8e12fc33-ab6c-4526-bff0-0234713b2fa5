package com.jd.kf.oss.performance.infra.configuration.businessLine;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.BusinessLinePO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BusinessLinePOConverter {
    BusinessLinePOConverter INSTANCE = Mappers.getMapper(BusinessLinePOConverter.class);

    /**
     * 转换PageCommand<BusinessLineDO>到PageCommand<BusinessLinePO>
     */
    PageCommand<BusinessLinePO> pageCommandDO2PO(PageCommand<BusinessLineDO> pageCommand);

    //TODO: URL的转换
    @Mapping(source = "records", target = "data")
    @Mapping(source = "current", target = "page")
    CommonPage<BusinessLineDO> pageP02DO(Page<BusinessLinePO> page);

    /**
     * 转换PO到DO
     */
    BusinessLinePO do2PO(BusinessLineDO businessLineDO);

    /**
     * 转换DO到PO
     */
    BusinessLineDO po2DO(BusinessLinePO businessLinePO);

    /**
     * 转换DO列表到PO列表
     */
    List<BusinessLineDO> poList2DOList(List<BusinessLinePO> businessLinePOList);

}
