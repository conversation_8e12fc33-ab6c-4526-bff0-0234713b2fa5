package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceFactorResultPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceFactorResultPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceFactorResultPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 因子结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
public class PerformanceFactorResultPOServiceImpl extends ServiceImpl<PerformanceFactorResultPOMapper, PerformanceFactorResultPO> implements IPerformanceFactorResultPOService {

}
