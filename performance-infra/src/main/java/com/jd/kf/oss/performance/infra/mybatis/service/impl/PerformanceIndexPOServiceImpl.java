package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceIndexPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceIndexPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceIndexPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指标信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class PerformanceIndexPOServiceImpl extends ServiceImpl<PerformanceIndexPOMapper, PerformanceIndexPO> implements IPerformanceIndexPOService {

}
