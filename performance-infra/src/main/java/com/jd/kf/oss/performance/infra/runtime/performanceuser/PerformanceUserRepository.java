package com.jd.kf.oss.performance.infra.runtime.performanceuser;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.IPerformanceUserRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.user.converter.UserPOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IUserPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PerformanceUserRepository implements IPerformanceUserRepository {
    @Resource
    private IUserPOService userPOService;

    @Override
    public List<PerformanceUserDO> queryAllUserByPeriodAndBusinessLineId(String businessLineId, String period) {
        List<UserPO> userPOList = userPOService.lambdaQuery()
                .eq(UserPO::getPeriod, period)
                .eq(UserPO::getBusinessLineId, businessLineId)
                .eq(UserPO::getYn, YnEnum.VALID.getValue())
                .list();
        if (CollectionUtils.isEmpty(userPOList)) {
            return Lists.newArrayList();
        }
        return convertToPerformanceUserDOS(userPOList);
    }

    @Override
    public List<PerformanceUserDO> queryUserByBusinessLineIdAndErps(String businessLineId, String period, List<String> erps) {
        if (StringUtils.isAnyEmpty(businessLineId, period) || CollectionUtils.isEmpty(erps)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(UserPO::getPeriod, period)
                .eq(UserPO::getBusinessLineId, businessLineId)
                .in(UserPO::getErp, erps)
                .eq(UserPO::getYn, YnEnum.VALID.getValue());

        List<UserPO> userPOList = userPOService.list(queryWrapper);
        if (CollectionUtils.isEmpty(userPOList)) {
            return Lists.newArrayList();
        }
        return convertToPerformanceUserDOS(userPOList);
    }

    private List<PerformanceUserDO> convertToPerformanceUserDOS(List<UserPO> userPOList) {
        if (CollectionUtils.isEmpty(userPOList)) {
            return Lists.newArrayList();
        }
        return userPOList.stream().map(PerformanceUserRepository::convertToPerformanceUserDO).collect(Collectors.toList());
    }

    private static PerformanceUserDO convertToPerformanceUserDO(UserPO userPO) {
        if (userPO == null) {
            return null;
        }

        PerformanceUserDO performanceUserDO = new PerformanceUserDO();
        // 继承自BaseEntity的属性
        performanceUserDO.setId(userPO.getId());
        performanceUserDO.setTenantCode(userPO.getTenantCode());
        performanceUserDO.setCreator(userPO.getCreator());
        performanceUserDO.setEditor(userPO.getEditor());
        performanceUserDO.setCreated(userPO.getCreated());
        performanceUserDO.setModified(userPO.getModified());

        // UserPO特有属性
        performanceUserDO.setBusinessLineId(userPO.getBusinessLineId());
        performanceUserDO.setDeptId(userPO.getDeptId());
        performanceUserDO.setDeptName(userPO.getDeptName());
        performanceUserDO.setEntryDate(userPO.getEntryDate());
        performanceUserDO.setManagerErp(userPO.getManagerErp());
        performanceUserDO.setQuitTime(userPO.getQuitTime());
        performanceUserDO.setErp(userPO.getErp());
        performanceUserDO.setName(userPO.getName());
        performanceUserDO.setType(userPO.getType());
        performanceUserDO.setPeriod(userPO.getPeriod());

        return performanceUserDO;
    }
}
