package com.jd.kf.oss.performance.infra.configuration.coefficient;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientItem;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceCoefficientDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceCoefficientItem;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientItemPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientPO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DOConverter {
    DOConverter INSTANCE = Mappers.getMapper(DOConverter.class);

    /**
     * 转换PO到DO
     */
    @Mapping(source="type",target = "type",qualifiedByName = "typeStringToEnum")
    CoefficientDO po2DO(CoefficientPO coefficientPO);

    /**
     * 转换PO到DO
     */
    @Mapping(source="type",target = "type",qualifiedByName = "typeStringToEnum")
    PerformanceCoefficientDO po2CoefficientDO(CoefficientPO coefficientPO);

    /**
     * 转换DO到PO
     */
    @Mapping(source="type",target = "type",qualifiedByName = "typeEnumToString")
    CoefficientPO do2PO(CoefficientDO coefficientDO);

    /**
     * 转换CoefficientItemPO到CoefficientItem
     */
    List<CoefficientItem> itemPO2DO(List<CoefficientItemPO> coefficientItemPOS);


    /**
     * 转换CoefficientItemPO到CoefficientItem
     */
    List<PerformanceCoefficientItem> itemPO2CoefficientItemDO(List<CoefficientItemPO> coefficientItemPOS);

    /**
     * 转换CoefficientItem到CoefficientItemPO
     */
    List<CoefficientItemPO> itemDO2PO(List<CoefficientItem> coefficientItemDOS);


    List<CoefficientDO> poList2DOList( List<CoefficientPO> coefficientPOS);
    /**
     * 分页转换
     */
    @Mapping(source = "records", target = "data")
    @Mapping(source = "current", target = "page")
    CommonPage<CoefficientDO> pagePO2DO(Page<CoefficientPO> mybatisPage);

    @Named( "typeStringToEnum")
    default CoefficientTemplateTypeEnum typeStringToEnum(String type){
        return CoefficientTemplateTypeEnum.getByType(type);
    }
    @Named( "typeEnumToString")
    default String typeEnumToString(CoefficientTemplateTypeEnum type){
        return type == null ? null : type.getType();
    }

}

