package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.IndexBasicPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.IndexBasicPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexBasicPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指标基础信息，绩效平台查询使用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class IndexBasicPOServiceImpl extends ServiceImpl<IndexBasicPOMapper, IndexBasicPO> implements IIndexBasicPOService {

}
