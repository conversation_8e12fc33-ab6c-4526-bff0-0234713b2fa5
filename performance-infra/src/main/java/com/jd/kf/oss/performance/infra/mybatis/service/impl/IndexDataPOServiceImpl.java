package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.IndexDataPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.IndexDataPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexDataPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指标数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class IndexDataPOServiceImpl extends ServiceImpl<IndexDataPOMapper, IndexDataPO> implements IIndexDataPOService {

}
