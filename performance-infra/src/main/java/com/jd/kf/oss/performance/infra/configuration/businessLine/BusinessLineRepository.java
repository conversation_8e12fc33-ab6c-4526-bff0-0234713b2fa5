package com.jd.kf.oss.performance.infra.configuration.businessLine;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.IBusinessLineRepository;
import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.mybatis.entity.BusinessLinePO;
import com.jd.kf.oss.performance.infra.mybatis.service.IBusinessLinePOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.jd.kf.oss.performance.enums.ResultCodeEnum.DUPLICATE_BUSINESS_LINE_NAME;


@Slf4j
@Service
public class BusinessLineRepository implements IBusinessLineRepository {

    /**
     * 业务线ID生成基数
     */
    private static final int BUSINESS_LINE_ID_BASE = 2000;

    @Autowired
    private IBusinessLinePOService businessLinePOService;


    /**
     * 根据租户标识、编辑人和名称分页查询绩效组
     */
    @Override
    public CommonPage<BusinessLineDO> queryBusinessLineByTenantAndEditorAndName(String tenantCode, String editor, String name, List<String> businessLineIds, int pageNum, int pageSize) {
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        Page<BusinessLinePO> businessLinePOPage = PageUtil.toMybatisPage(pageNum, pageSize);
        LambdaQueryWrapper<BusinessLinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLinePO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(tenantCode), BusinessLinePO::getTenantCode, tenantCode)
                .eq(StringUtils.isNotBlank(editor), BusinessLinePO::getEditor, editor)
                .in(CollectionUtils.isNotEmpty(businessLineIds), BusinessLinePO::getBusinessLineId, businessLineIds)
                .like(StringUtils.isNotBlank(name), BusinessLinePO::getName, name)
                .orderByDesc(BusinessLinePO::getModified)
                .orderByDesc(BusinessLinePO::getCreated);

        Page<BusinessLinePO> page = businessLinePOService.page(businessLinePOPage, queryWrapper);
        return BusinessLinePOConverter.INSTANCE.pageP02DO(page);
    }

    /**
     * @param businessLineDO 新建BusinessLine
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(BusinessLineDO businessLineDO) {
        BusinessLinePO businessLinePO = BusinessLinePOConverter.INSTANCE.do2PO(businessLineDO);

        try {
            // 先按照现有逻辑保存数据
            boolean result = businessLinePOService.save(businessLinePO);

            // 保存失败直接返回
            if (!result) {
                return result;
            }

            // 获取主键ID
            Long primaryId = businessLinePO.getId();
            if (primaryId == null) {
                return result;
            }

            // 使用主键ID生成businessLineId（格式：002001, 002002...）
            String businessLineId = String.format("%d", primaryId + BUSINESS_LINE_ID_BASE);

            // 更新businessLineId字段
            LambdaUpdateWrapper<BusinessLinePO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BusinessLinePO::getId, primaryId)
                       .set(BusinessLinePO::getBusinessLineId, businessLineId);
            businessLinePOService.update(updateWrapper);

            // 同步更新DO对象中的businessLineId
            businessLineDO.setBusinessLineId(businessLineId);

            return result;
        } catch (DuplicateKeyException e) {
            log.error("新增绩效组失败，存在唯一键冲突: {}", e.getMessage(), e);
            throw new IllegalArgumentException("保存失败，绩效组名称存在重复");
        }
    }

    /**
     * 更新业务线信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBusinessLine(BusinessLineDO businessLineDO) {
        BusinessLinePO businessLinePO = BusinessLinePOConverter.INSTANCE.do2PO(businessLineDO);
        return this.update(businessLinePO);
    }

    /**
     * 更新BusinessLine信息
     *
     * @param businessLinePO
     * @return
     */
    public boolean update(BusinessLinePO businessLinePO) {
        try {
            LambdaUpdateWrapper<BusinessLinePO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BusinessLinePO::getYn, YnEnum.VALID.getValue())
                    .eq(StringUtils.isNotBlank(businessLinePO.getBusinessLineId()), BusinessLinePO::getBusinessLineId,
                            businessLinePO.getBusinessLineId())
                    .set(BusinessLinePO::getName, businessLinePO.getName())
                    .set(StringUtils.isNotBlank(businessLinePO.getDescription()), BusinessLinePO::getDescription,
                            businessLinePO.getDescription());
            return businessLinePOService.update(businessLinePO, updateWrapper);
        } catch (DuplicateKeyException e) {
            log.error("更新绩效组失败，存在唯一键冲突: {}", e.getMessage(), e);
            throw new IllegalArgumentException("更新失败，绩效组名称存在重复");
        }
    }

    /**
     * 查询所有绩效组信息
     */
    @Override
    public List<BusinessLineDO> queryAllBusinessLine() {
        LambdaQueryWrapper<BusinessLinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLinePO::getYn, YnEnum.VALID.getValue());
        List<BusinessLinePO> poList = businessLinePOService.list(queryWrapper);
        return poList.stream().map(po -> {
            return convertBusinessLinePO(po);
        }).collect(Collectors.toList());
    }

    /**
     * 查询所有绩效组信息
     */
    @Override
    public List<BusinessLineDO> queryAllBusinessLineWithTenantCode() {
        LambdaQueryWrapper<BusinessLinePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessLinePO::getYn, YnEnum.VALID.getValue());
        List<BusinessLinePO> poList = businessLinePOService.list(queryWrapper);
        return poList.stream().map(BusinessLineRepository::convertBusinessLinePO).collect(Collectors.toList());

    }

    @NotNull
    private static BusinessLineDO convertBusinessLinePO(BusinessLinePO po) {
        BusinessLineDO businessLineDO = new BusinessLineDO();
        businessLineDO.setId(po.getId());
        businessLineDO.setTenantCode(po.getTenantCode());
        businessLineDO.setBusinessLineId(po.getBusinessLineId());
        businessLineDO.setName(po.getName());
        businessLineDO.setDescription(po.getDescription());
        businessLineDO.setCreated(po.getCreated());
        businessLineDO.setModified(po.getModified());
        businessLineDO.setCreator(po.getCreator());
        businessLineDO.setEditor(po.getEditor());
        return businessLineDO;
    }

    /**
     * 根据业务线ID查询业务线信息
     * @param businessLineId 业务线ID
     * @return 业务线DO对象
     */
    public BusinessLineDO queryBusinessLineByLineId(String businessLineId){
        BusinessLinePO po = businessLinePOService.lambdaQuery().eq(BusinessLinePO::getBusinessLineId, businessLineId).eq(BusinessLinePO::getYn, YnEnum.VALID.getValue()).one();
        return BusinessLinePOConverter.INSTANCE.po2DO(po);

    }

    /**
     * 根据业务线ID列表查询业务线信息
     * @param businessLineIds 业务线ID列表
     * @return 业务线DO对象列表
     */
    public List<BusinessLineDO> selectBusinessLineByLineIds(List<String> businessLineIds){
        if(businessLineIds == null || businessLineIds.isEmpty()) {
            return new ArrayList<>(0);
        }

        List<BusinessLinePO> pos = businessLinePOService.lambdaQuery()
                .in(BusinessLinePO::getBusinessLineId, businessLineIds)
                .eq(BusinessLinePO::getYn, YnEnum.VALID.getValue()).list();
        return BusinessLinePOConverter.INSTANCE.poList2DOList(pos);

    }
}
