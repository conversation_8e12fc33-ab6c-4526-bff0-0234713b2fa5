package com.jd.kf.oss.performance.infra.configuration.factor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.IFactorRepository;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.mybatis.entity.FactorPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IFactorPOService;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jd.kf.oss.performance.enums.ResultCodeEnum.VALIDATE_CHECK_ERROR;

/**
 * 因子仓储实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class FactorRepository implements IFactorRepository {

    @Resource
    private IFactorPOService factorPOService;

    /**
     * 保存或更新因子信息
     * @param factorDO 因子领域对象
     * @return 保存是否成功
     */
    @Override
    public boolean save(FactorDO factorDO) {
        if (factorDO == null) {
            return false;
        }
        FactorPO factorPO = convertToPO(factorDO);
        if (checkExist(factorPO)) {
            throw new BizException(VALIDATE_CHECK_ERROR.getCode(), "保存失败，因子名称存在重复");
        }
        // 使用MyBatis-Plus的IService接口方法
        if (factorDO.getId() != null && factorDO.getId() > 0) {
            return factorPOService.updateById(factorPO);
        } else {
            boolean success = factorPOService.save(factorPO);
            if (success) {
                factorDO.setId(factorPO.getId()); // 回写ID
            }
            return success;
        }
    }

    @Override
    public boolean delete(FactorDO factorDO) {
        return factorPOService.removeById(factorDO.getId());
    }

    @Override
    public FactorDO queryByCode(String tenantCode, String period, String code) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period, code)) {
            return null;
        }

        FactorPO factorPO = factorPOService.lambdaQuery()
                .eq(FactorPO::getTenantCode, tenantCode)
                .eq(FactorPO::getPeriod, period)
                .eq(FactorPO::getCode, code)
                .eq(FactorPO::getYn, true) // 查询未删除的记录
                .one();

        // 转换为领域对象
        return factorPO != null ? convertToDO(factorPO) : null;
    }

    private Boolean checkExist(FactorPO factorPO) {
        List<FactorPO> exists = factorPOService.lambdaQuery()
                .eq(FactorPO::getTenantCode, factorPO.getTenantCode())
                .eq(FactorPO::getPeriod, factorPO.getPeriod())
                .eq(FactorPO::getName, factorPO.getName()).list();
        exists = exists.stream().filter(a -> !a.getId().equals(factorPO.getId())).collect(Collectors.toList());
        return !CollectionUtils.isEmpty(exists);
    }

    @Override
    public List<FactorDO> queryAll(String tenantCode, String period) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period)) {
            return null;
        }

        List<FactorPO> factorPOS = factorPOService.lambdaQuery()
                .eq(FactorPO::getTenantCode, tenantCode)
                .eq(FactorPO::getPeriod, period)
                .eq(FactorPO::getYn, true) // 查询未删除的记录
                .list();

        if (CollectionUtils.isEmpty(factorPOS)) {
            return Lists.newArrayList();
        }
        // 转换为领域对象
        return factorPOS.stream().map(this::convertToDO).collect(Collectors.toList());
    }

    @Override
    public CommonPage<FactorDO> queryByName(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<FactorPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FactorPO::getTenantCode, tenantCode);
        queryWrapper.eq(FactorPO::getPeriod, period);
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(FactorPO::getType, type);
        }
        if (StringUtils.isNotBlank(factorName)) {
            queryWrapper.like(FactorPO::getName, factorName);
        }
        queryWrapper.orderByDesc(FactorPO::getModified);
        IPage<FactorPO> factorPOS = factorPOService.page(new Page<>(pageNum, pageSize), queryWrapper);
        CommonPage<FactorDO> result = new CommonPage<>();
        result.setTotal(factorPOS.getTotal());
        result.setPage(factorPOS.getPages());
        result.setSize(factorPOS.getSize());

        if (CollectionUtils.isNotEmpty(factorPOS.getRecords())) {
            result.setData(factorPOS.getRecords().stream().map(this::convertToDO).collect(Collectors.toList()));
        }
        // 转换为领域对象
        return result;
    }

    private FactorDO convertToDO(FactorPO factorPO) {
        FactorDO factorDO = new FactorDO();

        // 手动映射基类属性
        factorDO.setId(factorPO.getId());
        factorDO.setCode(factorPO.getCode());
        factorDO.setTenantCode(factorPO.getTenantCode());
        factorDO.setCreator(factorPO.getCreator());
        factorDO.setEditor(factorPO.getEditor());
        factorDO.setCreated(factorPO.getCreated());
        factorDO.setModified(factorPO.getModified());

        // 映射FactorPO特有属性
        factorDO.setName(factorPO.getName());
        factorDO.setType(factorPO.getType());
        factorDO.setFormula(factorPO.getFormula());
        factorDO.setDecimalPlaces(factorPO.getDecimalPlaces());
        factorDO.setRoundType(factorPO.getRoundType());
        factorDO.setPeriod(factorPO.getPeriod());
        factorDO.setStatus(factorPO.getStatus());
        factorDO.setFormulaDisplayInfo(factorPO.getFormulaDisplayInfo());
        factorDO.setDisplayInfo(factorPO.getDisplayInfo());
        return factorDO;
    }

    private FactorPO convertToPO(FactorDO factorDO) {
        FactorPO factorPO = new FactorPO();

        // 手动映射基类属性
        factorPO.setId(factorDO.getId());
        factorPO.setCode(factorDO.getCode());
        factorPO.setTenantCode(factorDO.getTenantCode());
        factorPO.setCreator(factorDO.getCreator());
        factorPO.setEditor(factorDO.getEditor());
        factorPO.setCreated(factorDO.getCreated());
        factorPO.setModified(factorDO.getModified());

        // 映射FactorDO特有属性
        factorPO.setName(factorDO.getName());
        factorPO.setType(factorDO.getType());
        factorPO.setFormula(factorDO.getFormula());
        factorPO.setDecimalPlaces(factorDO.getDecimalPlaces());
        factorPO.setRoundType(factorDO.getRoundType());
        factorPO.setPeriod(factorDO.getPeriod());
        factorPO.setStatus(factorDO.getStatus());
        factorPO.setFormulaDisplayInfo(factorDO.getFormulaDisplayInfo());
        factorPO.setDisplayInfo(factorDO.getDisplayInfo());
        // 处理特殊属性
        if (factorDO.getCode() == null && Objects.isNull(factorDO.getId())) {
            factorPO.setCode(factorDO.buildCode());
        }
        return factorPO;
    }

}
