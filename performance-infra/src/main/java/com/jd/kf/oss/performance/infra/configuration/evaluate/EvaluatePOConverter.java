package com.jd.kf.oss.performance.infra.configuration.evaluate;

import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.EvaluatePO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

@Mapper
public interface EvaluatePOConverter {
    EvaluatePOConverter INSTANCE = Mappers.getMapper(EvaluatePOConverter.class);

    EvaluatePO do2PO(EvaluateDO evaluateDO);
    EvaluateDO po2DO(EvaluatePO evaluatePO);


    PageCommand<EvaluatePO> pageCommandDO2PO(PageCommand<EvaluateDO> pageCommand);

    @Mapping(source = "records", target = "data")
    @Mapping(source = "current", target = "page")
    CommonPage<EvaluateDO> pageP02DO(Page<EvaluatePO> page);
}
