package com.jd.kf.oss.performance.infra.configuration.plan;

import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PlanPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DOConverter {
    DOConverter INSTANCE = Mappers.getMapper(DOConverter.class);

    PlanDO PO2DO(PlanPO planPO);

    List<PlanDO> POs2DOs(List<PlanPO> planPOs);
}
