package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.IPerformanceTaskRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskItemDO;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskStatusEnum;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTaskItemPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTaskPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTaskItemPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTaskPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PerformanceTaskRepository implements IPerformanceTaskRepository {

    @Autowired
    private IPerformanceTaskPOService performanceTaskPOService;

    @Autowired
    private IPerformanceTaskItemPOService performanceTaskItemPOService;

    @Transactional
    @Override
    public void save(PerformanceTaskDO task) {
        // 设置基础信息
        PerformanceTaskPO taskPO = convertToPO(task);
        performanceTaskPOService.saveOrUpdate(taskPO);
        task.setId(taskPO.getId());

        // 保存子任务项
        if (CollectionUtils.isNotEmpty(task.getItems())) {
            // 先删除旧的子任务项
            LambdaQueryWrapper<PerformanceTaskItemPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PerformanceTaskItemPO::getTaskId, task.getId());
            performanceTaskItemPOService.remove(wrapper);

            List<PerformanceTaskItemPO> items = task.getItems().stream().map(a -> convertToPO(taskPO.getId(), a)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                performanceTaskItemPOService.saveBatch(items);
            }
        }
    }

    @Override
    public PerformanceTaskDO query(String tenantCode, String period) {
        List<PerformanceTaskPO> performanceTaskPOS = performanceTaskPOService.lambdaQuery()
                .eq(PerformanceTaskPO::getTenantCode, tenantCode)
                .eq(PerformanceTaskPO::getPeriod, period)
                .in(PerformanceTaskPO::getStatus, Lists.newArrayList(TaskStatusEnum.NOT_STARTED.name(),TaskStatusEnum.RUNNING.name()))
                .list();
        if (CollectionUtils.isEmpty(performanceTaskPOS)) {
            return null;
        }
        PerformanceTaskPO taskPO = performanceTaskPOS.get(0);
        List<PerformanceTaskItemPO> performanceTaskItemPOS = performanceTaskItemPOService.lambdaQuery()
                .eq(PerformanceTaskItemPO::getTenantCode, tenantCode)
                .eq(PerformanceTaskItemPO::getPeriod, period)
                .eq(PerformanceTaskItemPO::getTaskId, taskPO.getId())
                .eq(PerformanceTaskItemPO::getYn, true)
                .list();
        return convert(taskPO, performanceTaskItemPOS);
    }

    private PerformanceTaskDO convert(PerformanceTaskPO taskPO, List<PerformanceTaskItemPO> performanceTaskItemPOS) {
        PerformanceTaskDO performanceTaskDO = convertToDO(taskPO);
        List<PerformanceTaskItemDO> taskItemDOS = performanceTaskItemPOS.stream().map(this::convertToDO).collect(Collectors.toList());
        performanceTaskDO.setItems(taskItemDOS);
        return performanceTaskDO;
    }

    private PerformanceTaskPO convertToPO(PerformanceTaskDO taskDO) {
        PerformanceTaskPO taskPO = new PerformanceTaskPO();

        // 手动映射基类属性
        taskPO.setId(taskDO.getId());
        taskPO.setCreator(taskDO.getCreator());
        taskPO.setEditor(taskDO.getEditor());
        taskPO.setCreated(taskDO.getCreated());
        taskPO.setModified(taskDO.getModified());
        taskPO.setPeriod(taskDO.getPeriod());
        taskPO.setStatus(taskDO.getStatus().name());
        return taskPO;
    }

    private PerformanceTaskDO convertToDO(PerformanceTaskPO taskPO) {
        PerformanceTaskDO taskDO = new PerformanceTaskDO();

        // 手动映射基类属性
        taskDO.setId(taskDO.getId());
        taskDO.setPeriod(taskDO.getPeriod());
        taskDO.setTenantCode(taskDO.getTenantCode());
        taskDO.setCreator(taskDO.getCreator());
        taskDO.setEditor(taskDO.getEditor());
        taskDO.setCreated(taskDO.getCreated());
        taskDO.setModified(taskDO.getModified());
        taskDO.setPeriod(taskDO.getPeriod());
        taskDO.setStatus(TaskStatusEnum.valueOf(taskPO.getStatus()));
        return taskDO;
    }



    private PerformanceTaskItemPO convertToPO(Long taskId, PerformanceTaskItemDO taskItem) {
        PerformanceTaskItemPO taskItemPO = new PerformanceTaskItemPO();

        taskItemPO.setId(taskItem.getId());
        taskItemPO.setCreator(taskItem.getCreator());
        taskItemPO.setEditor(taskItem.getEditor());
        taskItemPO.setCreated(taskItem.getCreated());
        taskItemPO.setModified(taskItem.getModified());
        taskItemPO.setPeriod(taskItem.getPeriod());
        taskItemPO.setTaskId(taskId);
        taskItemPO.setBusinessLineId(taskItem.getBusinessLineId());
        taskItemPO.setBusinessLineName(taskItem.getBusinessLineName());
        taskItemPO.setStatus(taskItem.getStatus().name());
        return taskItemPO;
    }

    private PerformanceTaskItemDO convertToDO(PerformanceTaskItemPO taskItem) {
        PerformanceTaskItemDO taskItemDO = new PerformanceTaskItemDO();

        taskItemDO.setId(taskItem.getId());
        taskItemDO.setTenantCode(taskItem.getTenantCode());
        taskItemDO.setCreator(taskItem.getCreator());
        taskItemDO.setEditor(taskItem.getEditor());
        taskItemDO.setCreated(taskItem.getCreated());
        taskItemDO.setModified(taskItem.getModified());
        taskItemDO.setPeriod(taskItem.getPeriod());
        taskItemDO.setTaskId(taskItem.getTaskId());
        taskItemDO.setBusinessLineId(taskItem.getBusinessLineId());
        taskItemDO.setBusinessLineName(taskItem.getBusinessLineName());
        taskItemDO.setStatus(TaskItemStatusEnum.valueOf(taskItem.getStatus()));
        return taskItemDO;
    }

    @Override
    public void cancelUnstartedTasks(String tenantCode, String period) {
        LambdaQueryWrapper<PerformanceTaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceTaskPO::getYn, YnEnum.VALID.getValue())
                .eq(PerformanceTaskPO::getTenantCode, tenantCode)
                .eq(PerformanceTaskPO::getPeriod, period);
        List<PerformanceTaskPO> performanceTaskPOS = performanceTaskPOService.list(queryWrapper);
        cancelTask(performanceTaskPOS);
    }

    @Override
    public boolean changeStatus(PerformanceTaskItemDO performanceTaskItemDO, TaskItemStatusEnum pre, TaskItemStatusEnum after) {
        if (Objects.isNull(performanceTaskItemDO) || Objects.isNull(performanceTaskItemDO.getId()) || Objects.isNull(pre) || Objects.isNull(after)) {
            return false;
        }
        boolean flag = performanceTaskItemPOService.lambdaUpdate()
                .eq(PerformanceTaskItemPO::getId, performanceTaskItemDO.getId())
                .eq(PerformanceTaskItemPO::getStatus, pre.name())
                .set(PerformanceTaskItemPO::getStatus, after.name())
                .update();
        if (flag) {
            performanceTaskItemDO.setStatus(after);
        }
        return flag;
    }

    private void cancelTask(List<PerformanceTaskPO> performanceTaskPOS) {
        if (CollectionUtils.isEmpty(performanceTaskPOS)) {
            return;
        }
        List<Long> taskIds = performanceTaskPOS.stream().map(PerformanceTaskPO::getId).collect(Collectors.toList());
        performanceTaskPOService.lambdaUpdate()
                .set(PerformanceTaskPO::getStatus, TaskStatusEnum.CANCELLED.name())
                .in(PerformanceTaskPO::getId, taskIds)
                .update();

        performanceTaskItemPOService.lambdaUpdate()
                .set(PerformanceTaskItemPO::getStatus, TaskStatusEnum.CANCELLED.name())
                .in(PerformanceTaskItemPO::getTaskId, taskIds)
                .update();
    }
}
