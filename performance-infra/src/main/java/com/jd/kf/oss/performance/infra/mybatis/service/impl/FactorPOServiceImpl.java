package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.FactorPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.FactorPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IFactorPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 因子信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
public class FactorPOServiceImpl extends ServiceImpl<FactorPOMapper, FactorPO> implements IFactorPOService {

}
