package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.WaiterHourAdjustmentPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.WaiterHourAdjustmentPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IWaiterHourAdjustmentPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指标数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public class WaiterHourAdjustmentPOServiceImpl extends ServiceImpl<WaiterHourAdjustmentPOMapper, WaiterHourAdjustmentPO> implements IWaiterHourAdjustmentPOService {

}
