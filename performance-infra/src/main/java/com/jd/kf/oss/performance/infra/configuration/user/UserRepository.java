package com.jd.kf.oss.performance.infra.configuration.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.component.dal.util.StringUtils;
import com.jd.kf.oss.performance.domain.config.domain.user.IUserRepository;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.user.converter.UserPOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.UserPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IUserPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息仓储实现类 - 处理涉及多表关联的复杂查询
 */
@Slf4j
@Service
public class UserRepository implements IUserRepository {

    @Autowired
    private IUserPOService userPOService;

    @Autowired
    private UserPOMapper userPOMapper;

    @Autowired
    private IWfcDeptPOService wfcDeptPOService;

    /**
     * 根据条件分页查询用户信息（涉及多表关联查询，不包含部门路径信息）
     * 关联表：user、performance_target
     * 注意：此方法不会设置部门路径信息，如需完整信息请使用UserAggregateService
     */
    @Override
    public CommonPage<UserDO> queryUserInfoByConditions(String tenantCode, String period, String managerErp,
                                                        String deptId, String businessLineId, String planCode,
                                                        String erp, String name, int pageNum, int pageSize) {
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
        CheckUtil.notBlank(tenantCode, "租户标识不能为空");

        // 处理部门ID查询逻辑：如果传入deptId，则查询该部门及其所有下级部门
        List<String> deptIdList = null;
        if (Strings.isNotBlank(deptId)) {
            try {
                deptIdList = wfcDeptPOService.querySubDeptIdsByParentDeptId(tenantCode, deptId);
                log.info("根据上级部门ID {} 查询到下级部门ID列表: {}", deptId, deptIdList);
            } catch (Exception e) {
                log.warn("查询下级部门ID失败，使用原始deptId进行查询, deptId: {}", deptId, e);
                // 如果查询失败，仍使用原始的deptId进行查询
            }
        }

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("tenantCode", tenantCode);
        params.put("period", period);
        params.put("managerErp", managerErp);
        // 如果查询到了下级部门列表，则使用列表查询；否则使用原始deptId
        if (deptIdList != null && !deptIdList.isEmpty()) {
            params.put("deptIdList", deptIdList);
        } else {
            params.put("deptId", deptId);
        }
        params.put("erp", erp);
        params.put("businessLineId", businessLineId);
        params.put("evaluationPlanCode", planCode);
        params.put("name", name);
        params.put("offset", (pageNum - 1) * pageSize);
        params.put("limit", pageSize);

        try {
            // 查询总数
            Long total = userPOMapper.countUserInfoByConditions(params);
            
            // 查询数据列表
            List<UserDO> userDOList = userPOMapper.queryUserInfoByConditions(params);
            return CommonPage.getCommonPage(Long.valueOf(pageNum), Long.valueOf(pageSize), total, userDOList);
        } catch (Exception e) {
            log.error("查询用户信息失败, params: {}", params, e);
            throw new RuntimeException("查询用户信息失败", e);
        }
    }

    /**
     * 查询当前绩效月的所有用户数据
     */
    @Override
    public List<UserDO> queryAllUserByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");

        try {
            LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserPO::getPeriod, period)
                       .eq(UserPO::getYn, YnEnum.VALID.getValue());

            List<UserPO> userPOList = userPOService.list(queryWrapper);
            return UserPOConverter.INSTANCE.poList2DOList(userPOList);
        } catch (Exception e) {
            log.error("查询绩效月用户数据失败, period: {}", period, e);
            throw new RuntimeException("查询绩效月用户数据失败", e);
        }
    }

    /**
     * 从WFC表中查询所有有效用户数据（关联wfc_user、wfc_user_ext、wfc_dept）
     */
    @Override
    public List<UserDO> queryAllWfcUserData() {
        try {
            return userPOMapper.queryAllWfcUserData();
        } catch (Exception e) {
            log.error("查询WFC用户数据失败", e);
            throw new RuntimeException("查询WFC用户数据失败", e);
        }
    }

    /**
     * 批量保存用户数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatchUsers(List<UserDO> userList) {
        CheckUtil.notEmpty(userList, "用户列表不能为空");

        try {
            List<UserPO> userPOList = UserPOConverter.INSTANCE.doList2POList(userList);
            boolean result = userPOService.saveBatch(userPOList);
            return result ? userPOList.size() : 0;
        } catch (Exception e) {
            log.error("批量保存用户数据失败, size: {}", userList.size(), e);
            throw new RuntimeException("批量保存用户数据失败", e);
        }
    }

    /**
     * 批量更新用户数据（通过erp、tenantCode、period条件更新dept_id和businessLineId）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBatchUsers(List<UserDO> userList) {
        CheckUtil.notEmpty(userList, "用户列表不能为空");

        try {
            List<UserPO> userPOList = UserPOConverter.INSTANCE.doList2POList(userList);
            int result = userPOService.batchUpdateByCondition(userPOList);
            return result;
        } catch (Exception e) {
            log.error("批量更新用户数据失败, size: {}", userList.size(), e);
            throw new RuntimeException("批量更新用户数据失败", e);
        }
    }


    /**
     * 根据ERP账号列表查询用户信息
     *
     * @param erps                     ERP账号列表
     * @param currentPerformancePeriod
     * @return 查询到的用户列表，若无结果返回空列表
     */
    @Override
    public List<UserDO> queryUsersByErp(List<String> erps, String currentPerformancePeriod) {
        if (erps == null || erps.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserPO::getErp, erps)
                .eq(UserPO::getPeriod, currentPerformancePeriod)
                .eq(UserPO::getYn, YnEnum.VALID.getValue());

        List<UserPO> userPOList = userPOService.list(queryWrapper);
        return UserPOConverter.INSTANCE.poList2DOList(userPOList);
    }

}
