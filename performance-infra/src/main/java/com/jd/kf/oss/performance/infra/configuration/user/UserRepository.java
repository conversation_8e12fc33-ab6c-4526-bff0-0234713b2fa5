package com.jd.kf.oss.performance.infra.configuration.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.component.dal.util.StringUtils;
import com.jd.kf.oss.performance.domain.config.domain.user.IUserRepository;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.cache.DeptHierarchyCache;
import com.jd.kf.oss.performance.infra.configuration.user.converter.UserPOConverter;
import com.jd.kf.oss.performance.infra.monitor.QueryPerformanceMonitor;
import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.UserPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IUserPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息仓储实现类 - 处理涉及多表关联的复杂查询
 */
@Slf4j
@Service
public class UserRepository implements IUserRepository {

    @Autowired
    private IUserPOService userPOService;

    @Autowired
    private UserPOMapper userPOMapper;

    @Autowired
    private IWfcDeptPOService wfcDeptPOService;

    @Autowired
    private DeptHierarchyCache deptHierarchyCache;

    @Autowired
    private QueryPerformanceMonitor performanceMonitor;

    /**
     * 根据条件分页查询用户信息（涉及多表关联查询，不包含部门路径信息）
     * 关联表：user、performance_target
     * 注意：此方法不会设置部门路径信息，如需完整信息请使用UserAggregateService
     */
    @Override
    public CommonPage<UserDO> queryUserInfoByConditions(String tenantCode, String period, String managerErp,
                                                        String deptId, String businessLineId, String planCode,
                                                        String erp, String name, int pageNum, int pageSize) {
        // 快速参数校验（避免多次方法调用）
        if (pageNum <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("分页参数必须大于0");
        }
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            throw new IllegalArgumentException("租户标识不能为空");
        }

        // 优化：使用缓存的部门层级查询
        List<String> deptIdList = getDeptIdListWithCache(tenantCode, deptId);

        // 优化：预分配HashMap容量，避免扩容
        Map<String, Object> params = buildQueryParams(tenantCode, period, managerErp,
                deptIdList, deptId, erp, businessLineId, planCode, name, pageNum, pageSize);

        // 优化：使用单次查询获取总数和数据
        return executeOptimizedQuery(params, pageNum, pageSize);
    }

    /**
     * 使用缓存的部门层级查询（优化版本）
     */
    private List<String> getDeptIdListWithCache(String tenantCode, String deptId) {
        if (Strings.isBlank(deptId)) {
            return null;
        }

        // 1. 先从缓存获取
        List<String> cachedResult = deptHierarchyCache.getSubDeptIds(tenantCode, deptId);
        if (cachedResult != null) {
            if (log.isDebugEnabled()) {
                log.debug("部门层级缓存命中 - 上级部门: {}, 下级部门数量: {}", deptId, cachedResult.size());
            }
            return cachedResult.isEmpty() ? null : cachedResult;
        }

        // 2. 缓存未命中，查询数据库
        try {
            List<String> deptIdList = wfcDeptPOService.querySubDeptIdsByParentDeptId(tenantCode, deptId);

            // 3. 将结果放入缓存
            deptHierarchyCache.putSubDeptIds(tenantCode, deptId, deptIdList);

            if (log.isDebugEnabled()) {
                log.debug("查询部门层级并缓存 - 上级部门: {}, 下级部门数量: {}", deptId,
                         deptIdList != null ? deptIdList.size() : 0);
            }

            return deptIdList;
        } catch (Exception e) {
            log.warn("查询下级部门ID失败，使用原始deptId: {}", deptId, e);
            // 缓存空结果，避免重复查询失败的情况
            deptHierarchyCache.putSubDeptIds(tenantCode, deptId, null);
            return null;
        }
    }

    /**
     * 构建查询参数（优化版本）
     */
    private Map<String, Object> buildQueryParams(String tenantCode, String period, String managerErp,
                                                List<String> deptIdList, String deptId, String erp,
                                                String businessLineId, String planCode, String name,
                                                int pageNum, int pageSize) {
        // 预分配容量，避免HashMap扩容
        Map<String, Object> params = new HashMap<>(12);

        // 必填参数
        params.put("tenantCode", tenantCode);
        params.put("offset", (pageNum - 1) * pageSize);
        params.put("limit", pageSize);

        // 可选参数（只有非空时才添加，减少Map大小）
        if (Strings.isNotBlank(period)) {
            params.put("period", period);
        }
        if (Strings.isNotBlank(managerErp)) {
            params.put("managerErp", managerErp);
        }
        if (Strings.isNotBlank(erp)) {
            params.put("erp", erp);
        }
        if (Strings.isNotBlank(businessLineId)) {
            params.put("businessLineId", businessLineId);
        }
        if (Strings.isNotBlank(planCode)) {
            params.put("evaluationPlanCode", planCode);
        }
        if (Strings.isNotBlank(name)) {
            params.put("name", name);
        }

        // 部门ID处理
        if (deptIdList != null && !deptIdList.isEmpty()) {
            params.put("deptIdList", deptIdList);
        } else if (Strings.isNotBlank(deptId)) {
            params.put("deptId", deptId);
        }

        return params;
    }

    /**
     * 执行优化的查询（考虑是否需要总数查询）
     */
    private CommonPage<UserDO> executeOptimizedQuery(Map<String, Object> params, int pageNum, int pageSize) {
        return performanceMonitor.executeWithMonitoring("user_query_with_conditions", () -> {
            // 优化：对于第一页且页面大小较小的查询，可以考虑先查数据再决定是否需要总数
            boolean needCount = pageNum > 1 || pageSize > 50; // 可配置的阈值

            Long total;
            List<UserDO> userDOList;

            if (needCount) {
                // 需要总数的情况：分别执行count和data查询
                total = performanceMonitor.executeWithMonitoring("user_count_query",
                    () -> userPOMapper.countUserInfoByConditions(params));

                userDOList = performanceMonitor.executeWithMonitoring("user_data_query",
                    () -> userPOMapper.queryUserInfoByConditions(params));
            } else {
                // 不需要总数的情况：先查数据，根据结果决定是否查总数
                userDOList = performanceMonitor.executeWithMonitoring("user_data_query",
                    () -> userPOMapper.queryUserInfoByConditions(params));

                // 如果返回的数据量等于页面大小，说明可能还有更多数据，需要查询总数
                if (userDOList.size() == pageSize) {
                    total = performanceMonitor.executeWithMonitoring("user_count_query",
                        () -> userPOMapper.countUserInfoByConditions(params));
                } else {
                    // 数据量小于页面大小，说明这是最后一页，总数就是当前数据量
                    total = (long) userDOList.size();
                }
            }

            return CommonPage.getCommonPage((long) pageNum, (long) pageSize, total, userDOList);
        });
    }

    /**
     * 查询当前绩效月的所有用户数据
     */
    @Override
    public List<UserDO> queryAllUserByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");

        try {
            LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserPO::getPeriod, period)
                       .eq(UserPO::getYn, YnEnum.VALID.getValue());

            List<UserPO> userPOList = userPOService.list(queryWrapper);
            return UserPOConverter.INSTANCE.poList2DOList(userPOList);
        } catch (Exception e) {
            log.error("查询绩效月用户数据失败, period: {}", period, e);
            throw new RuntimeException("查询绩效月用户数据失败", e);
        }
    }

    /**
     * 从WFC表中查询所有有效用户数据（关联wfc_user、wfc_user_ext、wfc_dept）
     */
    @Override
    public List<UserDO> queryAllWfcUserData() {
        try {
            return userPOMapper.queryAllWfcUserData();
        } catch (Exception e) {
            log.error("查询WFC用户数据失败", e);
            throw new RuntimeException("查询WFC用户数据失败", e);
        }
    }

    /**
     * 批量保存用户数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatchUsers(List<UserDO> userList) {
        CheckUtil.notEmpty(userList, "用户列表不能为空");

        try {
            List<UserPO> userPOList = UserPOConverter.INSTANCE.doList2POList(userList);
            boolean result = userPOService.saveBatch(userPOList);
            return result ? userPOList.size() : 0;
        } catch (Exception e) {
            log.error("批量保存用户数据失败, size: {}", userList.size(), e);
            throw new RuntimeException("批量保存用户数据失败", e);
        }
    }

    /**
     * 批量更新用户数据（通过erp、tenantCode、period条件更新dept_id和businessLineId）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateBatchUsers(List<UserDO> userList) {
        CheckUtil.notEmpty(userList, "用户列表不能为空");

        try {
            List<UserPO> userPOList = UserPOConverter.INSTANCE.doList2POList(userList);
            int result = userPOService.batchUpdateByCondition(userPOList);
            return result;
        } catch (Exception e) {
            log.error("批量更新用户数据失败, size: {}", userList.size(), e);
            throw new RuntimeException("批量更新用户数据失败", e);
        }
    }


    /**
     * 根据ERP账号列表查询用户信息
     *
     * @param erps                     ERP账号列表
     * @param currentPerformancePeriod
     * @return 查询到的用户列表，若无结果返回空列表
     */
    @Override
    public List<UserDO> queryUsersByErp(List<String> erps, String currentPerformancePeriod) {
        if (erps == null || erps.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserPO::getErp, erps)
                .eq(UserPO::getPeriod, currentPerformancePeriod)
                .eq(UserPO::getYn, YnEnum.VALID.getValue());

        List<UserPO> userPOList = userPOService.list(queryWrapper);
        return UserPOConverter.INSTANCE.poList2DOList(userPOList);
    }

}
