package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系数信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName(value = "coefficient" , autoResultMap = true)
public class CoefficientPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 系数id
     */
    private String code;

    /**
     * 系数名称
     */
    private String name;

    /**
     * 分段系数模板,月周期系数模板,常量系数模板
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
