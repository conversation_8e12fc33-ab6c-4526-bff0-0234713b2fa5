package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientItemPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.CoefficientItemPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientItemPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系数条目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class CoefficientItemPOServiceImpl extends ServiceImpl<CoefficientItemPOMapper, CoefficientItemPO> implements ICoefficientItemPOService {

}
