package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 绩效结果
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
@TableName("performance_result")
public class PerformanceResultPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 绩效条线
     */
    private String businessLineId;

    /**
     * 绩效条线名称
     */
    private String businessLineName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 类型：首次，申诉
     */
    private String type;

    /**
     * 绩效结果
     */
    private String result;

    /**
     * 详情
     */
    private String detail;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
