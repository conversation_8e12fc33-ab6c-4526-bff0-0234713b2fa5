package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserExtPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.WfcUserExtPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcUserExtPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 人员扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class WfcUserExtPOServiceImpl extends ServiceImpl<WfcUserExtPOMapper, WfcUserExtPO> implements IWfcUserExtPOService {

    @Resource
    private WfcUserExtPOMapper wfcUserExtPOMapper;

    @Override
    public List<WfcUserExtPO> getWfcUserExtListByErps(List<String> erpList) {
        if (CollectionUtils.isEmpty(erpList)) {
            return Collections.emptyList();
        }

        String tenantCode = UserContextHolder.getTenantCode();

        LambdaQueryWrapper<WfcUserExtPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WfcUserExtPO::getTenantCode, tenantCode)
                .in(WfcUserExtPO::getErp, erpList)
                .eq(WfcUserExtPO::getYn, true);

        return list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpsertUserExt(List<WfcUserExtPO> userExtList) {
        if (CollectionUtils.isEmpty(userExtList)) {
            log.info("WfcUserExtPOServiceImpl batchUpsertUserExt userExtList is empty");
            return;
        }

        try {
            saveOrUpdateBatch(userExtList);
        } catch (Exception e) {
            log.error("WfcUserExtPOServiceImpl batchUpsertUserExt error: {}", e.getMessage(), e);
            throw new RuntimeException("批量保存用户扩展信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRemoveUserExtById(List<WfcUserExtPO> userExtList) {
        if (CollectionUtils.isEmpty(userExtList)) {
            log.info("WfcUserExtPOServiceImpl batchRemoveUserExtById userExtList is empty");
            return;
        }

        try {
            List<Long> idList = userExtList.stream()
                    .map(WfcUserExtPO::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            wfcUserExtPOMapper.batchDeleteByIdList(idList);
        } catch (Exception e) {
            log.error("WfcUserExtPOServiceImpl batchRemoveUserExtById error: {}", e.getMessage(), e);
            throw new RuntimeException("批量逻辑删除用户扩展信息失败: " + e.getMessage(), e);
        }
    }

}
