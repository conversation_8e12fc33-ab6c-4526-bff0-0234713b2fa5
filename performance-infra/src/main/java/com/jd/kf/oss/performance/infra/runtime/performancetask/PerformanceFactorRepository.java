package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.IPerformanceFactorRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceFactor;
import com.jd.kf.oss.performance.infra.mybatis.entity.FactorPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IFactorPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PerformanceFactorRepository implements IPerformanceFactorRepository {
    @Resource
    private IFactorPOService factorPOService;
    @Override
    public List<PerformanceFactor> queryAll(String tenantCode, String period) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period)) {
            return null;
        }

        List<FactorPO> factorPOS = factorPOService.lambdaQuery()
                .eq(FactorPO::getTenantCode, tenantCode)
                .eq(FactorPO::getPeriod, period)
                .eq(FactorPO::getYn, true) // 查询未删除的记录
                .list();

        if (CollectionUtils.isEmpty(factorPOS)) {
            return Lists.newArrayList();
        }
        // 转换为领域对象
        return factorPOS.stream().map(this::convertToDO).collect(Collectors.toList());
    }

    private PerformanceFactor convertToDO(FactorPO factorPO) {
        PerformanceFactor factorDO = new PerformanceFactor();

        // 手动映射基类属性
        factorDO.setId(factorPO.getId());
        factorDO.setCode(factorPO.getCode());
        factorDO.setTenantCode(factorPO.getTenantCode());
        factorDO.setCreator(factorPO.getCreator());
        factorDO.setEditor(factorPO.getEditor());
        factorDO.setCreated(factorPO.getCreated());
        factorDO.setModified(factorPO.getModified());

        // 映射FactorPO特有属性
        factorDO.setName(factorPO.getName());
        factorDO.setType(factorPO.getType());
        factorDO.setFormula(factorPO.getFormula());
        factorDO.setDecimalPlaces(factorPO.getDecimalPlaces());
        factorDO.setRoundType(factorPO.getRoundType());
        factorDO.setPeriod(factorPO.getPeriod());
        return factorDO;
    }
}
