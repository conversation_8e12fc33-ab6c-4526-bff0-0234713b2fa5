package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("user")
public class UserPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;


    /**
     * 入职时间
     */
    private String entryDate;

    /**
     * 主管erp
     */
    private String managerErp;

    /**
     * 人资离职时间
     */
    private LocalDateTime quitTime;

    /**
     * 人员erp
     */
    private String erp;

    /**
     * 人员姓名
     */
    private String name;


    /**
     * 管理员/员工
     */
    private String type;

    /**
     * 绩效月
     */
    private String period;
}
