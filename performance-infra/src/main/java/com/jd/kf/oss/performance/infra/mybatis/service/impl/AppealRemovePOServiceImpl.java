package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.AppealRemovePO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.AppealRemovePOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IAppealRemovePOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 剔除单号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class AppealRemovePOServiceImpl extends ServiceImpl<AppealRemovePOMapper, AppealRemovePO> implements IAppealRemovePOService {

}
