package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标数据申诉
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("index_data_appeal")
public class IndexDataAppealPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 姓名
     */
    private String name;

    /**
     * 是否新老人
     */
    private String newOrOld;

    /**
     * 主管erp
     */
    private String manager;

    /**
     * 主管名字
     */
    private String managerName;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标name
     */
    private String kpiName;

    /**
     * 实际业务线
     */
    private String businessJxName;

    /**
     * 技能组ID
     */
    private String skillId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 分子
     */
    private String numerator;

    /**
     * 分母
     */
    private String denominator;

    /**
     * 是否主管
     */
    private Boolean managerFlag;

    /**
     * 是否全月假
     */
    private Boolean weatherFullMonthVacation;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;
}
