package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.UserPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IUserPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class UserPOServiceImpl extends ServiceImpl<UserPOMapper, UserPO> implements IUserPOService {

    @Resource
    private UserPOMapper userPOMapper;

    @Override
    public int batchUpdateByCondition(List<UserPO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            log.info("UserPOServiceImpl batchUpdateByCondition userList is empty");
            return 0;
        }

        try {
            int batchSize = 500;
            int totalSize = 0;
            for (int i = 0; i < userList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, userList.size());
                List<UserPO> batch = userList.subList(i, endIndex);
                totalSize += userPOMapper.batchUpdateByCondition(batch);
            }

            return totalSize;
        } catch (Exception e) {
            log.error("UserPOServiceImpl batchUpdateByCondition error: {}", e.getMessage(), e);
            throw new RuntimeException("批量更新用户信息失败: " + e.getMessage(), e);
        }
    }
}
