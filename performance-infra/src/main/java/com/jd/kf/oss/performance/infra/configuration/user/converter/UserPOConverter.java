package com.jd.kf.oss.performance.infra.configuration.user.converter;

import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息PO与DO转换器
 */
@Mapper
public interface UserPOConverter {
    
    UserPOConverter INSTANCE = Mappers.getMapper(UserPOConverter.class);

    /**
     * PO转DO
     */
    UserDO po2DO(UserPO userPO);

    /**
     * DO转PO
     */
    UserPO do2PO(UserDO userDO);

    /**
     * PO列表转DO列表
     */
    List<UserDO> poList2DOList(List<UserPO> userPOList);

    /**
     * PO列表转DO列表
     */
    List<PerformanceUserDO> userPOList2DOList(List<UserPO> userPOList);

    /**
     * PO列表转DO列表
     */
    PerformanceUserDO userPOList2DO(UserPO userPO);

    /**
     * DO列表转PO列表
     */
    List<UserPO> doList2POList(List<UserDO> userDOList);

    /**
     * 分页PO转分页DO
     */
    default CommonPage<UserDO> pageP02DO(com.baomidou.mybatisplus.extension.plugins.pagination.Page<UserPO> page) {
        List<UserDO> userDOList = poList2DOList(page.getRecords());
        return CommonPage.getCommonPage(page.getCurrent(), page.getTotal(), page.getSize(), userDOList);
    }
}
