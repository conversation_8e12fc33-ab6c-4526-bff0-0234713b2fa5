package com.jd.kf.oss.performance.infra.mybatis.service;

import com.jd.kf.oss.performance.infra.mybatis.entity.UserPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IUserPOService extends IService<UserPO> {

    /**
     * 批量更新用户数据（通过erp、tenantCode、period条件）
     * @param userList 用户列表
     * @return 更新的记录数
     */
    int batchUpdateByCondition(List<UserPO> userList);

}
