package com.jd.kf.oss.performance.infra.mybatis.mybatisPlusConfig;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.google.common.collect.ImmutableSet;
import com.jd.kf.oss.performance.context.UserContextHolder;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.NullValue;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Locale;
import java.util.Set;


@Slf4j
@Configuration
public class MybatisPlusConfig {

    private static final Set<String> TAB_NAME_BLACK_SET = ImmutableSet.of();
    private static final String DEFAULT_COLUMN = "tenant_code";

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                if (UserContextHolder.getTenantCode() == null) {
                    return new NullValue();
                }
                return new StringValue(UserContextHolder.getTenantCode());
            }

            @Override
            public boolean ignoreTable(String tableName) {
                boolean res = TAB_NAME_BLACK_SET.contains(tableName.toLowerCase(Locale.ROOT))
                        || UserContextHolder.getTenantCode() == null;
                return res;
            }

            @Override
            public String getTenantIdColumn() {
                return DEFAULT_COLUMN;
            }
        }));
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return mybatisPlusInterceptor;
    }
}
