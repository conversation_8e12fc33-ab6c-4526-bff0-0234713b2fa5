package com.jd.kf.oss.performance.infra.configuration.performancetarget;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.IPerformanceTargetRepository;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTargetPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTargetPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTargetPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/06/25
 */
@Service
@Slf4j
public class TargetRepository implements IPerformanceTargetRepository {
    @Autowired
    private IPerformanceTargetPOService performanceTargetPOService;

    @Autowired
    private PerformanceTargetPOMapper performanceTargetPOMapper;



    /**
     * 分页查询绩效目标
     * @param tenantCode 租户标识
     * @param period 绩效月，可选
     * @param businessLineId 绩效组ID，可选
     * @param businessLineName 绩效组名称，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return
     */
    @Override
    public CommonPage<PerformanceTargetDO> queryTargetByConditions(String tenantCode, String period, String businessLineId, String businessLineName, String planCode,int pageNum, int pageSize) {
        // 保持与原有方法完全一致的校验逻辑
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        try {
            // 先查询总数
            Long total = performanceTargetPOMapper.countTargetWithJoin(
                    tenantCode, period, businessLineId, planCode, businessLineName);

            // 如果总数为0，直接返回空结果
            if (total == 0) {
                return CommonPage.getCommonPage(Long.valueOf(pageNum), Long.valueOf(pageSize), 0L, new ArrayList<>());
            }

            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;

            // 查询分页数据
            List<PerformanceTargetDO> targetDOList = performanceTargetPOMapper.queryTargetWithJoin(
                    tenantCode, period, businessLineId, planCode, businessLineName, offset, pageSize);

            return CommonPage.getCommonPage(Long.valueOf(pageNum), Long.valueOf(pageSize), total, targetDOList);
        } catch (Exception e) {
            log.error("查询绩效目标失败, tenantCode: {}, period: {}, businessLineId: {}, businessLineName: {}, planCode: {}, pageNum: {}, pageSize: {}",
                    tenantCode, period, businessLineId, businessLineName, planCode, pageNum, pageSize, e);
            throw new RuntimeException("查询绩效目标失败", e);
        }
    }

    @Override
    public PerformanceTargetDO queryTargetByBusinessLineId(String businessLineId, String tenantCode, String period) {
        // 参数校验
        if (StringUtils.isAnyBlank(businessLineId, tenantCode, period)) {
            throw new IllegalArgumentException("参数不能为空：businessLineId、tenantCode和period均为必填项");
        }

        try {
            // 使用联表查询获取完整的绩效目标信息，包括businessLineName和evaluationPlan
            return performanceTargetPOMapper.queryTargetDetailByBusinessLineId(businessLineId, tenantCode, period);
        } catch (Exception e) {
            log.error("查询绩效目标详情失败, businessLineId: {}, tenantCode: {}, period: {}", businessLineId, tenantCode, period, e);
            throw new RuntimeException("查询绩效目标详情失败", e);
        }
    }

    public PerformanceTargetDO queryTargetByBusinessLineIdAndPeriod(String businessLineId, String period) {
        // 参数校验
        if (StringUtils.isAnyBlank(businessLineId, period)) {
            throw new IllegalArgumentException("参数不能为空：businessLineId和period均为必填项");
        }

        try {
            return performanceTargetPOMapper.queryTargetDetailByBusinessLineId(businessLineId, UserContextHolder.getTenantCode(), period);
        } catch (Exception e) {
            log.error("查询绩效目标详情失败, businessLineId: {}, period: {}", businessLineId, period, e);
            throw new RuntimeException("查询绩效目标详情失败", e);
        }
    }

    /**
     * 根据绩效月和绩效组IDs查询绩效目标
     */
    @Override
    public List<PerformanceTargetDO> listTargetByBusinessLineCodeAndPeriod(List<String> businessLineIds, String period) {
        if (CollectionUtils.isEmpty(businessLineIds) || StringUtils.isBlank(period)) {
            return new ArrayList<>();
        }

        try {
            return performanceTargetPOMapper.queryTargetListWithJoin(businessLineIds, period);
        } catch (Exception e) {
            log.error("批量查询绩效目标详情失败, businessLineIds: {}, period: {}", businessLineIds, period, e);
            throw new RuntimeException("批量查询绩效目标详情失败", e);
        }
    }

    /**
     * 更新绩效目标
     * @param targetDO
     * @return
     */
    public boolean updateTargetByLineIdAndPeriod(PerformanceTargetDO targetDO) {
        Objects.requireNonNull(targetDO, "绩效目标对象不能为空");
        CheckUtil.notBlank(targetDO.getPeriod(), targetDO.getBusinessLineId(), "更新绩效目标失败，绩效目标对应绩效月以及绩效组不能为空");
        return performanceTargetPOService.lambdaUpdate()
                .eq(PerformanceTargetPO::getPeriod, targetDO.getPeriod())
                .eq(PerformanceTargetPO::getBusinessLineId, targetDO.getBusinessLineId())
                .eq(PerformanceTargetPO::getYn, YnEnum.VALID.getValue())
                .set(PerformanceTargetPO::getEvaluationPlanCode, targetDO.getEvaluationPlanCode())
                .set(PerformanceTargetPO::getCpd, targetDO.getCpd())
                .set(PerformanceTargetPO::getPrice, targetDO.getPrice())
                .set(PerformanceTargetPO::getDays, targetDO.getDays())
                .set(PerformanceTargetPO::getEditor,UserContextHolder.getOperator())
                .set(PerformanceTargetPO::getModified,LocalDateTime.now())
                .update();
    }

    /**
     * 保存绩效目标
     *
     */
    @Override
    public boolean saveTarget(PerformanceTargetDO targetDO) {
        return performanceTargetPOService.save(TargetDOConverter.INSTANCE.DO2PO(targetDO));
    }

    @Override
    public List<PerformanceTargetDO> queryAllTargetByPeriod(String period) {
        LambdaQueryWrapper<PerformanceTargetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceTargetPO::getPeriod, period)
                .eq(PerformanceTargetPO::getYn, YnEnum.VALID.getValue());
        List<PerformanceTargetPO> poList = performanceTargetPOService.list(queryWrapper);
        return poList.stream().map(po -> {
            return covertPerformanceDO(po);
        }).collect(Collectors.toList());
    }

    private static PerformanceTargetDO covertPerformanceDO(PerformanceTargetPO po) {
        PerformanceTargetDO targetDO = new PerformanceTargetDO();
        targetDO.setId(po.getId());
        targetDO.setTenantCode(po.getTenantCode());
        targetDO.setBusinessLineId(po.getBusinessLineId());
        targetDO.setEvaluationPlanCode(po.getEvaluationPlanCode());
        // 已删除的字段：businessLineName、evaluationType、evaluationPlan现在通过联表查询获取
        targetDO.setPrice(po.getPrice());
        targetDO.setCpd(po.getCpd());
        targetDO.setPeriod(po.getPeriod());
        targetDO.setCreated(po.getCreated());
        targetDO.setModified(po.getModified());
        targetDO.setCreator(po.getCreator());
        targetDO.setEditor(po.getEditor());
        return targetDO;
    }

    /**
     * 根据主键id进行批量更新
     * @param targetDOs
     * @return
     */
    @Override
    public boolean updateBatchTargetById(List<PerformanceTargetDO> targetDOs) {
        if (CollectionUtils.isEmpty(targetDOs)) {
            return true;
        }
        return performanceTargetPOService.updateBatchById(TargetDOConverter.INSTANCE.DOs2POs(targetDOs));
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyBusinessLineToTarget(List<BusinessLineDO> businessLines, String period) {
        // 将BusinessLine数据转换为PerformanceTarget数据
        List<PerformanceTargetPO> toInsert = businessLines.stream().map(businessLine -> {
            return convertPerformanceTargetPO(period, businessLine);
        }).collect(Collectors.toList());

        return performanceTargetPOService.saveBatch(toInsert);
    }

    @NotNull
    private static PerformanceTargetPO convertPerformanceTargetPO(String period, BusinessLineDO businessLine) {
        PerformanceTargetPO po = new PerformanceTargetPO();
        po.setTenantCode(businessLine.getTenantCode());
        po.setBusinessLineId(businessLine.getBusinessLineId());
        // 已删除businessLineName字段，现在通过联表查询获取
        po.setPeriod(period); // 设置当前绩效月
        po.setCreator(businessLine.getCreator());
        po.setEditor(businessLine.getEditor());
        // 设置默认值
        // 已删除evaluationType和evaluationPlan字段，现在通过联表查询获取
        po.setPrice("0");
        po.setCpd("0");
        return po;
    }

    /**
     * 批量保存绩效目标数据
     * @param targets 绩效目标数据对象列表，包含租户编码、业务线ID、业务线名称、周期、创建人、编辑人、评估类型、评估方案、价格和CPD等信息
     * @return 返回批量保存操作是否成功，成功返回true，失败返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchTargets(List<PerformanceTargetDO> targets) {
        if (targets == null || targets.isEmpty()) return true;
        List<PerformanceTargetPO> poList = targets.stream().map(target -> {
            PerformanceTargetPO po = new PerformanceTargetPO();
            po.setTenantCode(target.getTenantCode());
            po.setBusinessLineId(target.getBusinessLineId());
            po.setEvaluationPlanCode(target.getEvaluationPlanCode());
            po.setPeriod(target.getPeriod());
            po.setCreator(target.getCreator());
            po.setEditor(target.getEditor());
            po.setPrice(target.getPrice());
            po.setCpd(target.getCpd());
            po.setDays(target.getDays());
            return po;
        }).collect(Collectors.toList());
        return performanceTargetPOService.saveBatch(poList);
    }

    /**
     * 根据实体是否携带id判断是插入还是更新
     * @param targetDO
     */
    public void insertOrUpdateTarget(PerformanceTargetDO targetDO) {
        Objects.requireNonNull(targetDO, "绩效目标对象不能为空");
        PerformanceTargetPO performanceTargetPO = TargetDOConverter.INSTANCE.DO2PO(targetDO);
        if (targetDO.getId() == null) {
            performanceTargetPOService.save(performanceTargetPO);
        } else {
            updateTarget(performanceTargetPO);
        }
    }

    /**
     * 更新绩效目标
     * @param targetPO
     */
    public void updateTarget(PerformanceTargetPO targetPO) {
        performanceTargetPOService.lambdaUpdate()
                .eq(PerformanceTargetPO::getId, targetPO.getId())
                .set(PerformanceTargetPO::getEvaluationPlanCode, targetPO.getEvaluationPlanCode())
                .set(PerformanceTargetPO::getPrice, targetPO.getPrice())
                .set(PerformanceTargetPO::getCpd, targetPO.getCpd())
                .set(PerformanceTargetPO::getDays, targetPO.getDays())
                .update();
    }

    /**
     * 更新绩效目标的绩效方案
     * @param targetIds
     * @param planCode
     */
    public  void updateTargetPlanCode(List<Long> targetIds,String planCode){

        performanceTargetPOService.lambdaUpdate()
                .in(PerformanceTargetPO::getId, targetIds)
                .set(PerformanceTargetPO::getEvaluationPlanCode, planCode)
                .set(PerformanceTargetPO::getModified, LocalDateTime.now())
                .set(PerformanceTargetPO::getEditor, UserContextHolder.getOperator())
                .eq(PerformanceTargetPO::getYn,YnEnum.VALID.getValue())
                .update();
    }

    /**
     * 根据业务线ID列表和周期更新目标数据修改时间和操作人
     * @param businessLineIds 业务线ID列表
     * @param period 周期标识
     * @param operator 操作人标识
     */
    public void updateTargetModifyTime(List<String> businessLineIds,String period, String operator){
        if(CollectionUtils.isEmpty(businessLineIds)){
            return;
        }
        if(StringUtils.isBlank(period)){
            throw new IllegalArgumentException("绩效月不能为空");
        }
        performanceTargetPOService.lambdaUpdate()
                .set(PerformanceTargetPO::getModified, LocalDateTime.now())
                .set(PerformanceTargetPO::getEditor, operator)
                .eq(PerformanceTargetPO::getPeriod, period)
                .in(PerformanceTargetPO::getBusinessLineId, businessLineIds)
                .eq(PerformanceTargetPO::getYn, YnEnum.VALID.getValue())
                .update();

    }
}
