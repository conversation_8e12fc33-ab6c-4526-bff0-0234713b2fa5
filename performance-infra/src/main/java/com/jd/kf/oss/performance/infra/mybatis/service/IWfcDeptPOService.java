package com.jd.kf.oss.performance.infra.mybatis.service;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IWfcDeptPOService extends IService<WfcDeptPO> {

    /**
     * 批量添加部门
     * @param wfcDeptPOList 部门列表
     */
    void batchUpsertDept(List<WfcDeptPO> wfcDeptPOList);

    /**
     * 批量逻辑删除部门信息
     * @param wfcDeptPOList 需要删除的部门列表
     */
    void batchRemoveDeptById(List<WfcDeptPO> wfcDeptPOList);

    /**
     * 根据部门ID列表获取对应的WfcDeptPO对象列表
     * @param deptIds 部门ID列表
     * @return WfcDeptPO对象列表
     */
    List<WfcDeptPO> getWfcDeptListByDeptIds(List<String> deptIds);

    /**
     * 根据上级部门ID查询所有下级部门ID列表（包含自身）
     * @param tenantCode 租户标识
     * @param parentDeptId 上级部门ID
     * @return 包含该部门及其所有下级部门的ID列表
     */
    List<String> querySubDeptIdsByParentDeptId(String tenantCode, String parentDeptId);
}
