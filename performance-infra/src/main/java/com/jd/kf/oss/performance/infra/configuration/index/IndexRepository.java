package com.jd.kf.oss.performance.infra.configuration.index;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.fastjson.JSON;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.index.IIndexRepository;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexBasicDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexBasicPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexOriginPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceIndexPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexBasicPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexOriginPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceIndexPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CollectionDiffUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
@Service
@Slf4j
public class IndexRepository implements IIndexRepository {
    @Autowired
    private IIndexBasicPOService indexBasicP0Service;

    @Autowired
    IPerformanceIndexPOService performanceIndexPOService;

    @Autowired
    private IIndexOriginPOService indexOriginPOService;

    @Autowired
    private DynamicConfig dynamicConfig;

    /**
     * 根据条件分页查询指标信息
     *
     * @param tenantCode 租户编码
     * @param kpiCd      指标编码
     * @param kpiName    指标名称（支持模糊查询）
     * @param status     指标状态
     * @param pageNum    页码（必须大于0）
     * @param pageSize   每页数量（必须大于0）
     * @return 包含分页结果的通用分页对象
     */
    @Override
    public CommonPage<IndexDO> queryIndexByConditions(String tenantCode, String kpiCd, String kpiName, String status, int pageNum, int pageSize) {
        Page<IndexBasicPO> indexBasicPOPage = PageUtil.toMybatisPage(pageNum, pageSize);
        LambdaQueryWrapper<IndexBasicPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(true, IndexBasicPO::getYn, YnEnum.VALID.getValue())
                .eq(StringUtils.isNotBlank(tenantCode), IndexBasicPO::getTenantCode, tenantCode)
                .eq(StringUtils.isNotBlank(status), IndexBasicPO::getStatus, status)
                .eq(StringUtils.isNotBlank(kpiCd), IndexBasicPO::getKpiCd, kpiCd)
                .like(StringUtils.isNotBlank(kpiName), IndexBasicPO::getKpiName, kpiName)
                .orderByDesc(IndexBasicPO::getModified)
                .orderByDesc(IndexBasicPO::getCreated);

        Page<IndexBasicPO> page = indexBasicP0Service.page(indexBasicPOPage, queryWrapper);
        return IndexBasicPOConverter.INSTANCE.pageP02DO(page, dynamicConfig.getIndexPlatformUrlPrefix());
    }

    /**
     * 根据绩效组ID、租户编码和期间查询指标列表
     *
     * @param businessLineId 业务线ID
     * @param tenantCode     租户编码
     * @param period         期间
     * @return 指标领域对象列表
     */
    @Override
    public List<IndexDO> queryIndexesByBusinessId(String businessLineId, String tenantCode, String period) {
        List<PerformanceIndexPO> indexP0s = queryIndexPOsByPeriodAndBusinessLineId(businessLineId, tenantCode, period);
        return IndexBasicPOConverter.INSTANCE.po2DO(indexP0s);
    }


    private List<PerformanceIndexPO> queryIndexPOsByPeriodAndBusinessLineId(String businessLineId, String tenantCode, String period) {
        LambdaQueryWrapper<PerformanceIndexPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceIndexPO::getYn, YnEnum.VALID.getValue())
                .eq(PerformanceIndexPO::getBusinessLineId, businessLineId)
                .eq(PerformanceIndexPO::getTenantCode, tenantCode)
                .eq(PerformanceIndexPO::getPeriod, period);
        return performanceIndexPOService.list(queryWrapper);
    }

    /**
     * 批量保存绩效指标数据
     *
     * @param dos 待保存的绩效指标数据列表，不能为空
     * @return 是否保存成功
     */
    public boolean saveBatchIndex(List<IndexDO> dos) {
        if (dos == null || dos.isEmpty()) {
            return true;
        }
        List<PerformanceIndexPO> indexPOs = IndexPOConverter.INSTANCE.dos2POs(dos);
        return performanceIndexPOService.saveBatch(indexPOs);
    }

    /**
     * 批量更新绩效指标信息
     *
     * @param dos 绩效指标数据对象列表，不能为null或空
     * @return 更新是否成功，true表示成功
     */
    public boolean updateBatchIndexDOById(List<IndexDO> dos) {
        if (dos == null || dos.isEmpty()) {
           return true;
        }
        List<PerformanceIndexPO> indexPOs = IndexPOConverter.INSTANCE.dos2POs(dos);
        return performanceIndexPOService.updateBatchById(indexPOs);

    }

    /**
     * 根据指标名称列表查询指标基础信息
     *
     * @param kpiNames 指标名称列表，不能为空
     * @return 指标基础信息DO对象列表
     */
    public List<IndexBasicDO> selectIndexBasicByKpiNames(List<String> kpiNames) {
        if (CollectionUtils.isEmpty(kpiNames)) {
            throw new IllegalArgumentException("查询指标基础信息失败，指标编码或者绩效月不能为空");
        }
        LambdaQueryWrapper<IndexBasicPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IndexBasicPO::getYn, YnEnum.VALID.getValue())
                .eq(IndexBasicPO::getStatus,IndexStatusEnum.VALID.getValue())
                .in(IndexBasicPO::getKpiName, kpiNames);
        return IndexPOConverter.INSTANCE.indexBasicPOs2DOs(indexBasicP0Service.list(queryWrapper));

    }

    /**
     * 根据业务线ID列表和期间查询指标信息
     *
     * @param businessLineIds 业务线ID列表
     * @param period          期间
     * @return 指标DO对象列表
     */
    public List<IndexDO> queryIndexPOsByPeriodAndBusinessLineId(List<String> businessLineIds, String period) {
        LambdaQueryWrapper<PerformanceIndexPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceIndexPO::getYn, YnEnum.VALID.getValue())
                .in(PerformanceIndexPO::getBusinessLineId, businessLineIds)
                .eq(PerformanceIndexPO::getPeriod, period);
        return IndexPOConverter.INSTANCE.po2DO(performanceIndexPOService.list(queryWrapper));
    }

    public boolean deleteIndexesByLineIdAndPeriod(String businessLineId, String period) {
        return performanceIndexPOService.lambdaUpdate()
                .eq(PerformanceIndexPO::getBusinessLineId, businessLineId)
                .eq(PerformanceIndexPO::getPeriod, period)
                .set(PerformanceIndexPO::getModified, LocalDateTime.now())
                .set(PerformanceIndexPO::getEditor, UserContextHolder.getOperator())
                .set(PerformanceIndexPO::getYn, YnEnum.INVALID.getValue()).update();
    }

    /**
     * 从原始数据源查询所有指标数据并转换为DO对象列表
     *
     * @return 转换后的指标DO对象列表，包含指标的基本信息
     */
    public List<IndexDO> queryAllIndexDOFromOrigin() {
        List<IndexOriginPO> originPOList = indexOriginPOService.list();
        return originPOList.stream().map(po -> {
            IndexDO indexDO = new IndexDO();
            indexDO.setId(po.getId());
            indexDO.setTenantCode(po.getTenantCode());
            indexDO.setKpiCd(po.getKpiCd());
            indexDO.setCreated(po.getCreated());
            indexDO.setModified(po.getModified());
            return indexDO;
        }).collect(Collectors.toList());
    }

    /**
     * 同步指标平台数据到基础数据库
     *
     * @param platformList 指标平台数据列表
     * @param basicList    基础数据库中的指标数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncIndexBasicWithPlatform(List<IndexDO> platformList, List<IndexDO> basicList) {

        // 1. 获取指标平台和绩效存储的指标数据的差异集合
        String[] uniqueFields = new String[]{"tenantCode", "kpiCd"};
        String[] checkFields = new String[]{"kpiName", "description"};
        Map<String, List<IndexDO>> diffIndexDOMap = CollectionDiffUtil.diffCollections(basicList, platformList, uniqueFields, checkFields);

        // 2. 插入或者更新Basic数据
        if (!diffIndexDOMap.isEmpty()) {
            syncDiffIndexDOS(diffIndexDOMap);
        }
    }

    private void syncDiffIndexDOS(Map<String, List<IndexDO>> diffIndexDOMap) {
        List<IndexBasicPO> indexP0SList = new ArrayList<>();
        List<IndexDO> toInsert = diffIndexDOMap.get(CollectionDiffUtil.DIFF_ADD);
        List<IndexDO> toRemove = diffIndexDOMap.get(CollectionDiffUtil.DIFF_REMOVE);
        List<IndexDO> toUpdate = diffIndexDOMap.get(CollectionDiffUtil.DIFF_UPDATE);

        // 1. 处理需要插入的数据
        log.info("syncIndexBasicWithPlatform, toInsert{}", JSON.toJSON(toInsert));
        if (!CollectionUtils.isEmpty(toInsert)) {
            indexP0SList.addAll(convertToIndexBasicPOList(toInsert, IndexStatusEnum.VALID.getStatus()));
        }

        // 2. 处理需要删除的数据
        log.info("syncIndexBasicWithPlatform, toRemove{}", JSON.toJSON(toRemove));
        if (!CollectionUtils.isEmpty(toRemove)) {
            indexP0SList.addAll(convertToIndexBasicPOList(toRemove, IndexStatusEnum.INVALID.getStatus()));
        }

        // 3. 处理需要更新的数据
        log.info("syncIndexBasicWithPlatform, toUpdate{}", JSON.toJSON(toUpdate));
        if (!CollectionUtils.isEmpty(toUpdate)) {
            indexP0SList.addAll(convertToIndexBasicPOList(toUpdate, IndexStatusEnum.VALID.getStatus()));
        }

        if (indexP0SList.isEmpty()) {
            log.info("empty indexP0SList, do not need to save or update");
            return;
        }
        indexBasicP0Service.saveOrUpdateBatch(indexP0SList);
    }

    /**
     * 将 IndexDO 列表转换为 IndexBasicPO 列表，并设置指定的状态
     */
    private List<IndexBasicPO> convertToIndexBasicPOList(List<IndexDO> indexDOList, String status) {
        return indexDOList.stream().map(po -> convert(status, po)).collect(Collectors.toList());
    }

    @NotNull
    private static IndexBasicPO convert(String status, IndexDO po) {
        IndexBasicPO p = new IndexBasicPO();
        p.setTenantCode(po.getTenantCode());
        p.setKpiCd(po.getKpiCd());
        p.setKpiName(po.getKpiName());
        p.setStatus(status);
        p.setDescription(po.getDescription());
        return p;
    }

    /**
     * 查询所有指标基本信息并将其转换为DO对象列表
     *
     * @return 包含所有指标基本信息的DO对象列表
     */
    public List<IndexDO> queryAllIndexBasic() {
        LambdaQueryWrapper<IndexBasicPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IndexBasicPO::getYn, YnEnum.VALID.getValue())
                .eq(IndexBasicPO::getStatus, IndexStatusEnum.VALID.getValue());
        List<IndexBasicPO> poList = indexBasicP0Service.list(queryWrapper);
        return poList.stream().map(IndexRepository::convert).collect(Collectors.toList());
    }

    @NotNull
    private static IndexDO convert(IndexBasicPO po) {
        IndexDO indexDO = new IndexDO();
        indexDO.setId(po.getId());
        indexDO.setTenantCode(po.getTenantCode());
        indexDO.setKpiCd(po.getKpiCd());
        indexDO.setKpiName(po.getKpiName());
        indexDO.setStatus(IndexStatusEnum.getStatus(po.getStatus()));
        indexDO.setDescription(po.getDescription());
        return indexDO;
    }

    /**
     * 根据ID列表批量删除指标数据
     * 物理删除
     * @param ids 待删除的ID列表
     * @return 删除是否成功
     */
    public boolean deleteBatchById(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        return this.performanceIndexPOService.removeByIds(ids);
    }

    /**
     * 根据指标编码列表查询指标基础信息
     *
     * @param kpiCds 指标编码列表，不能为空
     * @return 指标基础信息DO对象列表
     */
    public List<IndexBasicDO> selectIndexBasicByKpiCds(List<String> kpiCds) {
        if (CollectionUtils.isEmpty(kpiCds)) {
            throw new IllegalArgumentException("查询指标基础信息失败，指标编码不能为空");
        }
        LambdaQueryWrapper<IndexBasicPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IndexBasicPO::getYn, YnEnum.VALID.getValue())
                .eq(IndexBasicPO::getStatus,IndexStatusEnum.VALID.getValue())
                .in(IndexBasicPO::getKpiCd, kpiCds);
        return IndexPOConverter.INSTANCE.indexBasicPOs2DOs(indexBasicP0Service.list(queryWrapper));

    }

    /**
     * 批量删除指定业务线ID该绩效月下的关联的绩效指标
     * @param businessLineIds
     * @param period
     */
    public boolean deleteBatchByBusinessLineIdsAndPeriod(List<String> businessLineIds, String period){
        if(CollectionUtils.isEmpty(businessLineIds) ){
           return true;
        }
        if(StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("删除指标失败，绩效月不能为空");
        }
        return performanceIndexPOService.lambdaUpdate()
                .in(PerformanceIndexPO::getBusinessLineId, businessLineIds)
                .eq(PerformanceIndexPO::getPeriod, period)
                .set(PerformanceIndexPO::getYn, YnEnum.INVALID.getValue())
                .update();
    }
}
