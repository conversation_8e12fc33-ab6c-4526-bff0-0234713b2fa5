package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.IPerformanceIndexRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.index.IndexBasicPOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceIndexPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceIndexPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PerformanceIndexRepository implements IPerformanceIndexRepository {
    @Resource
    private IPerformanceIndexPOService performanceIndexPOService;
    @Override
    public List<PerformanceIndex> queryIndexesByBusinessId(String businessLineId, String tenantCode, String period) {
        LambdaQueryWrapper<PerformanceIndexPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PerformanceIndexPO::getYn, YnEnum.VALID.getValue())
                .eq(PerformanceIndexPO::getBusinessLineId, businessLineId)
                .eq(PerformanceIndexPO::getTenantCode, tenantCode)
                .ne(PerformanceIndexPO::getKpiCd, "")
                .eq(PerformanceIndexPO::getPeriod, period);
        List<PerformanceIndexPO> indexPOS = performanceIndexPOService.list(queryWrapper);
        if (CollectionUtils.isEmpty(indexPOS)) {
            return Lists.newArrayList();
        }
        return indexPOS.stream().map(PerformanceIndexRepository::convertFromPO).collect(Collectors.toList());
    }

    public static PerformanceIndex convertFromPO(PerformanceIndexPO po) {
        PerformanceIndex index = new PerformanceIndex();
        // 基础属性拷贝
        index.setId(po.getId());
        index.setTenantCode(po.getTenantCode());
        index.setBusinessLineId(po.getBusinessLineId());
        index.setKpiCd(po.getKpiCd());
        index.setKpiType(po.getKpiType());
        index.setKpiName(po.getKpiName());
        index.setDescription(po.getDescription());
        index.setStartDate(po.getStartDate());
        index.setEndDate(po.getEndDate());
        index.setThreshold(po.getThreshold());
        index.setPeriod(po.getPeriod());
        index.setWeight(po.getWeight());
        index.setIndexPlatformUrlCode(po.getIndexPlatformUrlCode());

        // 需要类型转换的属性
        index.setTemplate(IndexTemplate.getByCode(po.getTemplate()));
        index.setStatus(IndexStatusEnum.getStatus(po.getStatus()));

        // 基础实体属性
        index.setCreator(po.getCreator());
        index.setEditor(po.getEditor());
        index.setCreated(po.getCreated());
        index.setModified(po.getModified());

        return index;
    }

}
