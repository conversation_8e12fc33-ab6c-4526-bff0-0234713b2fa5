package com.jd.kf.oss.performance.infra.mybatis.service;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public interface IWfcUserPOService extends IService<WfcUserPO> {

    /**
     * 根据ERP列表批量获取用户信息
     * @param erpList ERP列表
     * @return 用户信息列表
     */
    List<WfcUserPO> getWfcUserListByErps(List<String> erpList);

    /**
     * 批量插入或更新用户信息
     * @param userList 用户信息列表
     */
    void batchUpsertUser(List<WfcUserPO> userList);

    /**
     * 批量逻辑删除用户信息
     * @param userList 用户信息列表
     */
    void batchRemoveUserById(List<WfcUserPO> userList);

}
