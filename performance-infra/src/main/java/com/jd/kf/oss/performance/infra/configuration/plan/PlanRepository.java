package com.jd.kf.oss.performance.infra.configuration.plan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.domain.config.domain.plan.IPlanRepository;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.mybatis.entity.FactorPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PlanPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IPlanPOService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jd.kf.oss.performance.enums.ResultCodeEnum.VALIDATE_CHECK_ERROR;

/**
 * 计划仓储实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class PlanRepository implements IPlanRepository {
    @Resource
    private IPlanPOService planPOService;

    /**
     * 保存或更新计划信息
     * @param PlanDO 计划领域对象
     * @return 保存是否成功
     */
    @Override
    public boolean save(PlanDO planDO) {
        if (planDO == null) {
            return false;
        }
        if (checkExist(planDO)) {
            throw new BizException(VALIDATE_CHECK_ERROR.getCode(), "保存失败，方案名称存在重复");
        }
        PlanPO planPO = convertToPO(planDO);
        // 使用MyBatis-Plus的IService接口方法
        if (planDO.getId() != null && planDO.getId() > 0) {
            return planPOService.updateById(planPO);
        } else {
            boolean success = planPOService.save(planPO);
            if (success) {
                planDO.setId(planPO.getId()); // 回写ID
            }
            return success;
        }
    }

    private Boolean checkExist(PlanDO planDO) {
        List<PlanPO> exists = planPOService.lambdaQuery()
                .eq(PlanPO::getTenantCode, planDO.getTenantCode())
                .eq(PlanPO::getPeriod, planDO.getPeriod())
                .eq(PlanPO::getName, planDO.getName()).list();
        exists = exists.stream().filter(a -> !a.getId().equals(planDO.getId())).collect(Collectors.toList());
        return !CollectionUtils.isEmpty(exists);
    }

    @Override
    public boolean delete(String tenantCode, String period, String code) {
        if (StringUtils.isAnyEmpty(tenantCode, period, code)) {
            return false;
        }
        LambdaQueryWrapper<PlanPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlanPO::getTenantCode, tenantCode)
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getCode, code)
                .eq(PlanPO::getYn, true); // 查询未删除的记录

        return planPOService.remove(queryWrapper);
    }

    @Override
    public PlanDO queryByCode(String tenantCode, String period, String code) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period, code)) {
            return null;
        }

        PlanPO planPO = planPOService.lambdaQuery()
                .eq(PlanPO::getTenantCode, tenantCode)
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getCode, code)
                .eq(PlanPO::getYn, true) // 查询未删除的记录
                .one();

        // 转换为领域对象
        return planPO != null ? convertToDO(planPO) : null;
    }

    @Override
    public List<PlanDO> queryAll(String tenantCode, String period) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period)) {
            return null;
        }

        // 构建查询条件
        // 调用MyBatis-Plus的查询方法
        List<PlanPO> PlanPOS = planPOService.lambdaQuery()
                .eq(PlanPO::getTenantCode, tenantCode)
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getYn, true) // 查询未删除的记录
                .list();

        if (CollectionUtils.isEmpty(PlanPOS)) {
            return Lists.newArrayList();
        }
        // 转换为领域对象
        return PlanPOS.stream().map(this::convertToDO).collect(Collectors.toList());
    }

    @Override
    public List<PlanDO> queryPlanByCodes(String tenantCode, String period, List<String> codes) {
        // 参数校验
        if (StringUtils.isAnyBlank(tenantCode, period) || CollectionUtils.isEmpty(codes)) {
            return null;
        }

        // 构建查询条件
        // 调用MyBatis-Plus的查询方法
        List<PlanPO> PlanPOS = planPOService.lambdaQuery()
                .eq(PlanPO::getTenantCode, tenantCode)
                .eq(PlanPO::getPeriod, period)
                .in(PlanPO::getCode, codes)
                .eq(PlanPO::getYn, true) // 查询未删除的记录
                .list();

        if (CollectionUtils.isEmpty(PlanPOS)) {
            return Lists.newArrayList();
        }
        // 转换为领域对象
        return PlanPOS.stream().map(this::convertToDO).collect(Collectors.toList());
    }

    @Override
    public CommonPage<PlanDO> queryByName(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<PlanPO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(PlanPO::getTenantCode, tenantCode);
        queryWrapper.eq(PlanPO::getPeriod, period);
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(PlanPO::getType, type);
        }
        if (StringUtils.isNotBlank(factorName)) {
            queryWrapper.like(PlanPO::getName, factorName);
        }
        queryWrapper.orderByDesc(PlanPO::getModified);
        IPage<PlanPO> planPOPage = planPOService.page(new Page<>(pageNum, pageSize), queryWrapper);
        CommonPage<PlanDO> result = new CommonPage<>();
        result.setTotal(planPOPage.getTotal());
        result.setPage(planPOPage.getPages());
        result.setSize(planPOPage.getSize());

        if (CollectionUtils.isNotEmpty(planPOPage.getRecords())) {
            result.setData(planPOPage.getRecords().stream().map(this::convertToDO).collect(Collectors.toList()));
        }
        // 转换为领域对象
        return result;
    }

    private PlanDO convertToDO(PlanPO planPO) {
        if (Objects.isNull(planPO)) {
            return null;
        }
        PlanDO planDO = new PlanDO();

        // 手动映射基类属性
        planDO.setId(planPO.getId());
        planDO.setCode(planPO.getCode());
        planDO.setTenantCode(planPO.getTenantCode());
        planDO.setCreator(planPO.getCreator());
        planDO.setEditor(planPO.getEditor());
        planDO.setCreated(planPO.getCreated());
        planDO.setModified(planPO.getModified());

        // 映射PlanPO特有属性
        planDO.setName(planPO.getName());
        planDO.setType(planPO.getType());
        planDO.setFormula(planPO.getFormula());
        planDO.setDecimalPlaces(planPO.getDecimalPlaces());
        planDO.setRoundType(planPO.getRoundType());
        planDO.setPeriod(planPO.getPeriod());
        planDO.setStatus(planPO.getStatus());

        planDO.setFormulaDisplayInfo(planPO.getFormulaDisplayInfo());
        planDO.setDisplayInfo(planPO.getDisplayInfo());

        return planDO;
    }

    private PlanPO convertToPO(PlanDO planDO) {
        PlanPO planPO = new PlanPO();

        // 手动映射基类属性
        planPO.setId(planDO.getId());
        planPO.setCode(planDO.getCode());
        planPO.setTenantCode(planDO.getTenantCode());
        planPO.setCreator(planDO.getCreator());
        planPO.setEditor(planDO.getEditor());
        planPO.setCreated(planDO.getCreated());
        planPO.setModified(planDO.getModified());

        // 映射PlanDO特有属性
        planPO.setName(planDO.getName());
        planPO.setType(planDO.getType());
        planPO.setFormula(planDO.getFormula());
        planPO.setDecimalPlaces(planDO.getDecimalPlaces());
        planPO.setRoundType(planDO.getRoundType());
        planPO.setPeriod(planDO.getPeriod());
        planPO.setStatus(planDO.getStatus());

        planPO.setFormulaDisplayInfo(planDO.getFormulaDisplayInfo());
        planPO.setDisplayInfo(planDO.getDisplayInfo());

        // 处理特殊属性
        if (Objects.isNull(planDO.getId()) && planDO.getCode() == null) {
            planPO.setCode(planDO.buildCode());
        }
        return planPO;
    }

    public PlanDO selectPlanByCodeAndPeriod(String planCode,String period){
        CheckUtil.notBlank(planCode,period,"绩效方案编码和绩效月不能为空");
        PlanPO planPO = planPOService.lambdaQuery()
                .eq(PlanPO::getCode, planCode)
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getYn, YnEnum.VALID.getValue())
                .one();
        return DOConverter.INSTANCE.PO2DO(planPO);
    }

    public List<PlanDO> selectAllPlansByPeriod(String period){
        CheckUtil.notBlank(period, "绩效月不能为空");
        List<PlanPO> list = planPOService.lambdaQuery()
                .eq(PlanPO::getPeriod, period)
                .eq(PlanPO::getYn, YnEnum.VALID.getValue()).list();
        return DOConverter.INSTANCE.POs2DOs(list);
    }

}
