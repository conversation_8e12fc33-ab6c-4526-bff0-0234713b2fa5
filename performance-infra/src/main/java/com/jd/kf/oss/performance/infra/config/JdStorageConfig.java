package com.jd.kf.oss.performance.infra.config;

import com.jd.jss.Credential;
import com.jd.jss.JingdongStorageService;
import com.jd.jss.client.ClientConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class JdStorageConfig {

    @Autowired
    private DynamicConfig dynamicConfig;


    @Bean
    public ClientConfig clientConfig() {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setConnectionTimeout(dynamicConfig.getConnectionTimeout());
        clientConfig.setEndpoint(dynamicConfig.getHostName());
        return clientConfig;
    }

    @Bean(name = "ddFileCredential")
    public Credential ddFileCredential() {
        return new Credential(dynamicConfig.getDdFileAccessKeyId(), dynamicConfig.getDdFileSecretAccessKeyId());
    }

    @Bean(name = "ddFileJingdongStorageService")
    public JingdongStorageService ddFileJingdongStorageService(Credential ddFileCredential, ClientConfig clientConfig) {
        return new JingdongStorageService(ddFileCredential, clientConfig);
    }


}
