package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 绩效目标
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Getter
@Setter
@TableName("performance_target")
public class PerformanceTargetPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 绩效组id
     */
    private String businessLineId;

    /**
     * 考核方案Id
     */
    private String evaluationPlanCode;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 单价,单位元
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String price;

    /**
     * 产能目标
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cpd;


    /**
     * 月标准天数
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String days;

    /**
     * 绩效目标code
     */
    private String code;
}
