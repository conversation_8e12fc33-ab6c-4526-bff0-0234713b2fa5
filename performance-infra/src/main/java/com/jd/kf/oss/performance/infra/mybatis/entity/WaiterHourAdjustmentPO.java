package com.jd.kf.oss.performance.infra.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.kf.oss.performance.infra.mybatis.BaseEntity;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 指标数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@Setter
@TableName("waiter_hour_adjustment")
public class WaiterHourAdjustmentPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 客服erp
     */
    private String erp;

    /**
     * 姓名
     */
    private String name;

    /**
     * 抽掉工时数
     */
    private Object mobilizeHours;

    /**
     * 带训&淘金者标识
     */
    private String trainerGolddiggerFlag;
}
