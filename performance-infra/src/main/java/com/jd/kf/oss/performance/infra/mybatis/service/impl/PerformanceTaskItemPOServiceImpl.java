package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTaskItemPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTaskItemPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IPerformanceTaskItemPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务条目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-06
 */
@Service
public class PerformanceTaskItemPOServiceImpl extends ServiceImpl<PerformanceTaskItemPOMapper, PerformanceTaskItemPO> implements IPerformanceTaskItemPOService {

}
