package com.jd.kf.oss.performance.infra.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.jd.kf.oss.performance.domain.config.domain.dept.IDeptPathCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 部门路径缓存实现类
 * 使用 Guava Cache 实现本地缓存
 * 
 * <AUTHOR>
 * @date 2025/07/25
 */
@Slf4j
@Component
public class DeptPathCache implements IDeptPathCache {

    /**
     * 部门路径缓存
     * Key: tenantCode:deptId
     * Value: 部门路径名称
     */
    private final Cache<String, String> deptPathCache = CacheBuilder.newBuilder()
            .maximumSize(10000) // 最大缓存10000个部门路径
            .expireAfterWrite(30, TimeUnit.MINUTES) // 30分钟后过期
            .recordStats() // 启用统计信息
            .build();

    /**
     * 生成缓存Key
     * 
     * @param tenantCode 租户标识
     * @param deptId 部门ID
     * @return 缓存Key
     */
    private String buildCacheKey(String tenantCode, String deptId) {
        return tenantCode + ":" + deptId;
    }

    @Override
    public String getDeptPathName(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return null;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        return deptPathCache.getIfPresent(cacheKey);
    }

    @Override
    public void putDeptPathName(String tenantCode, String deptId, String deptPathName) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        deptPathCache.put(cacheKey, StringUtils.isBlank(deptPathName) ? "" : deptPathName);
        
        log.debug("缓存部门路径: tenantCode={}, deptId={}, pathName={}", tenantCode, deptId, deptPathName);
    }

    @Override
    public void putDeptPathNames(String tenantCode, Map<String, String> deptPathMap) {
        if (StringUtils.isBlank(tenantCode) || deptPathMap == null || deptPathMap.isEmpty()) {
            return;
        }
        
        for (Map.Entry<String, String> entry : deptPathMap.entrySet()) {
            putDeptPathName(tenantCode, entry.getKey(), entry.getValue());
        }
        
        log.debug("批量缓存部门路径: tenantCode={}, count={}", tenantCode, deptPathMap.size());
    }

    @Override
    public boolean containsDeptPath(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return false;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        return deptPathCache.getIfPresent(cacheKey) != null;
    }

    @Override
    public void removeDeptPath(String tenantCode, String deptId) {
        if (StringUtils.isBlank(tenantCode) || StringUtils.isBlank(deptId)) {
            return;
        }
        
        String cacheKey = buildCacheKey(tenantCode, deptId);
        deptPathCache.invalidate(cacheKey);
        
        log.debug("移除部门路径缓存: tenantCode={}, deptId={}", tenantCode, deptId);
    }

    @Override
    public void clearTenantCache(String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            return;
        }
        
        // 遍历所有缓存Key，移除指定租户的缓存
        String prefix = tenantCode + ":";
        Set<String> keysToRemove = new HashSet<>();
        
        for (String key : deptPathCache.asMap().keySet()) {
            if (key.startsWith(prefix)) {
                keysToRemove.add(key);
            }
        }
        
        deptPathCache.invalidateAll(keysToRemove);
        
        log.info("清空租户部门路径缓存: tenantCode={}, removedCount={}", tenantCode, keysToRemove.size());
    }

    @Override
    public void clearAll() {
        long sizeBefore = deptPathCache.size();
        deptPathCache.invalidateAll();
        
        log.info("清空所有部门路径缓存: removedCount={}", sizeBefore);
    }

    @Override
    public String getCacheStats() {
        return String.format("缓存大小: %d, 命中率: %.2f%%, 命中次数: %d, 未命中次数: %d",
                deptPathCache.size(),
                deptPathCache.stats().hitRate() * 100,
                deptPathCache.stats().hitCount(),
                deptPathCache.stats().missCount());
    }

    @Override
    public Set<String> getMissingDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || deptIds == null || deptIds.isEmpty()) {
            return deptIds == null ? new HashSet<>() : new HashSet<>(deptIds);
        }
        
        Set<String> missingIds = new HashSet<>();
        for (String deptId : deptIds) {
            if (!containsDeptPath(tenantCode, deptId)) {
                missingIds.add(deptId);
            }
        }
        
        log.debug("检查缓存未命中部门ID: tenantCode={}, totalCount={}, missingCount={}", 
                 tenantCode, deptIds.size(), missingIds.size());
        
        return missingIds;
    }
}
