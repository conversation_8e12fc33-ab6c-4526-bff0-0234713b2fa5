package com.jd.kf.oss.performance.infra.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/4/1 23:33
 * @Description:
 * @Version 1.0
 */
@Data
@Component
public class LocalConfig {

    /**
     * umpkey用于区分预发和线上，线上默认为空
     * 预发添加 uat前缀
     */
    @Value("${kf.performance.ump.cluster:}")
    private String umpCluster = "";


    public String getUmpKeyPrefix() {
        return umpCluster + "kf-performance";
    }

}
