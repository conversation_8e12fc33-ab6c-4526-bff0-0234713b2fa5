package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.EvaluatePO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.EvaluatePOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IEvaluatePOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 上级评价信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Slf4j
@Service
public class EvaluatePOServiceImpl extends ServiceImpl<EvaluatePOMapper, EvaluatePO> implements IEvaluatePOService {
    /**
     * 批量保存或更新评价数据
     * 根据period+erp+yn字段为唯一键来判断是insert还是update
     *
     * @param evaluatePOs 评价数据集合
     * @return 操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(Collection<EvaluatePO> evaluatePOs) {
        if (CollectionUtils.isEmpty(evaluatePOs)) {
            log.info("EvaluatePOServiceImpl saveOrUpdateBatch evaluatePOs is empty");
            return true;
        }

        try {
            List<EvaluatePO> evaluateList = new ArrayList<>(evaluatePOs);

            // 分批处理，避免SQL过长
            int batchSize = 500;
            for (int i = 0; i < evaluateList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, evaluateList.size());
                List<EvaluatePO> batch = evaluateList.subList(i, endIndex);
                processBatch(batch);
            }

            return true;
        } catch (Exception e) {
            log.error("EvaluatePOServiceImpl saveOrUpdateBatch error: {}", e.getMessage(), e);
            throw new RuntimeException("批量保存或更新评价数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个批次的数据
     *
     * @param batch 批次数据
     */
    private void processBatch(List<EvaluatePO> batch) {
        // 查询数据库中已存在的记录
        Map<String, EvaluatePO> existingRecords = queryExistingRecords(batch);

        // 分离新增和更新的数据
        List<EvaluatePO> toInsert = new ArrayList<>();
        List<EvaluatePO> toUpdate = new ArrayList<>();

        for (EvaluatePO evaluate : batch) {
            String uniqueKey = buildUniqueKey(evaluate);
            EvaluatePO existing = existingRecords.get(uniqueKey);

            if (existing != null) {
                // 存在记录，需要更新
                evaluate.setId(existing.getId());
                toUpdate.add(evaluate);
            } else  {
                // 不存在记录，需要新增
                evaluate.setId(null);
                toInsert.add(evaluate);
            }
        }

        // 执行批量新增
        if (!CollectionUtils.isEmpty(toInsert)) {
            saveBatch(toInsert);
            log.info("批量新增评价数据 {} 条", toInsert.size());
        }

        // 执行批量更新
        if (!CollectionUtils.isEmpty(toUpdate)) {
            updateBatchById(toUpdate);
            log.info("批量更新评价数据 {} 条", toUpdate.size());
        }
    }

    /**
     * 构建唯一键：period+erp+yn
     *
     * @param evaluate 评价对象
     * @return 唯一键字符串
     */
    private String buildUniqueKey(EvaluatePO evaluate) {
        return String.format("%s_%s",
                evaluate.getPeriod(),
                evaluate.getErp());
    }

    /**
     * 查询数据库中已存在的记录
     *
     * @param batch 批次数据
     * @return 已存在记录的Map，key为唯一键，value为记录对象
     */
    private Map<String, EvaluatePO> queryExistingRecords(List<EvaluatePO> batch) {
        // 提取所有的period、erp组合
        Set<String> periods = batch.stream()
                .map(EvaluatePO::getPeriod)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> erps = batch.stream()
                .map(EvaluatePO::getErp)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (periods.isEmpty() || erps.isEmpty()) {
            return new HashMap<>();
        }

        // 查询数据库中已存在的记录
        LambdaQueryWrapper<EvaluatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EvaluatePO::getPeriod, periods)
                   .in(EvaluatePO::getErp, erps)
                   .eq(EvaluatePO::getYn, YnEnum.VALID.getValue());

        List<EvaluatePO> existingList = list(queryWrapper);

        // 转换为Map，便于快速查找
        // 如果有重复key，保留第一个
        return existingList.stream()
                .collect(Collectors.toMap(
                        this::buildUniqueKey,
                        evaluate -> evaluate,
                        (existing, replacement) -> existing
                ));
    }
}
