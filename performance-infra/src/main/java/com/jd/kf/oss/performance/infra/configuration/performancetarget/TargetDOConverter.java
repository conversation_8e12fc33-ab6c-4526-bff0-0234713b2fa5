package com.jd.kf.oss.performance.infra.configuration.performancetarget;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceTargetPO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TargetDOConverter {
    TargetDOConverter INSTANCE = Mappers.getMapper(TargetDOConverter.class);

    PerformanceTargetDO PO2DO(PerformanceTargetPO performanceTargetPO);
    List<PerformanceTargetDO> POs2DOs(List<PerformanceTargetPO> pos);
    List<PerformanceTargetPO> DOs2POs(List<PerformanceTargetDO> dos);

    PerformanceTargetPO DO2PO(PerformanceTargetDO performanceTargetDO);

    PageCommand<PerformanceTargetPO> pageCommandDO2PO(PageCommand<PerformanceTargetDO> pageCommand);

    @Mapping(source = "records", target = "data")
    @Mapping(source = "current", target = "page")
    CommonPage<PerformanceTargetDO> pagePO2DO(Page<PerformanceTargetPO> page);

    // 已删除联表查询转换方法，现在直接在Mapper中返回TargetDO


}
