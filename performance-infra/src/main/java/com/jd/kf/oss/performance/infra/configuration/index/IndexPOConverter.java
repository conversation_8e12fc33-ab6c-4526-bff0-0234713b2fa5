package com.jd.kf.oss.performance.infra.configuration.index;

import com.jd.kf.oss.performance.domain.config.domain.index.IndexBasicDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexBasicPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceIndexPO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import com.jd.component.dal.util.StringUtils;

import java.util.List;

@Mapper
public interface IndexPOConverter {
    IndexPOConverter INSTANCE = Mappers.getMapper(IndexPOConverter.class);

    @Mapping(target = "status", expression = "java(indexDO.getStatus() == null ? null : indexDO.getStatus().getStatus())")
    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateEnumToCodeString")
    PerformanceIndexPO do2PO(IndexDO indexDO);

    @Mapping(target = "status", expression = "java(com.jd.kf.oss.performance.enums.IndexStatusEnum.getStatus(indexPO.getStatus()))")
    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    IndexDO po2DO(PerformanceIndexPO indexPO);

    @AfterMapping
    default void setIndexPlatformUrl(PerformanceIndexPO indexPO, @MappingTarget IndexDO doObj) {
        doObj.setIndexPlatformUrlCode(indexPO.getIndexPlatformUrlCode());
    }

    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    List<PerformanceIndexPO> dos2POs(List<IndexDO> indexDOs);
    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    List<IndexDO> po2DO(List<PerformanceIndexPO> indexPOs);

    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    List<IndexBasicDO> indexBasicPOs2DOs(List<IndexBasicPO> indexBasicPOS);
    @Mapping(source="status",target="status",qualifiedByName = "indexStatusStringToEnum")
    IndexBasicDO  indexBasicPO2DO(IndexBasicPO indexBasicPO);

    @Named( "indexTemplateCodeStringToEnum")
    default IndexTemplate indexTemplateCodeStringToEnum(String templateCode){
        return IndexTemplate.getByCode(templateCode);
    }
    @Named( "indexTemplateEnumToCodeString")
    default String indexTemplateEnumToCodeString(IndexTemplate template) {
        return template == null ? null : template.getCode();
    }

    @Named("indexStatusStringToEnum")
    default IndexStatusEnum indexStatusCodeStringToEnum(String status) {
        return IndexStatusEnum.getStatus(status);
    }

    @Named("indexStatusEnumToString")
    default String indexStatusEnumToCodeString(IndexStatusEnum status) {
        return status == null ? null : status.getValue();
    }
}
