package com.jd.kf.oss.performance.infra.mybatis.mybatisPlusConfig;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.domain.config.aggregate.common.Constants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.enums.YnEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 3/11/24 3:06 PM
 * @Description:
 * @Version 1.0
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill...");
        String operator = UserContextHolder.getOperator();
        String tenantCode = UserContextHolder.getTenantCode();

        // 只在字段为 null 时才进行填充
        this.strictInsertFill(metaObject, "tenantCode", String.class, tenantCode);
        this.strictInsertFill(metaObject, "yn", Boolean.class, YnEnum.VALID.getValue());
        this.strictInsertFill(metaObject, "created", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "creator", String.class, operator);
        this.strictInsertFill(metaObject, "editor", String.class, operator);
        this.strictInsertFill(metaObject, "modified", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        //TODO 鉴权携带租户信息、editor 信息
        String operator = UserContextHolder.getOperator();
        String tenantCode = UserContextHolder.getTenantCode();

        // 只在字段为 null 时才进行填充
        this.strictUpdateFill(metaObject, "tenantCode", String.class, tenantCode);
        this.strictUpdateFill(metaObject, "editor", String.class, operator);
        this.strictUpdateFill(metaObject, "modified", LocalDateTime.class, LocalDateTime.now());
    }

}
