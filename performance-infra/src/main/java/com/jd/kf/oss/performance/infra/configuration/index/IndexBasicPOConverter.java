package com.jd.kf.oss.performance.infra.configuration.index;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.kf.oss.performance.domain.config.aggregate.common.PageCommand;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexBasicPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.PerformanceIndexPO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import com.jd.component.dal.util.StringUtils;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;

import java.util.List;

@Mapper
public interface IndexBasicPOConverter {
    IndexBasicPOConverter INSTANCE = Mappers.getMapper(IndexBasicPOConverter.class);


    PageCommand<IndexBasicPO> pageCommandDO2PO(PageCommand<IndexDO> pageCommand);

    @Mapping(source = "records", target = "data")
    @Mapping(source = "current", target = "page")
    CommonPage<IndexDO> pageP02DO(Page<IndexBasicPO> page, @Context String urlPrefix);

    @Mapping(source = "status", target = "status", qualifiedByName = "indexStatusStringToEnum")
    IndexDO PO2DO(IndexBasicPO indexBasicPO);

    // 支持外部URL前缀参数的转换方法，使用不同的方法名避免歧义
    @Mapping(source = "status", target = "status", qualifiedByName = "indexStatusStringToEnum")
    IndexDO PO2DOWithUrlPrefix(IndexBasicPO indexBasicPO, @Context String urlPrefix);

    // 带外部参数的@AfterMapping方法，只对PO2DOWithUrlPrefix方法生效
    @AfterMapping
    default void setIndexPlatformUrlWithPrefix(IndexBasicPO indexBasicPO, @MappingTarget IndexDO doObj, @Context String urlPrefix) {
        if (StringUtils.isNotBlank(indexBasicPO.getIndexPlatformUrlCode())) {
            String fullUrl;
            if (StringUtils.isNotBlank(urlPrefix)) {
                // 使用传入的URL前缀
                fullUrl = urlPrefix + indexBasicPO.getIndexPlatformUrlCode();
            } else {
                // 使用默认URL前缀
                fullUrl = "http://one-service.jd.com/#/indicator/detail?index=" + indexBasicPO.getIndexPlatformUrlCode();
            }
            doObj.setIndexPlatformUrl(fullUrl);
        }
    }

    // 添加带URL前缀的列表转换方法
    default List<IndexDO> PO2DOListWithUrlPrefix(List<IndexBasicPO> indexBasicPOs, @Context String urlPrefix) {
        if (indexBasicPOs == null) {
            return null;
        }
        return indexBasicPOs.stream()
                .map(po -> PO2DOWithUrlPrefix(po, urlPrefix))
                .collect(java.util.stream.Collectors.toList());
    }
    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    List<IndexDO> po2DO(List<PerformanceIndexPO> indexPOS);

    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    List<PerformanceIndex> po2Index(List<PerformanceIndexPO> indexPOS);

    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    PerformanceIndex po2Index(PerformanceIndexPO indexPO);

    @Mapping(source = "template", target = "template", qualifiedByName = "indexTemplateCodeStringToEnum")
    IndexDO  po2DO(PerformanceIndexPO indexPO);

    PageCommand<IndexBasicPO> pageCommandDO2IndexBasicPO(PageCommand<IndexDO> pageCommand);

    @Named( "indexTemplateCodeStringToEnum")
    default IndexTemplate indexTemplateCodeStringToEnum(String templateCode){
        return IndexTemplate.getByCode(templateCode);
    }
    @Named( "indexTemplateEnumToCodeString")
    default String indexTemplateEnumToCodeString(IndexTemplate template) {
        return template == null ? null : template.getCode();
    }


    @Named( "indexStatusStringToEnum")
    default IndexStatusEnum indexStatusStringToEnum(String indexStatus){
        return IndexStatusEnum.getStatus(indexStatus);
    }

}
