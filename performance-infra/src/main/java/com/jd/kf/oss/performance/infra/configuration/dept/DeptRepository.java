package com.jd.kf.oss.performance.infra.configuration.dept;

import com.jd.kf.oss.performance.domain.config.domain.dept.IDeptRepository;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 部门仓储实现类
 * 
 * <AUTHOR>
 * @date 2025/07/17
 */
@Slf4j
@Service
public class DeptRepository implements IDeptRepository {

    @Resource
    private IWfcDeptPOService wfcDeptPOService;

    @Override
    public Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }

        try {
            // 查询所有Id包括的DeptPO
            List<WfcDeptPO> inputDeptList = wfcDeptPOService.lambdaQuery()
                    .in(WfcDeptPO::getDeptId, deptIds)
                    .list();

            // 获取deptIdPath解析出来的所有deptId
            Set<String> fullPathDeptIdSet = inputDeptList.stream()
                    .map(WfcDeptPO::getDeptPath)
                    .flatMap(deptPath -> Arrays.stream(deptPath.split("@")))
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(fullPathDeptIdSet)) {
                return Collections.emptyMap();
            }

            // 查询所有deptIdPath解析出来的所有deptPO
            List<WfcDeptPO> fullPathDeptList = wfcDeptPOService.lambdaQuery()
                    .in(WfcDeptPO::getDeptId, fullPathDeptIdSet)
                    .list();

            Map<String, String> deptIdToNameMap = fullPathDeptList.stream()
                    .collect(Collectors.toMap(WfcDeptPO::getDeptId, WfcDeptPO::getDeptName));

            return inputDeptList.stream().collect(Collectors.toMap(
                deptPO -> deptPO.getDeptId(),
                    wfcDeptPO ->  getDeptPathName(wfcDeptPO, deptIdToNameMap)
            ));
        } catch (Exception e) {
            log.warn("批量获取部门路径名称失败, tenantCode: {}, deptIds: {}", tenantCode, deptIds, e);
            return Collections.emptyMap();
        }
    }

    private String getDeptPathName(WfcDeptPO wfcDeptPO, Map<String, String> deptIdToDeptNameMap) {
        String deptPath = wfcDeptPO.getDeptPath();
        StringJoiner deptNameJoiner = new StringJoiner("-");

        if (StringUtils.isNotBlank(deptPath)) {
            String[] ids = deptPath.split("@");
            for (String id : ids) {
                Optional.ofNullable(deptIdToDeptNameMap.get(id))
                        .ifPresent(deptNameJoiner::add);
            }
        }

        return deptNameJoiner.toString();
    }
}
