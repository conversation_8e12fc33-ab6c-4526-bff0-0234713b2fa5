package com.jd.kf.oss.performance.infra.configuration.dept;

import com.jd.kf.oss.performance.domain.config.domain.dept.IDeptRepository;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 部门仓储实现类
 * 
 * <AUTHOR>
 * @date 2025/07/17
 */
@Slf4j
@Service
public class DeptRepository implements IDeptRepository {

    @Resource
    private IWfcDeptPOService wfcDeptPOService;

    @Override
    public Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds) {
        if (StringUtils.isBlank(tenantCode) || CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }

        try {
            // 优化：添加租户条件和有效性条件，减少查询数据量
            List<WfcDeptPO> inputDeptList = wfcDeptPOService.lambdaQuery()
                    .eq(WfcDeptPO::getTenantCode, tenantCode)
                    .eq(WfcDeptPO::getYn, 1)
                    .in(WfcDeptPO::getDeptId, deptIds)
                    .list();

            if (CollectionUtils.isEmpty(inputDeptList)) {
                return Collections.emptyMap();
            }

            // 优化：使用HashSet预分配容量，避免Stream开销
            Set<String> fullPathDeptIdSet = new HashSet<>();
            for (WfcDeptPO dept : inputDeptList) {
                String deptPath = dept.getDeptPath();
                if (StringUtils.isNotBlank(deptPath)) {
                    String[] pathIds = deptPath.split("@");
                    for (String pathId : pathIds) {
                        if (StringUtils.isNotBlank(pathId)) {
                            fullPathDeptIdSet.add(pathId);
                        }
                    }
                }
            }

            if (fullPathDeptIdSet.isEmpty()) {
                return Collections.emptyMap();
            }

            // 优化：添加租户条件，减少查询数据量
            List<WfcDeptPO> fullPathDeptList = wfcDeptPOService.lambdaQuery()
                    .eq(WfcDeptPO::getTenantCode, tenantCode)
                    .eq(WfcDeptPO::getYn, 1)
                    .in(WfcDeptPO::getDeptId, fullPathDeptIdSet)
                    .list();

            // 优化：预分配容量
            Map<String, String> deptIdToNameMap = new HashMap<>(fullPathDeptList.size());
            for (WfcDeptPO dept : fullPathDeptList) {
                deptIdToNameMap.put(dept.getDeptId(), dept.getDeptName());
            }

            // 优化：预分配容量，避免Stream开销
            Map<String, String> result = new HashMap<>(inputDeptList.size());
            for (WfcDeptPO dept : inputDeptList) {
                String pathName = getDeptPathName(dept, deptIdToNameMap);
                result.put(dept.getDeptId(), pathName);
            }

            return result;
        } catch (Exception e) {
            log.warn("批量获取部门路径名称失败, tenantCode: {}, deptIds: {}", tenantCode, deptIds, e);
            return Collections.emptyMap();
        }
    }

    private String getDeptPathName(WfcDeptPO wfcDeptPO, Map<String, String> deptIdToDeptNameMap) {
        String deptPath = wfcDeptPO.getDeptPath();
        StringJoiner deptNameJoiner = new StringJoiner("-");

        if (StringUtils.isNotBlank(deptPath)) {
            String[] ids = deptPath.split("@");
            for (String id : ids) {
                Optional.ofNullable(deptIdToDeptNameMap.get(id))
                        .ifPresent(deptNameJoiner::add);
            }
        }

        return deptNameJoiner.toString();
    }
}
