package com.jd.kf.oss.performance.infra.runtime.performanceplan;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.IPerformanceCoefficientRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceCoefficientDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceCoefficientItem;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.YnEnum;
import com.jd.kf.oss.performance.infra.configuration.coefficient.DOConverter;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientItemPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.CoefficientPO;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientItemPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.ICoefficientPOService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PerformanceCoefficientRepository implements IPerformanceCoefficientRepository {
    @Resource
    private ICoefficientPOService coefficientPOService;

    @Resource
    private ICoefficientItemPOService coefficientItemPOService;

    @Override
    public List<PerformanceCoefficientDO> queryAllCoefficientDO(String tenantCode, String period) {
        List<CoefficientPO> coefficientPOS = coefficientPOService.lambdaQuery()
                .eq(CoefficientPO::getTenantCode, tenantCode)
                .eq(CoefficientPO::getPeriod, period)
                .eq(CoefficientPO::getYn, YnEnum.VALID.getValue())
                .list();

        List<CoefficientItemPO> coefficientItemPOS = coefficientItemPOService.lambdaQuery()
                .eq(CoefficientItemPO::getTenantCode, tenantCode)
                .eq(CoefficientItemPO::getPeriod, period)
                .eq(CoefficientItemPO::getYn, YnEnum.VALID.getValue())
                .list();

        return convert(coefficientPOS, coefficientItemPOS);
    }

    /**
     * 将PO转换为聚合DO
     */
    public List<PerformanceCoefficientDO> convert(List<CoefficientPO> coefficientPOS, List<CoefficientItemPO> coefficientItemPOS) {
        if (CollectionUtils.isEmpty(coefficientPOS) || CollectionUtils.isEmpty(coefficientItemPOS)) {
            return Lists.newArrayList();
        }
        Multimap<String, CoefficientItemPO> coefficientItemPOMultimap = ArrayListMultimap.create();
        for (CoefficientItemPO coefficientItemPO : coefficientItemPOS) {
            coefficientItemPOMultimap.put(coefficientItemPO.getCoefficientCode(), coefficientItemPO);
        }
        List<PerformanceCoefficientDO> coefficientDOS = Lists.newArrayList();
        for (CoefficientPO coefficientPO : coefficientPOS) {
            PerformanceCoefficientDO coefficientDO = new PerformanceCoefficientDO();
            BeanUtils.copyProperties(coefficientPO, coefficientDO);
            coefficientDO.setType(CoefficientTemplateTypeEnum.getByType(coefficientPO.getType()));

            List<CoefficientItemPO> itemPOS = Lists.newArrayList(coefficientItemPOMultimap.get(coefficientDO.getCode()));

            List<PerformanceCoefficientItem> performanceCoefficientItems = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(itemPOS)) {
                for (CoefficientItemPO itemPO : itemPOS) {
                    PerformanceCoefficientItem coefficientItem = new PerformanceCoefficientItem();
                    BeanUtils.copyProperties(itemPO, coefficientItem);
                    performanceCoefficientItems.add(coefficientItem);
                }
                coefficientDO.setCoefficientItems(performanceCoefficientItems);
            }
            coefficientDOS.add(coefficientDO);
        }
        return coefficientDOS;
    }

}
