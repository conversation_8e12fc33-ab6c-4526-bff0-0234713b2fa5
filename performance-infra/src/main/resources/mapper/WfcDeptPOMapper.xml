<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.kf.oss.performance.infra.mybatis.mapper.WfcDeptPOMapper">

    <!-- 批量新增部门 -->
    <insert id="batchSaveOrUpdate" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO wfc_dept (
        tenant_code,
        uniq_id,
        biz_tenant_code,
        dept_id,
        dept_pid,
        dept_name,
        dept_code,
        dept_level,
        dept_path,
        dept_type,
        allow_del,
        dept_note,
        depart_dept,
        dept_manager,
        dept_channel,
        dept_business,
        yn,
        creator,
        editor,
        created,
        modified
        ) VALUES
        <foreach collection="deptList" item="item" separator=",">
            (
            #{item.tenantCode,jdbcType=VARCHAR},
            #{item.uniqId,jdbcType=BIGINT},
            #{item.bizTenantCode,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=VARCHAR},
            #{item.deptPid,jdbcType=VARCHAR},
            #{item.deptName,jdbcType=VARCHAR},
            #{item.deptCode,jdbcType=VARCHAR},
            #{item.deptLevel,jdbcType=INTEGER},
            #{item.deptPath,jdbcType=VARCHAR},
            #{item.deptType,jdbcType=VARCHAR},
            #{item.allowDel,jdbcType=TINYINT},
            #{item.deptNote,jdbcType=VARCHAR},
            #{item.departDept,jdbcType=TINYINT},
            #{item.deptManager,jdbcType=VARCHAR},
            #{item.deptChannel,jdbcType=VARCHAR},
            #{item.deptBusiness,jdbcType=VARCHAR},
            #{item.yn,jdbcType=TINYINT},
            #{item.creator,jdbcType=VARCHAR},
            #{item.editor,jdbcType=VARCHAR},
            #{item.created,jdbcType=TIMESTAMP},
            #{item.modified,jdbcType=TIMESTAMP}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        biz_tenant_code = VALUES(biz_tenant_code),
        dept_pid = VALUES(dept_pid),
        dept_name = VALUES(dept_name),
        dept_code = VALUES(dept_code),
        dept_level = VALUES(dept_level),
        dept_path = VALUES(dept_path),
        dept_type = VALUES(dept_type),
        allow_del = VALUES(allow_del),
        dept_note = VALUES(dept_note),
        depart_dept = VALUES(depart_dept),
        dept_manager = VALUES(dept_manager),
        dept_channel = VALUES(dept_channel),
        dept_business = VALUES(dept_business),
        yn = VALUES(yn),
        editor = VALUES(editor),
        modified = VALUES(modified)
    </insert>

    <update id="batchDeleteByIdList" parameterType="java.util.List">
        UPDATE wfc_dept
        SET
        yn = 0,
        uniq_id = id
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据上级部门ID查询所有下级部门ID列表（包含自身） -->
    <select id="querySubDeptIdsByParentDeptId" resultType="java.lang.String">
        SELECT dept_id
        FROM wfc_dept
        WHERE yn = 1
        AND tenant_code = #{tenantCode}
        AND (
            dept_id = #{parentDeptId}
            OR dept_path LIKE CONCAT('%@', #{parentDeptId}, '@%')
        )
    </select>

</mapper>
