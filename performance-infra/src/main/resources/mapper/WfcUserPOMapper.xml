<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.kf.oss.performance.infra.mybatis.mapper.WfcUserPOMapper">

    <update id="batchDeleteByIdList" parameterType="java.util.List">
        UPDATE wfc_user
        SET
        yn = 0,
        uniq_id = id
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
