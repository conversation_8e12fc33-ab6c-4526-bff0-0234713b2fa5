package com.jd.kf.oss.performance.utils;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.google.common.collect.ImmutableList;
import lombok.Getter;

import java.util.Collection;
import java.util.EnumSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: im-roster
 * @description:
 * @author: zhubin64
 * @create: 2021-12-21 14:23
 **/
@Getter
public enum AgentTypeEnum {


    WINDOWS(ImmutableList.of("Windows"), ExcelTypeEnum.XLS),
    MAC(ImmutableList.of("Mac OS"), ExcelTypeEnum.XLSX),
    OTHER(ImmutableList.of("Linux", "iPad", "Android", "FreeBSD", "Solaris"), ExcelTypeEnum.CSV);

    private Collection<String> agents;

    private ExcelTypeEnum excelTypeEnum;


    AgentTypeEnum(Collection<String> agents, ExcelTypeEnum excelTypeEnum) {
        this.agents = agents;
        this.excelTypeEnum = excelTypeEnum;
    }

    AgentTypeEnum() {
    }

    static Map<Collection<String>, ExcelTypeEnum> excelTypeEnumMap =
            getEnumSet().stream().collect(Collectors.toMap(AgentTypeEnum::getAgents, AgentTypeEnum::getExcelTypeEnum));


    public static ExcelTypeEnum valueOfAgents(String agent) {
        for (Entry<Collection<String>, ExcelTypeEnum> entry : excelTypeEnumMap.entrySet()) {
            for (String s : entry.getKey()) {
                if (agent.trim().contains(s)) {
                    return entry.getValue();
                }
            }
        }
        return ExcelTypeEnum.XLSX;
    }

    public static AgentTypeEnum valueOfExcelTypeEnum(ExcelTypeEnum excelTypeEnum) {
        for (AgentTypeEnum obj : AgentTypeEnum.values()) {
            if (java.util.Objects.equals(obj.excelTypeEnum, excelTypeEnum)) {
                return obj;
            }
        }
        return null;
    }

    public Collection<String> getAgents() {
        return agents;
    }

    public ExcelTypeEnum getExcelTypeEnum() {
        return excelTypeEnum;
    }

    public static Set<AgentTypeEnum> getEnumSet() {
        return EnumSet.allOf(AgentTypeEnum.class);
    }
}
