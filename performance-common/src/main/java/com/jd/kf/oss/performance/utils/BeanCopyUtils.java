package com.jd.kf.oss.performance.utils;

import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.objenesis.ObjenesisStd;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
public class BeanCopyUtils {

    private static final ThreadLocal<ObjenesisStd> objenesisStdThreadLocal = ThreadLocal
            .withInitial(ObjenesisStd::new);
    private static final ConcurrentHashMap<Class<?>, ConcurrentHashMap<Class<?>, BeanCopier>> cache = new ConcurrentHashMap<>();


    public static <T> T copy(Object source, Class<T> target) {
        return copy(source, objenesisStdThreadLocal.get().newInstance(target));
    }


    private static <T> T copy(Object source, T target) {
        BeanCopier beanCopier = getCacheBeanCopier(source.getClass(), target.getClass());
        beanCopier.copy(source, target, null);
        return target;
    }

    /**
     * 普通copy 集合
     */
    public static <T> List<T> copyList(List<?> sources, Class<T> target) {
        if (sources.isEmpty()) {
            return Collections.emptyList();
        }
        ArrayList<T> list = new ArrayList<>(sources.size());
        ObjenesisStd objenesisStd = objenesisStdThreadLocal.get();
        for (Object source : sources) {
            if (source == null) {
                return new ArrayList<>();
            }
            T newInstance = objenesisStd.newInstance(target);
            BeanCopier beanCopier = getCacheBeanCopier(source.getClass(), target);
            beanCopier.copy(source, newInstance, null);
            list.add(newInstance);
        }
        return list;
    }

    /**
     * 普通copy 集合
     */
    public static <T> List<T> copyList(List<?> sources, Class<T> target, Consumer<T> fillingConsumer) {
        if (sources.isEmpty()) {
            return Collections.emptyList();
        }
        ArrayList<T> list = new ArrayList<>(sources.size());
        ObjenesisStd objenesisStd = objenesisStdThreadLocal.get();
        for (Object source : sources) {
            if (source == null) {
                return new ArrayList<>();
            }
            T newInstance = objenesisStd.newInstance(target);
            BeanCopier beanCopier = getCacheBeanCopier(source.getClass(), target);
            beanCopier.copy(source, newInstance, null);
            fillingConsumer.accept(newInstance);
            list.add(newInstance);
        }
        return list;
    }

    public static <T> T mapToBean(Map<?, ?> source, Class<T> target) {
        T bean = objenesisStdThreadLocal.get().newInstance(target);
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(source);
        return bean;
    }


    public static <T> Map<?, ?> beanToMap(T source) {
        return BeanMap.create(source);
    }


    public static <T> Map<String, Object> beanToObjectMap(T source) {
        BeanMap beanMap = BeanMap.create(source);
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(beanMap.size());
        for (Object key : beanMap.keySet()) {
            map.put(key.toString(), beanMap.get(key));
        }
        return map;
    }

    public static <T> Map<String, String> beanToStringMap(T source) {
        BeanMap beanMap = BeanMap.create(source);
        Map<String, String> map = Maps.newHashMapWithExpectedSize(beanMap.size());
        for (Object key : beanMap.keySet()) {
            if (key != null && beanMap.get(key) != null) {
                map.put(key.toString(), beanMap.get(key).toString());
            }
        }
        return map;
    }

    /**
     * 带缓存
     */
    private static <S, T> BeanCopier getCacheBeanCopier(Class<S> source, Class<T> target) {
        ConcurrentHashMap<Class<?>, BeanCopier> copierConcurrentHashMap = cache
                .computeIfAbsent(source, aClass -> new ConcurrentHashMap<>(16));
        return copierConcurrentHashMap
                .computeIfAbsent(target, aClass -> BeanCopier.create(source, target, false));
    }


}

