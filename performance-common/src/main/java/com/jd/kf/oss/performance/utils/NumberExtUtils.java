package com.jd.kf.oss.performance.utils;

import org.apache.commons.lang3.math.NumberUtils;

import java.util.function.Supplier;
import java.util.regex.Pattern;

public class NumberExtUtils {
        // 正则表达式模式，用于匹配整数
        private static final Pattern INTEGER_PATTERN = Pattern.compile("^-?\\d+$");

        // 正则表达式模式，用于匹配浮点数
        private static final Pattern DOUBLE_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");


        /**
         * 检查字符串是否表示一个整数。
         *
         * @param str 要检查的字符串
         * @return 如果字符串表示一个整数，则返回 true，否则返回 false
         */
        public static boolean isInteger(String str) {
            return str!=null && INTEGER_PATTERN.matcher(str).matches();
        }

        /**
         * 检查字符串是否表示一个浮点数。
         *
         * @param str 要检查的字符串
         * @return 如果字符串表示一个浮点数，则返回 true，否则返回 false
         */
        public static boolean isDouble(String str) {
            return str!=null && DOUBLE_PATTERN.matcher(str).matches();
        }

        /**
         * 尝试将字符串解析为整数，如果解析失败则返回 null。
         *
         * @param str 要解析的字符串
         * @return 解析后的整数，或者 null 如果解析失败
         */
        public static Integer tryParseInteger(String str) {
            if (isInteger(str)) {
                try {
                    return Integer.parseInt(str);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
            return null;
        }

        /**
         * 尝试将字符串解析为浮点数，如果解析失败则返回 null。
         *
         * @param str 要解析的字符串
         * @return 解析后的浮点数，或者 null 如果解析失败
         */
        public static Double tryParseDouble(String str) {
            if (isDouble(str)) {
                try {
                    return Double.parseDouble(str);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
            return null;
        }


        public static Double parseDouble(String num, Supplier<Double> defaultNum){
            if(isDouble(num)){
                return Double.parseDouble(num);
            }
            return defaultNum.get();
        }


        public static Double parseDoubleOrDefault(String num,Double defaultNum){
            if(isDouble(num)){
                return Double.parseDouble(num);
            }
            return defaultNum;
        }


        public static boolean isNumber(String str) {
            return NumberUtils.isParsable(str);
        }
    }
