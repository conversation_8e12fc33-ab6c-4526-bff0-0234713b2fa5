package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Consumer;

/**
 * @author:zmf
 * @date:2021/8/27
 */
@Slf4j
public class EasyExcelBatchStringListener<T> extends AnalysisEventListener<Map<Integer, String>> {

    private final int batchCount;

    private static final ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(new JavaTimeModule());

    private  Class<T> tClass;

    private final BiFunction<Map<Integer, String>, List<Map<Integer, String>>, List<T>> buildFunction;
    private final Consumer<List<T>> dataListProcessConsumer;
    private final List<Map<Integer, String>> dataList = new ArrayList<>();
    private Map<Integer, String> singleHeadMap = Maps.newHashMap();

    public EasyExcelBatchStringListener(int batchCount, Consumer<List<T>> dataListProcessConsumer,
                                        BiFunction<Map<Integer, String>, List<Map<Integer, String>>, List<T>> buildFunction) {
        this.batchCount = batchCount;
        this.dataListProcessConsumer = dataListProcessConsumer;
        this.buildFunction = buildFunction;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        dataList.add(data);
        if (data.size() == batchCount) {
            List<T> list = buildFunction.apply(singleHeadMap, dataList);
            dataListProcessConsumer.accept(list);
            dataList.clear();
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        singleHeadMap = headMap;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

        log.info("all data analysed！");
    }


    private T buildUserModel(Map<Integer,String> rowValue,Class<T> tclass) {

        Map<String, String> map = new HashMap<>();
        for (Map.Entry<Integer, String> entry : rowValue.entrySet()) {
            Integer index = entry.getKey();
            String value = entry.getValue();
            String key = singleHeadMap.get(index);
            if (key != null) {
                map.put(key, value);
            }
        }
        return mapper.convertValue(map, tclass);
    }



}
