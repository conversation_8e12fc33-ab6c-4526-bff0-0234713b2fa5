package com.jd.kf.oss.performance.context;

import com.jd.kf.oss.performance.enums.DataAuthEnum;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Builder
@ToString
@Accessors(chain = true)
public class UserContext {

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 用户PIN码
     */
    private String pin;

    /**
     * ERP账号
     */
    private String erp;

    /**
     * 操作员ID
     */
    private String opId;

    /**
     * 用户Cookie信息
     *
     */
    private String cookie;

    /**
     * 数据权限枚举
     *
     */
    private DataAuthEnum dataAuth;
}
