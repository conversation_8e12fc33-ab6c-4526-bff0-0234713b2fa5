package com.jd.kf.oss.performance.exception;

import com.jd.kf.oss.performance.enums.ResultCodeEnum;

public class BizException extends RuntimeException {

    private String code;
    private Exception exception;
    private String errorMsg;

    public BizException() {
        super();
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(ResultCodeEnum codeEnum) {
        super(codeEnum.getMessage());
        this.code = codeEnum.getCode();
    }

    public BizException(ResultCodeEnum codeEnum, String errorMsg) {
        super(codeEnum.getMessage());
        this.code = codeEnum.getCode();
        this.errorMsg = errorMsg;
    }

    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(String message, Exception e) {
        super(message);
        this.exception = e;
    }

    public BizException(String message, String errorMsg, Exception e) {
        super(message);
        this.exception = e;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}
