package com.jd.kf.oss.performance.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/07/01
 */
@AllArgsConstructor
@Getter
public enum IndexTypeEnum {
    QUALITY("quality", "质量指标"),
    CAPACITY("capacity", "产能指标"),
    CONSTANT("constant", "常量");

    private final String type;
    private final String desc;

    public String getValue() {
        return this.type;
    }

    /**
     * 根据类型字符串获取对应的索引类型枚举值
     * @param type 索引类型字符串
     * @return 匹配的索引类型枚举值，若未匹配到或输入为null则返回null
     */
    public static IndexTypeEnum getType(String type) {
        if (type == null) {
            return null;
        }
        for (IndexTypeEnum value : values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }
}
