package com.jd.kf.oss.performance.utils.file;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

public class FileUtil {

    private static final Logger log = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 如果文件夹不存在则创建
     *
     * @param dir
     * @return
     */
    public static boolean checkAndMakeDir(String dir) {
        File file = new File(dir);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
            return false;
        }
        return true;
    }

    /**
     * 删除文件
     *
     * @param filePath 将要删除的文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists() && !file.isDirectory()) {
                return file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 取得下载到本地的文件路径
     *
     * @return
     */
    public static String getLocalDataDir(String localDataDir) {
        String dir = "";
        try {
            dir = localDataDir;
            String separator = File.separator;
            if (!localDataDir.endsWith(separator)) {
                dir += separator;
            }
        } catch (Exception e) {
            log.error("[FileUtil] getLocalDataDir 获取本地存储目录失败dir:" + dir, e);
        }
        return dir;
    }


}