package com.jd.kf.oss.performance.enums;


import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <AUTHOR>
 * @date 2025/07/01
 */
@AllArgsConstructor
@Getter
public enum IndexStatusEnum {

    VALID("有效"),
    INVALID("无效");

    @JsonValue
    private final String status;

    public String getValue() {
        return this.status;
    }

    public static IndexStatusEnum getStatus(String status) {
        if (status == null) {
            return null;
        }
        for (IndexStatusEnum value : values()) {
            if (Objects.equals(value.getStatus(), status)) {
                return value;
            }
        }
        return null;
    }
}
