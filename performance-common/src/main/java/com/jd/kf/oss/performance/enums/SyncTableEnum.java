package com.jd.kf.oss.performance.enums;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum SyncTableEnum {

    TABLE_HR_BASE_USER("cchome零售人员信息主表", "wfc_hr_base_user"),

    TABLE_AUTH_DEPT("cchome零售部门信息表", "wfc_auth_dept"),
    ;

    private final String msg;

    private final String tableName;

    private static final Map<String, SyncTableEnum> TABLE_NAME_MAP = Stream.of(values())
            .collect(Collectors.toMap(
                    SyncTableEnum::getTableName, Function.identity()
            ));

    /**
     * 构造函数
     * @param msg 表描述
     * @param tableName 表名
     */
    SyncTableEnum(String msg, String tableName) {
        this.msg = msg;
        this.tableName = tableName;
    }

    public String getMsg() {
        return msg;
    }

    public String getTableName() {
        return tableName;
    }

    /**
     * 根据表名获取对应的枚举值
     * @param tableName 表名
     * @return 对应的枚举值,如果不存在返回null
     */
    public static SyncTableEnum getByTableName(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return null;
        }
        return TABLE_NAME_MAP.get(tableName);
    }

    /**
     * 检查表名是否存在
     * @param tableName 表名
     * @return 是否存在
     */
    public static boolean contains(String tableName) {
        return TABLE_NAME_MAP.containsKey(tableName);
    }

}
