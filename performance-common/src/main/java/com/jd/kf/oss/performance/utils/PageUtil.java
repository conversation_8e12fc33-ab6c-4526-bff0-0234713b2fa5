package com.jd.kf.oss.performance.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


public class PageUtil {

    /**
     * 将pageSize，pageNO转为mybatis-plus的page对象
     */
    public static <T> Page<T> toMybatisPage(Integer pageNo, Integer pageSize) {
        return Page.of(pageNo, pageSize);
    }

    /**
     * 将MP的page对象转为CommonPage
     */
    public static <T> CommonPage<T> toCommonPage(Page<T> page) {
        return CommonPage.getCommonPage(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }





}
