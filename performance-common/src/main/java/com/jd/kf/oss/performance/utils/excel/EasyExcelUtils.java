package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.AgentTypeEnum;
import lombok.Getter;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * @author:zmf
 * @date:2021/8/26
 */
public class EasyExcelUtils {

    private static final String agent = "User-Agent";

    /**
     * 生成Excel文件并返回字节流
     * @param heads 表头列表
     * @param dataList 数据列表
     * @param writeHandler 写入处理器
     * @return Excel文件的字节流
     */
    public static ByteArrayOutputStream generateExcel(List<List<String>> heads,
                                                      List<List<String>> dataList,
                                                      WriteHandler writeHandler) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(writeHandler)
                .build();
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setHead(heads);
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
        return outputStream;
    }

    public static <T> ExcelWriter buildExcel(Class<T> tClass, OutputStream outputStream) {
        return new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .head(tClass)
                .build();
    }

    public static <T> ExcelWriter buildExcel(String fileName, Class<T> tClass, HttpServletResponse response) {
        OutputStream outputStream = getOutputStream(fileName, response, ExcelTypeEnum.XLSX.getValue());
        return new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .head(tClass)
                .build();
    }

    public static <T> void writeExcelSheet(ExcelWriter excelWriter, WriteSheet writeSheet,
                                           EasyExcelExportSheetExtract<T> sheetExtract) {
        writeSheet.setSheetNo(sheetExtract.getSheetNo());
        writeSheet.setSheetName(sheetExtract.getSheetName());
        excelWriter.write(sheetExtract.getSheetExtractData(), writeSheet);
    }

    public static void finish(ExcelWriter excelWriter) {
        excelWriter.finish();
    }

    public static WriteSheet buildWriteSheet() {
        return new WriteSheet();
    }

    /**
     * 同步顺序写导出excel
     * 使用默认文件后缀
     *
     * @param fileName      文件名
     * @param tClass        对象映射(属性实现@ExcelProperty注解)
     * @param sheetExtracts sheet数据抽取对象
     * @param response      输出流
     * @param <T>           t
     */
    public static <T> void generateExcelAndExport(String fileName, Class<T> tClass,
                                                  List<EasyExcelExportSheetExtract<T>> sheetExtracts,
                                                  HttpServletResponse response) {
        ExcelWriter excelWriter = buildExcel(fileName, tClass, response);
        for (EasyExcelExportSheetExtract<T> sheetExtract : sheetExtracts) {
            WriteSheet writeSheet = buildWriteSheet();
            writeExcelSheet(excelWriter, writeSheet, sheetExtract);
        }
        finish(excelWriter);
    }


    /**
     * 导出单个sheet页签
     * @param tClass
     * @param data
     * @param request
     * @param response
     */
    public static <T> void exportSingleSheet(String fileName, Class<T> tClass, List<T> data,
                                             HttpServletRequest request, HttpServletResponse response) {
        EasyExcelExportSheetExtract<T> sheetExtract = buildSingleExportSheet(data, fileName);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(request);
        try{
            addResponseHead(response, fileName, excelType.getValue());
            OutputStream outputStream = response.getOutputStream();
            exportByClassMapping( tClass ,sheetExtract,outputStream,excelType);
        }catch (Exception e){
            throw new BizException("导出excel表格失败!", e);
        }
    }

    public static <T> ByteArrayOutputStream exportSingleSheet(String sheetName, Class<T> tClass, List<T> data,
                                             HttpServletRequest request) {
        EasyExcelExportSheetExtract<T> sheetExtract = buildSingleExportSheet(data,sheetName);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(request);
        try{
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            exportByClassMapping( tClass ,sheetExtract,outputStream,excelType);
            return outputStream;
        }catch (Exception e){
            throw new BizException("导出excel表格失败!", e);
        }
    }

    private static <T> EasyExcelExportSheetExtract<T> buildSingleExportSheet(List<T> data,String filename) {
        EasyExcelExportSheetExtract<T> sheetExtract = new EasyExcelExportSheetExtract<>();
        sheetExtract.setSheetExtractData(data);
        sheetExtract.setSheetNo(1);
        sheetExtract.setSheetName(filename);
        return sheetExtract;
    }


    public static <T> void exportByClassMapping(Class<T>  tClass,
                                              EasyExcelExportSheetExtract<T> sheetExtract,
                                              OutputStream outputStream,
                                              ExcelTypeEnum excelType) {

        ExcelWriter excelWriter = new ExcelWriterBuilder().file(outputStream).excelType(excelType).head(tClass).build();
        try {
            WriteSheet writeSheet = buildWriteSheet();
            writeExcelSheet(excelWriter, writeSheet, sheetExtract);
        }catch (Exception e){
            throw new BizException("导出excel表格失败!", e);
        }finally {
            finish(excelWriter);
        }

    }

    public static <T> ByteArrayOutputStream generateExcel(Class<T> tClass,
                                                          List<EasyExcelExportSheetExtract<T>> sheetExtracts) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter = buildExcel(tClass, outputStream);
        for (EasyExcelExportSheetExtract<T> sheetExtract : sheetExtracts) {
            WriteSheet writeSheet = buildWriteSheet();
            writeExcelSheet(excelWriter, writeSheet, sheetExtract);
        }
        finish(excelWriter);
        return outputStream;
    }


    public static void generateCsvAndExport(String fileName, HttpServletResponse response,
                                            List<List<String>> heads, List<List<String>> dataList) {
        OutputStream outputStream = getOutputStream(fileName, response, ExcelTypeEnum.CSV.getValue());
        ExcelWriter excelWriter = new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.CSV)
                .charset(Charset.forName("GBK"))
                .build();
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setHead(heads);
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
    }

    public static ByteArrayOutputStream generateExcelAndExport(
            List<List<String>> heads, List<List<String>> dataList,
            HttpServletRequest httpServletRequest) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        ExcelWriter excelWriter = new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(AgentTypeEnum.valueOfAgents(httpServletRequest.getHeader(agent)))
                .registerWriteHandler(new ExcelCellWidthStyleStrategy())
                .registerWriteHandler(new ExcelStyleTextWriteHandler())
                .build();
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setHead(heads);
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
        return outputStream;
    }

    /**
     * 通过客户端类型选择文件后缀
     *
     * @param httpServletRequest
     * @return
     */
    public static ExcelTypeEnum getExcelTypeByAgentOrDefault(HttpServletRequest httpServletRequest) {
        return AgentTypeEnum.valueOfAgents(httpServletRequest.getHeader(agent));
    }




    public static ByteArrayOutputStream generateExcelAndExport(List<EasyExcelExportStringSheetExtract> stringSheetExtracts,
                                                               ExcelTypeEnum excelType) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        generateExcelAndExport(stringSheetExtracts, excelType, outputStream);
        return outputStream;
    }

    public static void generateExcelAndExport(List<EasyExcelExportStringSheetExtract> stringSheetExtracts,
                                              ExcelTypeEnum excelType,OutputStream outputStream) {

        try(ExcelWriter excelWriter = new ExcelWriterBuilder().file(outputStream).excelType(excelType).build()){
            for (EasyExcelExportStringSheetExtract sheetExtract : stringSheetExtracts) {
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setHead(sheetExtract.getHeads());
                writeSheet.setSheetNo(sheetExtract.getSheetNo());
                writeSheet.setSheetName(sheetExtract.getSheetName());
                excelWriter.write(sheetExtract.getDataList(), writeSheet);
            }
        } catch (Exception e) {
            throw new BizException("导出excel表格失败!"+e.getMessage(), e);
        }
    }


    public static void generateExcelAndExport(String fileName,
                                              EasyExcelExportStringSheetExtract stringSheetExtract,
                                              HttpServletRequest httpServletRequest,
                                              HttpServletResponse response) {
        List<EasyExcelExportStringSheetExtract> sheetExtracts = new ArrayList<>();
        sheetExtracts.add(stringSheetExtract);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(httpServletRequest);
        try {
            addResponseHead(response, fileName, excelType.getValue());
            OutputStream outputStream = response.getOutputStream();
            generateExcelAndExport(sheetExtracts, excelType, outputStream);
        }catch (UnsupportedEncodingException e){
            throw new BizException("设置响应头失败", e);
        } catch (IOException e) {
            throw new BizException("获取输出流失败", e);
        }
    }

    public static ByteArrayOutputStream generateExcelAndExport(EasyExcelExportStringSheetExtract stringSheetExtract,
                                              HttpServletRequest httpServletRequest) {
        List<EasyExcelExportStringSheetExtract> sheetExtracts = new ArrayList<>();
        sheetExtracts.add(stringSheetExtract);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(httpServletRequest);
        return generateExcelAndExport(sheetExtracts, excelType);
    }

    /**
     * 根据模版生成Excel并导出
     *
     * @param fileName
     * @param templateFileName
     * @param clazz
     * @param writeHandlers
     * @param data
     * @param <T>
     */

    public static <T> void generateExcelAndExport(String fileName, String templateFileName, Class<T> clazz,
                                                  List<WriteHandler> writeHandlers, List<T> data) {
//        .registerWriteHandler(new CustomCellWriteHandler())
        // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
        ExcelWriterBuilder builder = EasyExcel.write(fileName, clazz);
        for (WriteHandler handler : writeHandlers) {
            builder.registerWriteHandler(handler);
        }
        builder.withTemplate(templateFileName).sheet().doWrite(data);
    }

    private static OutputStream getOutputStream(String fileName, HttpServletResponse response, String fileFormat) {
        try {
            fileName = URLEncoder.encode(fileName+fileFormat, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (Exception e) {
            throw new BizException("导出excel表格失败!", e);
        }
    }

    private static void addResponseHead( HttpServletResponse response, String fileName,String fileFormat) throws UnsupportedEncodingException {

            fileName = URLEncoder.encode(fileName+fileFormat, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");

    }

    /**
     * 多Sheet页签导出
     *
     * @param sheetDataList      sheet页数据
     * @param httpServletRequest 请求Request
     * @return
     */
    public static ByteArrayOutputStream generateExcelAndExport(
            List<ExcelSheetData> sheetDataList,
            HttpServletRequest httpServletRequest) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        ExcelWriter excelWriter = new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(AgentTypeEnum.valueOfAgents(httpServletRequest.getHeader(agent)))
                .registerWriteHandler(new ExcelCellWidthStyleStrategy())
                .registerWriteHandler(new ExcelStyleTextWriteHandler())
                .build();

        try {
            // 遍历所有sheet数据并写入
            for (int i = 0; i < sheetDataList.size(); i++) {
                ExcelSheetData sheetData = sheetDataList.get(i);
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setSheetName(sheetData.getSheetName() != null ?
                        sheetData.getSheetName() : "Sheet" + (i + 1));
                writeSheet.setHead(sheetData.getHead());
                excelWriter.write(sheetData.getDataList(), writeSheet);
            }
        } finally {
            // 确保无论是否发生异常都能关闭资源
            excelWriter.finish();
        }

        return outputStream;
    }

    /**
     * 统计Excel文件的行数
     * @param file 上传的Excel文件
     * @return 文件总行数
     * @throws IOException 文件读取异常
     */
    public static Integer countFileRows(MultipartFile file) throws IOException {
        CountRowsListener countRowsListener = new CountRowsListener();
        EasyExcel.read(file.getInputStream(),countRowsListener).sheet().doRead();
        return countRowsListener.getTotalRowCount();
    }

    public static Integer countFileRows(File file) throws IOException {
        CountRowsListener countRowsListener = new CountRowsListener();
        EasyExcel.read(file,countRowsListener).sheet().doRead();
        return countRowsListener.getTotalRowCount();
    }

    @Getter
    public static class CountRowsListener implements   ReadListener<Object> {
        private  Integer totalRowCount=0;
        @Override
        public void invoke(Object data, AnalysisContext context) {
            totalRowCount++;
        }
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }
    }

}
