package com.jd.kf.oss.performance.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 用于指标与因子中定义的指标进行映射
 */
@Getter
public enum IndexTemplate {

    QUALITY1("质量指标1", "index_quality_1", IndexTypeEnum.QUALITY.getDesc()),
    QUALITY2("质量指标2", "index_quality_2", IndexTypeEnum.QUALITY.getDesc()),
    QUALITY3("质量指标3", "index_quality_3", IndexTypeEnum.QUALITY.getDesc()),
    QUALITY4("质量指标4", "index_quality_4", IndexTypeEnum.QUALITY.getDesc()),
    CAPACITY1("产能指标1", "index_capacity_1",IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY2("产能指标2", "index_capacity_2", IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY3("产能指标3", "index_capacity_3", IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY4("产能指标4", "index_capacity_4", IndexTypeEnum.CAPACITY.getDesc());


    private final String templateName;

    @EnumValue
    @JsonValue
    private final String code;

    private final String type;

    /**
     * 构造函数
     * @param templateName 模板名称
     * @param code 模板代码
     * @param type 指标类型
     */
    IndexTemplate(String templateName, String code, String type) {
        this.templateName = templateName;
        this.code = code;
        this.type = type;
    }

    /**
     * 根据指标模板名称获取指标模板
     */
    public static IndexTemplate getByTemplateName(String templateName) {
        for (IndexTemplate template : IndexTemplate.values()) {
            if (template.getTemplateName().equals(templateName)) {
                return template;
            }
        }
        return null;
    }



    public static IndexTemplate getByCapacityIndex(String code) {
        String templateName = IndexTypeEnum.CAPACITY.getDesc()+code;
         return getByTemplateName(templateName);
    }

    public static IndexTemplate getByQualityIndex(String code) {
        String templateName = IndexTypeEnum.QUALITY.getDesc() +code;
        return getByTemplateName(templateName);
    }


    public static IndexTemplate getByCode(String code) {
        for (IndexTemplate template : IndexTemplate.values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }

    public  boolean isQualityIndex() {
        return IndexTypeEnum.QUALITY.getDesc().equals(type);
    }


    public IndexTemplate getIndexTemplate(String code) {
        for (IndexTemplate template : IndexTemplate.values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }
    public static boolean isIndexTemplate(String code) {
        for (IndexTemplate template : IndexTemplate.values()) {
            if (template.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
