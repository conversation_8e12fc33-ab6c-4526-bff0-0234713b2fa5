package com.jd.kf.oss.performance.utils;

import com.jd.kf.oss.performance.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

import static com.jd.kf.oss.performance.enums.ResultCodeEnum.VALIDATE_CHECK_ERROR;

@Slf4j
public class CheckUtil {


    public static void isTrue(boolean value, String msg) {
        if (BooleanUtils.isTrue(value)) {
            return;
        }
        log.info("CheckUtil isTrue fail value:{} msg:{}", value, msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }
    public static void isTrue(Boolean value, String msg) {
        if (BooleanUtils.isTrue(value)) {
            return;
        }
        log.info("CheckUtil isTrue fail value:{} msg:{}", value, msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void isFalse(Boolean value, String msg) {
        if (BooleanUtils.isFalse(value)) {
            return;
        }
        log.info("CheckUtil isFalse fail value:{} msg:{}", value, msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void checkPageSize(Integer pageSize) {
        if (pageSize == null || pageSize <= 0) {
            throw new BizException(VALIDATE_CHECK_ERROR.getCode(), "每页大小必须大于0");
        }

        if (pageSize > 1000) {
            throw new BizException(VALIDATE_CHECK_ERROR.getCode(), "每页大小不能超过1000");
        }
    }


    public static void notNull(Object obj, String msg) {
        if (Objects.nonNull(obj)) {
            return;
        }
        log.info("CheckUtil notNull fail msg:{}", msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);

    }
    public static void notNull(Object obj1,Object obj2 ,Object obj3,Object obj4 ,String msg) {
        Objects.requireNonNull(obj1,msg);
        Objects.requireNonNull(obj2,msg);
        Objects.requireNonNull(obj3,msg);
        Objects.requireNonNull(obj4,msg);
    }
    public static void notNull(Object obj1,Object obj2 ,Object obj3,String msg) {
        Objects.requireNonNull(obj1,msg);
        Objects.requireNonNull(obj2,msg);
        Objects.requireNonNull(obj3,msg);
    }
    public static void notNull(Object obj1,Object obj2 ,String msg) {
        Objects.requireNonNull(obj1,msg);
        Objects.requireNonNull(obj2,msg);

    }

    public static <T extends Collection<?>> void notEmpty(T collection, String msg) {
        if (CollectionUtils.isNotEmpty(collection)) {
            return;
        }
        log.info("CheckUtil notEmpty Collection fail msg:{}", msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void size(Collection<?> collection, int size, String msg) {
        if (CollectionUtils.size(collection) == size) {
            return;
        }
        log.info("CheckUtil size Collection fail msg:{}, expected size: {}, actual size: {}", msg, size, CollectionUtils.size(collection));
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void size(int sizeA, int sizeB, String msg) {
        if (sizeA== sizeB) {
            return;
        }
        log.info("CheckUtil size Collection fail msg:{}, expected size: {}, actual size: {}", msg, sizeA, sizeB);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }


    public static void isEmpty(Collection<?> collection, String msg) {
        if (CollectionUtils.isEmpty(collection)) {
            return;
        }
        log.info("CheckUtil isEmpty Collection fail msg:{}", msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void notEmpty(CharSequence str, String msg) {
        if (StringUtils.isNotEmpty(str)) {
            return;
        }
        log.info("CheckUtil notEmpty String fail msg:{}", msg);
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void equals(Object obj1, Object obj2, String msg) {
        if (Objects.equals(obj1, obj2)) {
            return;
        }
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }


    public static void notBlank(CharSequence str,String msg){
        if(StringUtils.isNotBlank(str)){
            return;
        }
        throw new BizException(VALIDATE_CHECK_ERROR.getCode(), msg);
    }

    public static void notBlank(CharSequence str1,CharSequence str2,String msg){
        notBlank(str1,msg);
        notBlank(str2,msg);
    }
    public static void notBlank(CharSequence str1, CharSequence str2, CharSequence str3, String msg){
        notBlank(str1, msg);
        notBlank(str2,msg);
        notBlank(str3,msg);
    }

}
