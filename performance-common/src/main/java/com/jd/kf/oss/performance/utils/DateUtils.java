package com.jd.kf.oss.performance.utils;

import org.apache.commons.lang3.tuple.Pair;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;


/**
 * 时间计算工具类
 */
public class DateUtils {
    public static final String DATE_FMT_yyyy_MM = "yyyy-MM";

    public static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String zoneId = "Asia/Shanghai";



    /**
     * localDateTime 转 自定义格式string
     *
     * @param format 例：yyyy-MM-dd hh:mm:ss
     */
    public static String formatLocalDateTimeToString(LocalDateTime localDateTime, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return localDateTime.format(formatter);

        } catch (DateTimeParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String formatDate(Date date, String format) {
        return formatLocalDateTimeToString(dateToLocalDateTime(date), format);
    }



    /**
     * string 转 LocalDateTime
     *
     * @param dateStr 例："2017-08-11 01:00:00"
     * @param format  例："yyyy-MM-dd HH:mm:ss"
     */
    public static LocalDateTime stringToLocalDateTime(String dateStr, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取当前时间
     *
     * @return LocalDateTime
     */
    public static LocalDateTime getCurrentLocalDateTime() {
        return LocalDateTime.now(Clock.system(ZoneId.of(zoneId)));

    }

    public static LocalDateTime now() {
        return getCurrentLocalDateTime();
    }

    /**
     * Date 转 LocalDateTime
     *
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
    }


    /**
     * Date 转 LocalDateTime
     * @return LocalDate
     */
    public static LocalDate dateToLocalDate(Date date) {

        return dateToLocalDateTime(date).toLocalDate();
    }


    public static Date parseStringToDate(String source, String pattern) {
        if (source == null) {
            return null;
        }
        pattern = pattern == null ? TIME_PATTERN : pattern;

        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(source);
        } catch (ParseException e) {
            return null;
        }
    }



    /**
     * 获取当前日期对应的绩效月份（以每月21日为分界点）
     * @return 返回格式为"yyyy-MM"的绩效月份字符串
     */
    public static String getCurrentPerformancePeriod() {
        LocalDate now = LocalDate.now();
        // 取本月21日
        LocalDate thisMonth21 = now.withDayOfMonth(21);
        String period;
        if (now.isBefore(thisMonth21)) {
            // 21号之前，属于上一个绩效月
            LocalDate lastMonth = now.minusMonths(1);
            period = lastMonth.plusMonths(1).format(DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM));
        } else {
            // 21号及以后，属于下一个绩效月
            period = now.plusMonths(1).format(DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM));
        }
        return period;
    }

    /**
     * 将字符串解析为LocalDate对象
     * @param date 符合ISO_LOCAL_DATE格式(yyyy-MM-dd)的日期字符串
     * @return 解析成功返回对应的LocalDate对象
     * @throws IllegalArgumentException 当日期格式不符合要求或日期无效时抛出
     */
    public static LocalDate parseStringToLocalDate(String date) {
        try {
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM_dd);
            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
            return LocalDate.parse(date, formatter);
        }catch (DateTimeParseException e) {
            throw new IllegalArgumentException("请使用yyyy-MM-dd日期格式，并确保日期有效");
        }
    }

    public static boolean isBefore(String startDate, String endDate){
        LocalDate start = parseStringToLocalDate(startDate);
        LocalDate end = parseStringToLocalDate(endDate);
        return start.isBefore(end);
    }

    /**
     * 判断给定的开始日期和结束日期是否构成一个完整的自然月
     * @param startDate 开始日期字符串，格式应符合parseStringToLocalDate方法的要求
     * @param endDate 结束日期字符串，格式应符合parseStringToLocalDate方法的要求
     * @return 若结束日期等于开始日期加一个月减一天（即自然月的最后一天）则返回true，否则返回false
     */
    public static boolean isCompleteMonth(String startDate, String endDate){
        LocalDate start = parseStringToLocalDate(startDate);
        LocalDate end = parseStringToLocalDate(endDate);
        LocalDate expectedEnd = start.plusMonths(1).minusDays(1);
        return expectedEnd.isEqual(end);
    }

    /**
     * 判断date1是否早于date2 (LocalDateTime版本), 两个时间相等返回true
     */
    public static boolean isEqualOrBefore(LocalDateTime date1, LocalDateTime date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isBefore(date2) || date1.isEqual(date2);
    }






}