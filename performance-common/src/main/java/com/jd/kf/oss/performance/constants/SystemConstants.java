package com.jd.kf.oss.performance.constants;

/**
 * Description: Constant Demo
 *
 * <AUTHOR>
 */
public interface SystemConstants {
    /**
     * 导入最大行数
     */
    Integer IMPORT_EXCEL_MAX_ROW_COUNT=1000;
    Integer EXPORT_EXCEL_MAX_ROW_COUNT=1000;

    String NULL = "null";

    Integer VALID = 1;
    /**
     * 请求头中租户编码
     */
    String TENANT_CODE = "tenantCode";
    /**
     * 请求头中token
     */
    String TOKEN_NAME = "token";

    String TENANT_CODE_DEFAULT = "kf_perform_retail绩效-零售";

    String EDITOR_SYSTEM = "sys";

    String TENANT_CODE_RETAIL = "kf_perform_retail";
}
