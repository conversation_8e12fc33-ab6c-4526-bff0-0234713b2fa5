package com.jd.kf.oss.performance.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }

    /**
     * 通过给定的类获取对应的Spring单例实例
     *
     * @param clazz 要获取实例的类（通常为接口或具体类类型）
     * @param <T>   对应的泛型类型
     * @return 该类对应的Spring单例实例，如果不存在则返回null
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext has not been initialized yet.");
        }
        try {
            return applicationContext.getBean(clazz);
        } catch (BeansException e) {
            e.printStackTrace();
            return null;
        }
    }
}