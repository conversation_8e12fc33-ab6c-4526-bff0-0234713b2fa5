package com.jd.kf.oss.performance.utils;

import java.io.Serializable;
import java.util.UUID;


public class IDUtils implements Serializable {


    private static final String[] chars = new String[]{"a", "b", "c", "d", "e", "f",
        "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
        "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
        "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
        "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
        "W", "X", "Y", "Z"};

    public static String getUuid() {
        //随机生成一位整数
        int random = (int) (Math.random() * 9 + 1);

        String valueOf = String.valueOf(random);
        //生成uuid的hashCode值
        int hashCode = UUID.randomUUID().toString().hashCode();
        //可能为负数
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
//    String timestamp = String.valueOf(System.currentTimeMillis());
        return valueOf + String.format("%015d", hashCode);
    }
    public static String getShortId() {
        StringBuilder shortBuffer = new StringBuilder();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }
    public static String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    public static String generateUUID(String prefix) {
        return prefix+"_"+UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

    public static String generateFileId() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        return timestamp+UUID.randomUUID().toString().replace("-", "").substring(0,4);
    }



    /**
     * 从业务线ID字符串中提取数字部分
     * @param businessLineId 业务线ID，如"0001"、"0123"等
     * @return 提取的数字，如果格式不正确则返回0
     */
    public static int extractNumberFromBusinessLineId(String businessLineId) {
        if (businessLineId == null || businessLineId.trim().isEmpty()) {
            return 0;
        }
        try {
            // 去除前导零并转换为整数
            return Integer.parseInt(businessLineId.trim());
        } catch (NumberFormatException e) {
            return 0;
        }
    }

}
