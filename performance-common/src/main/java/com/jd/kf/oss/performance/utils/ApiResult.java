package com.jd.kf.oss.performance.utils;

import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 接口返回结果
 *
 * @param <T>
 */
@Data
public class ApiResult<T> implements Serializable {

    private T data;

    private String resultCode;

    private String resultMsg;

    public ApiResult() {
    }

    public ApiResult(T data, String resultCode, String resultMsg) {
        this(resultCode, resultMsg);
        this.data = data;
    }

    public ApiResult(ResultCodeEnum resultCode) {
        this.resultCode = resultCode.getCode();
        this.resultMsg = resultCode.getMessage();
    }

    public ApiResult(String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    public ApiResult(T data, ResultCodeEnum resultCode) {
        this(data, resultCode.getCode(), resultCode.getMessage());
    }

    public static <T> ApiResult<T> success(T data){
        return new ApiResult<T>(data, ResultCodeEnum.SUCCESS);
    }

    public static <T> ApiResult<T> success() {
        return new ApiResult<T>(ResultCodeEnum.SUCCESS);
    }

    public static <T> ApiResult<T> illegal() {
        return new ApiResult<T>(ResultCodeEnum.PARAM_ILLEGAL);
    }

    public static <T> ApiResult<T> illegal(String message) {
        return new ApiResult<T>(ResultCodeEnum.PARAM_ILLEGAL.getCode(), message);
    }

    public static <T> ApiResult<T> error() {
        return error(ResultCodeEnum.ERROR);
    }

    public static <T> ApiResult<T> error(String message) {
        return new ApiResult<T>(ResultCodeEnum.ERROR.getCode(), message);
    }

    public static <T> ApiResult<T> error(ResultCodeEnum codeEnum) {
        return new ApiResult<T>(codeEnum);
    }


}
