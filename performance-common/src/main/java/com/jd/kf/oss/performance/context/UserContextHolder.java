package com.jd.kf.oss.performance.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.enums.DataAuthEnum;
import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import com.jd.kf.oss.performance.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户上下文持有者工具类
 * 基于TransmittableThreadLocal实现用户上下文信息的线程间传递
 * 提供用户上下文的存储、获取、清理等操作，支持异步线程传递
 */
@Slf4j
public class UserContextHolder {

    /**
     * 可传递的线程本地变量，用于存储用户上下文信息
     */
    private static final TransmittableThreadLocal<UserContext> USER_THREAD_LOCAL = new TransmittableThreadLocal<>();

    /**
     * 移除当前线程的用户上下文并设置新的上下文
     *
     * @param context 新的用户上下文对象
     */
    public static void removeAndSet(UserContext context) {
        UserContextHolder.remove();
        UserContextHolder.set(context);
    }

    /**
     * 设置当前线程的用户上下文
     *
     * @param context 用户上下文对象
     */
    public static void set(UserContext context) {
        USER_THREAD_LOCAL.set(context);
    }

    /**
     * 获取当前线程的用户上下文
     *
     * @return 用户上下文对象，可能为null
     */
    public static UserContext get() {
        return USER_THREAD_LOCAL.get();
    }

    /**
     * 清除当前线程的用户上下文
     * 防止内存泄漏
     */
    public static void remove() {
        USER_THREAD_LOCAL.remove();
    }

    /**
     * 获取当前用户的租户编码
     *
     */
    public static String getTenantCode() {
        UserContext userContext = get();
        if (userContext == null) {
            log.error("userContext is null");
            throw new BizException(ResultCodeEnum.CONTEXT_IS_NULL);
        }
        String tenantCode = userContext.getTenantCode();
        if (StringUtils.isBlank(tenantCode)) {
            log.error("tenantCode is blank");
            throw new BizException(ResultCodeEnum.TENANT_CODE_IS_NULL);
        }
        return tenantCode;
    }

    /**
     * 获取当前用户的PIN码
     *
     */
    public static String getPin() {
        UserContext userContext = get();
        if (userContext == null) {
            log.error("userContext is null");
            return null;
        }
        String pin = userContext.getPin();
        if (StringUtils.isBlank(pin)) {
            pin = "default";
        }
        return pin;
    }

    /**
     * 获取当前用户的ERP账号
     *
     * @return ERP账号
     * @throws BizException 当用户上下文为空或ERP账号为空/null字符串时抛出业务异常
     */
    public static String getErp() {
        UserContext userContext = get();
        if (userContext == null) {
            log.error("userContext is null");
            throw new BizException(ResultCodeEnum.CONTEXT_IS_NULL);
        }
        String erp = userContext.getErp();
        if (StringUtils.isBlank(erp) || SystemConstants.NULL.equalsIgnoreCase(erp)) {
            throw new BizException(ResultCodeEnum.ERP_IS_NULL);
        }
        return erp;
    }

    /**
     * 获取当前用户的数据权限
     *
     */
    public static DataAuthEnum getDataAuth(){
        UserContext userContext = get();
        return userContext.getDataAuth();
    }

    /**
     * 封装获取操作者的方法，目前是获取的erp，方便后续如果需要换成其他的，比如pin，可以一键切换
     *
     */
    public static String getOperator() {
        return getErp();
    }

    /**
     * 初始化并设置用户上下文
     * 使用提供的租户编码和ERP账号创建用户上下文对象
     */
    public static void initAndSetUserContext(String tenantCode, String erp) {
        UserContext userContext = UserContext
                .builder()
                .erp(erp)
                .tenantCode(tenantCode)
                .build();

        UserContextHolder.removeAndSet(userContext);
    }
}