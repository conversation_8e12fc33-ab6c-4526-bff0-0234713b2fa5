package com.jd.kf.oss.performance.enums;

import lombok.Getter;

@Getter
public enum FactorSysEnum {
    SUPPORT_QUANTITY("factor_support_quantity", "factor_main_quantity", "支援赛道单量"),

    SUPPORT_CONVERSION_TOTAL("factor_support_conversion_total", "", "支援折算总量"),

    SUPPORT_BUSINESS_CPD("factor_support_business_line_cpd", "factor_mian_business_line_cpd", "支援赛道CPD"),

    SUPPORT_BUSINESS_LINE_QUANTITY("factor_support_conversion_quantity", "", "支援折算量"),

    SUPPORT_MAIN_PRICE("factor_support_main_price", "factor_main_price","支援赛道单价"),

    COMPREHENSIVE_QUALITY_COEFFICIENT("factor_comprehensive_quality_coefficient", "", "综合质量系数"),
    ;

    private final String code;

    private final String relatedCode;

    private final String desc;

    FactorSysEnum(String code, String relatedCode, String desc) {
        this.code = code;
        this.relatedCode = relatedCode;
        this.desc = desc;
    }


    public static FactorSysEnum getByCode(String code) {
        for (FactorSysEnum template : FactorSysEnum.values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }

}
