package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Consumer;

/**
 **
 *  一行一行操作
 */
@Slf4j
public class EasyExcelStringListener<T> extends AnalysisEventListener<Map<Integer, String>> {

    private final int batchCount;

    /**
     * headMap, rowMap -> list<T>
     */
    private final BiFunction<Map<Integer, String>, Map<Integer, String>, List<T>> buildFunction;
    private final Consumer<List<T>> dataListProcessConsumer;
    private final List<Map<Integer, String>> dataList = new ArrayList<>();
    private Map<Integer, String> singleHeadMap = Maps.newHashMap();

    public EasyExcelStringListener(int batchCount, Consumer<List<T>> dataListProcessConsumer,
                                   BiFunction<Map<Integer, String>, Map<Integer, String>, List<T>> buildFunction) {
        this.batchCount = batchCount;
        this.dataListProcessConsumer = dataListProcessConsumer;
        this.buildFunction = buildFunction;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        dataList.add(data);
        if (dataList.size() >= batchCount) {
            dataList.forEach(k -> {
                List<T> list = buildFunction.apply(singleHeadMap, k);
                if (CollectionUtils.isNotEmpty(list)) {
                    dataListProcessConsumer.accept(list);
                }
            });
            dataList.clear();
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        singleHeadMap = headMap;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("all data analysed！");
    }
}
