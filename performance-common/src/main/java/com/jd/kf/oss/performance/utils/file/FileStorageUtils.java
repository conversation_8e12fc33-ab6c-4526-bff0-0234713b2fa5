package com.jd.kf.oss.performance.utils.file;

import com.jd.jss.JingdongStorageService;
import com.jd.jss.domain.StorageObject;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URI;

/**
 * @author:zmf
 * @date:2021/9/17
 */
@Service
@Slf4j
public class FileStorageUtils {

    private static final String BUCKET_NAME = "im.roster";
    private static final int DEFAULT_TIME_OUT = 864000;

    public static String property = System.getProperty("user.dir");
    public static String localDataDir =  "/performance/tempFiles/";

    @Autowired
    private JingdongStorageService ddFileJingdongStorageService;

    /**
     * 需要过滤的特殊字符
     */
    @LafValue("predict.filter.special.characters")
    private String filterSpecialCharacters = "[!@#$%^&*\\\\/]";

    public InputStream getInputStream(String url) {
        BucketFileNameCombine bucketAndFileName = getBucketAndFileName(url);
        StorageObject storageObject = ddFileJingdongStorageService
                .bucket(bucketAndFileName.getBucket())
                .object(bucketAndFileName.getFileName())
                .get();
        return storageObject.getInputStream();
    }


    public File getFile(String url) {
        try {
            BucketFileNameCombine bucketAndFileName = getBucketAndFileName(url);
            StorageObject storageObject = ddFileJingdongStorageService
                    .bucket(bucketAndFileName.getBucket())
                    .object(bucketAndFileName.getFileName())
                    .get();
            FileUtil.checkAndMakeDir(FileUtil.getLocalDataDir(property + localDataDir));
            String filePath = property + localDataDir +bucketAndFileName.getFileName();
            File file = new File(filePath);
            storageObject.toFile(file);
            return  file;
        }catch (Exception e){
          log.error("RosterFileStorageServiceImpl.getFile error ={}",e.getMessage());
        }
        return null;
    }

    public String upload(String fileName, ByteArrayOutputStream outputStream) {
        checkAndCreateBucket();
        ddFileJingdongStorageService.bucket(BUCKET_NAME).object(fileName.replaceAll(filterSpecialCharacters,""))
                .entity(outputStream.size(), new ByteArrayInputStream(outputStream.toByteArray()))
                .put();
        URI uri = ddFileJingdongStorageService.bucket(BUCKET_NAME)
                .object(fileName.replaceAll(filterSpecialCharacters,""))
                .generatePresignedUrl(DEFAULT_TIME_OUT);
        return uri.toString();
    }

    private void checkAndCreateBucket() {
        if (ddFileJingdongStorageService.hasBucket(BUCKET_NAME)) {
            return;
        }
        ddFileJingdongStorageService.createBucket(BUCKET_NAME);
    }

    private BucketFileNameCombine getBucketAndFileName(String url) {
        int questionMarPos = url.indexOf("?");
        int slashPos = url.lastIndexOf("/", questionMarPos);
        String fileName = url.substring(slashPos + 1, questionMarPos);
        int slashPos2 = url.lastIndexOf("/", slashPos - 1);
        return BucketFileNameCombine.builder()
                .bucket(url.substring(slashPos2 + 1, slashPos))
                .fileName(fileName.replaceAll(filterSpecialCharacters,""))
                .build();
    }


    @Builder
    @Getter
    private static class BucketFileNameCombine {

        private final String bucket;

        private final String fileName;
    }



}
