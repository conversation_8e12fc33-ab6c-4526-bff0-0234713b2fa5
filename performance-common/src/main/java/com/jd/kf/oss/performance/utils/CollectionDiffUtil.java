package com.jd.kf.oss.performance.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用集合差异比较工具类
 */
public class CollectionDiffUtil {

    public static final String DIFF_ADD = "ADD";
    public static final String DIFF_REMOVE = "REMOVE";
    public static final String DIFF_UPDATE = "UPDATE";
    /**
     * 比较两个集合的差异，返回新增、删除和需要更新的元素集合
     *
     * @param oldList      旧集合
     * @param newList      新集合
     * @param uniqueFields 用于判断元素唯一性的字段名数组
     * @param <T>          集合元素的类型
     * @return 包含新增、删除和需要更新元素集合的 Map
     */
    public static <T> Map<String, List<T>> diffCollections(List<T> oldList, List<T> newList, String[] uniqueFields, String[] checkFields) {
        Map<String, List<T>> result = new HashMap<>();
        List<T> added = new ArrayList<>();
        List<T> removed = new ArrayList<>();
        List<T> updated = new ArrayList<>();

        // 为每个旧元素生成唯一标识
        Map<String, T> oldMap = new HashMap<>();
        for (T oldItem : oldList) {
            String key = generateKey(oldItem, uniqueFields);
            oldMap.put(key, oldItem);
        }

        // 为每个新元素生成唯一标识
        Map<String, T> newMap = new HashMap<>();
        for (T newItem : newList) {
            String key = generateKey(newItem, uniqueFields);
            newMap.put(key, newItem);
        }

        // 新增元素
        for (T newItem : newList) {
            String key = generateKey(newItem, uniqueFields);
            if (!oldMap.containsKey(key)) {
                added.add(newItem);
            }
        }

        // 删除元素
        for (T oldItem : oldList) {
            String key = generateKey(oldItem, uniqueFields);
            if (!newMap.containsKey(key)) {
                removed.add(oldItem);
            }
        }

        // 变更元素
        for (T newItem : newList) {
            String key = generateKey(newItem, uniqueFields);
            if(oldMap.containsKey(key) && newMap.containsKey(key)){
                T oldItem = oldMap.get(key);
                String oldData = generateKey(oldItem,checkFields);
                String newData = generateKey(newItem,checkFields);
                if(!oldData.equals(newData)){
                    updated.add(newItem);
                }
            }
        }
        result.put(DIFF_ADD, added);
        result.put(DIFF_REMOVE, removed);
        result.put(DIFF_UPDATE, updated);
        return result;
    }

    /**
     * 生成元素的唯一标识
     *
     * @param item         元素对象
     * @param uniqueFields 用于判断元素唯一性的字段名数组
     * @param <T>          元素的类型
     * @return 元素的唯一标识
     */
    private static <T> String generateKey(T item, String[] uniqueFields) {
        StringBuilder keyBuilder = new StringBuilder();
        for (String fieldName : uniqueFields) {
            try {
                Field field = getFieldFromClassHierarchy(item.getClass(), fieldName);
                if (field != null) {
                    field.setAccessible(true);
                    Object value = field.get(item);
                    keyBuilder.append(value != null ? value : "NULL").append("_");
                } else {
                    System.out.println("Field not found: " + fieldName + " in class hierarchy of " + item.getClass().getName());
                    keyBuilder.append("MISSING_");
                }
            } catch (IllegalAccessException e) {
                System.out.println("Access denied for field: " + fieldName + " in class " + item.getClass().getName());
                keyBuilder.append("INACCESSIBLE_");
            }
        }
        return keyBuilder.toString();
    }

    /**
     * 从类的继承层次结构中查找字段
     *
     * @param clazz     目标类
     * @param fieldName 字段名
     * @return 找到的字段，如果未找到则返回null
     */
    private static Field getFieldFromClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 在当前类中未找到，继续查找父类
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }


}
