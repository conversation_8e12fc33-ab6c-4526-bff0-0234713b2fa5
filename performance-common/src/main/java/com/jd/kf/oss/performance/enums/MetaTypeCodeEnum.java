package com.jd.kf.oss.performance.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.jd.laf.binding.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum  MetaTypeCodeEnum {

    /**
     * 公式
     */
    INDEX("index","index_","指标"),
    COEFFICIENT("coefficient","coefficient_","系数"),
    FACTOR("factor","factor_","因子"),
    PLAN("plan","plan_","绩效方案");
    @EnumValue
    private final String code;
    @JsonValue
    private final String name;
    private final String prefix;

    MetaTypeCodeEnum(String code, String prefix, String name) {
        this.code = code;
        this.prefix = prefix;
        this.name = name;
    }
}
