package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * @author:zmf
 * @date:2021/827 需要对象属性标注实现@ExcelProperty注解
 */
@Slf4j
@Getter
public class EasyExcelReWriteListener<T> extends AnalysisEventListener<Map<Integer, String>> {

    private MultiParamFunction<Map<Integer, String>, Map<Integer, String>> consumerFunction;

    private int batchCount;

    private Map<Integer, String> headMap;

    public EasyExcelReWriteListener(int batchCount, MultiParamFunction<Map<Integer, String>, Map<Integer, String>> consumeFunction) {
        this.consumerFunction = consumeFunction;
        this.batchCount = batchCount;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        if (MapUtils.isEmpty(data) || allValueIsEmpty(data.values())) {
            return;
        }
        consumerFunction.exec(headMap, data);
    }

    private boolean allValueIsEmpty(Collection<String> values) {
        return values.stream().allMatch(StringUtils::isEmpty);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.headMap = headMap;
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }

}



