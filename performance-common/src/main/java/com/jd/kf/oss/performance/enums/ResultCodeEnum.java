package com.jd.kf.oss.performance.enums;

import lombok.Getter;

/**
 * Description: Enum Demo
 *
 * <AUTHOR>
 */
@Getter
public enum ResultCodeEnum {
    /**
     * 0: 成功
     */
    SUCCESS("0", "操作成功！"),
    ERROR("1","系统异常！"),
    PARAM_ILLEGAL("2", "请求参数不合法！"),
    BIZ_EXCEPTION("3", "请求业务异常！"),
    OSS_ERROR("4", "文件操作错误"),
    VALIDATE_CHECK_ERROR("5", "参数校验异常"),

    CONTEXT_IS_NULL("10", "用户上下文为空"),
    ERP_IS_NULL("11", "用户ERP为空"),
    TENANT_CODE_IS_NULL("12", "租户标识为空"),
    TENANT_ID_IS_NULL("13", "租户id为空"),
    SUPER_TENANT_CODE_IS_NULL("14", "一级租户标识为空"),

    DUPLICATE_BUSINESS_LINE_NAME("15", "绩效组名称重复")
    ;


    private final String code;

    /**
     * 结果code对应的message
     */

    private final String message;


    ResultCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }

}

