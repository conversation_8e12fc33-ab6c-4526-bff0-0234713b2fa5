package com.jd.kf.oss.performance.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 部门路径工具类
 * 
 * <AUTHOR>
 * @date 2025/07/17
 */
public class DeptPathUtils {

    /**
     * 解析deptPath字段，提取所有层级的部门ID
     * deptPath格式：1@5@19843@19844@21753@21754@
     * 
     * @param deptPath 部门路径字符串
     * @return 部门ID列表，按层级顺序排列
     */
    public static List<String> parseDeptPath(String deptPath) {
        if (StringUtils.isBlank(deptPath)) {
            return Collections.emptyList();
        }

        // 去掉首尾的@符号，然后按@分割
        String cleanPath = deptPath.replaceAll("^@+|@+$", "");
        if (StringUtils.isBlank(cleanPath)) {
            return Collections.emptyList();
        }

        String[] deptIds = cleanPath.split("@");
        List<String> result = new ArrayList<>();
        for (String id : deptIds) {
            if (StringUtils.isNotBlank(id.trim())) {
                result.add(id.trim());
            }
        }
        return result;
    }

    /**
     * 将部门名称列表按层级用分隔符连接
     * 
     * @param deptNames 部门名称列表
     * @param separator 分隔符，默认为"-"
     * @return 连接后的部门路径名称
     */
    public static String joinDeptNames(List<String> deptNames, String separator) {
        if (deptNames == null || deptNames.isEmpty()) {
            return "";
        }
        
        String sep = StringUtils.isBlank(separator) ? "-" : separator;
        return String.join(sep, deptNames);
    }

    /**
     * 将部门名称列表按层级用"-"连接
     * 
     * @param deptNames 部门名称列表
     * @return 连接后的部门路径名称
     */
    public static String joinDeptNames(List<String> deptNames) {
        return joinDeptNames(deptNames, "-");
    }

    /**
     * 验证deptPath格式是否正确
     * 
     * @param deptPath 部门路径字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidDeptPath(String deptPath) {
        if (StringUtils.isBlank(deptPath)) {
            return false;
        }
        
        // 检查是否包含@符号
        if (!deptPath.contains("@")) {
            return false;
        }
        
        // 解析后检查是否有有效的部门ID
        List<String> deptIds = parseDeptPath(deptPath);
        return !deptIds.isEmpty();
    }
}
