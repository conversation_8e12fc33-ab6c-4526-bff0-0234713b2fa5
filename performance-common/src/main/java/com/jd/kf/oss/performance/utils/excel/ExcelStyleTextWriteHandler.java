package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;

/**
 * <AUTHOR>
 * @date 2025-04-21 19:42
 * description
 */
public class ExcelStyleTextWriteHandler implements CellWriteHandler {
    /**
     * 强制Excel原样展示数字
     * 导出数据包含长数字（如身份证号、银行卡号）时，Excel默认转为科学计数法
     * @param context
     */
    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        // 3.0 设置单元格为文本
        WriteCellData<?> cellData = context.getFirstCellData();
        WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
        DataFormatData dataFormatData = new DataFormatData();
        //49表示文本格式
        dataFormatData.setIndex((short) 49);
        writeCellStyle.setDataFormatData(dataFormatData);
    }
}
