package com.jd.kf.oss.performance.utils.excel;

import java.util.List;

/**
 * 新增内部类用于封装每个sheet的数据
 */
public class ExcelSheetData {
    private String sheetName;
    private List<List<String>> head;
    private List<List<String>> dataList;

    // 构造方法
    public ExcelSheetData(String sheetName, List<List<String>> head, List<List<String>> dataList) {
        this.sheetName = sheetName;
        this.head = head;
        this.dataList = dataList;
    }

    // Getter方法
    public String getSheetName() {
        return sheetName;
    }

    public List<List<String>> getHead() {
        return head;
    }

    public List<List<String>> getDataList() {
        return dataList;
    }
}
