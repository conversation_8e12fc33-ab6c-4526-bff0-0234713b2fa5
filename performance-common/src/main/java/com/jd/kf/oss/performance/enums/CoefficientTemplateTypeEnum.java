package com.jd.kf.oss.performance.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum CoefficientTemplateTypeEnum  {

    SEGMENT("分段系数"),
    MONTHLY("月周期系数"),
    CONSTANT("常量系数");

    @EnumValue
    @JsonValue
    private final String type;
    /**
     * 根据类型名称获取枚举实例
     * @param type 类型名称
     * @return 对应的枚举实例，找不到时返回null
     */


    public static CoefficientTemplateTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (CoefficientTemplateTypeEnum value : values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }
        return null;
    }


}


