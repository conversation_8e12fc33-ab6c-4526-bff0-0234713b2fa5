package com.jd.kf.oss.performance.enums;


import lombok.Getter;

/**
 * 用于指标与因子中定义的指标进行映射
 */
@Getter
public enum IndexConstant {

    QUALITY_WEIGHT_1("质量指标权重1", "index_quality_weight_1", "index_quality_1", IndexTypeEnum.QUALITY.getDesc()),
    QUALITY_WEIGHT_2("质量指标权重2", "index_quality_weight_2", "index_quality_2", IndexTypeEnum.QUALITY.getDesc()),
    QUALITY_WEIGHT_3("质量指标权重3", "index_quality_weight_3", "index_quality_3",IndexTypeEnum.QUALITY.getDesc()),
    QUALITY_WEIGHT_4("质量指标权重4", "index_quality_weight_4", "index_quality_3", IndexTypeEnum.QUALITY.getDesc()),
    CAPACITY_WEIGHT_1("产能指标权重1", "index_capacity_weight_1","index_capacity_1", IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY_WEIGHT_2("产能指标权重2", "index_capacity_weight_2", "index_capacity_2", IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY_WEIGHT_3("产能指标权重3", "index_capacity_weight_3", "index_capacity_3", IndexTypeEnum.CAPACITY.getDesc()),
    CAPACITY_WEIGHT_4("产能指标权重4", "index_capacity_weight_4", "index_capacity_4", IndexTypeEnum.CAPACITY.getDesc()),
    BUSINESS_LINE_CPD("赛道CPD", "index_business_line_cpd", "", IndexTypeEnum.CONSTANT.getDesc()),
    BUSINESS_LINE_PRICE("赛道单价", "index_business_line_price", "", IndexTypeEnum.CONSTANT.getDesc()),
    BUSINESS_LINE_STANDARD_DAYS("月标天数", "index_business_line_standard_days", "", IndexTypeEnum.CONSTANT.getDesc()),
    NORMAL_WITHDRAWAL_DURATION("普通抽调时长", "index_normal_withdrawal_duration", "", IndexTypeEnum.CAPACITY.getDesc()),
    INSPIRE_WITHDRAWAL_DURATION("激励抽调时长", "index_inspire_withdrawal_duration", "", IndexTypeEnum.CAPACITY.getDesc()),
    NEW_EMPLOYEE_ACCELERATION_COEFFICIENT("新人加速系数", "index_new_employee_acceleration_coefficient", "", IndexTypeEnum.CAPACITY.getDesc()),
    ;

    private final String templateName;

    private final String code;

    private final String linkTo;

    private final String type;

    /**
     * 构造函数
     * @param templateName 模板名称
     * @param code 模板代码
     * @param linkTo 模板代码
     * @param type 指标类型
     */
    IndexConstant(String templateName, String code, String linkTo, String type) {
        this.templateName = templateName;
        this.code = code;
        this.linkTo = linkTo;
        this.type = type;
    }

    /**
     * 根据指标模板名称获取指标模板
     */
    public static IndexConstant getByTemplateName(String templateName) {
        for (IndexConstant template : IndexConstant.values()) {
            if (template.getTemplateName().equals(templateName)) {
                return template;
            }
        }
        return null;
    }



    public static IndexConstant getByCapacityIndex(String code) {
        String templateName = IndexTypeEnum.CAPACITY.getDesc()+code;
         return getByTemplateName(templateName);
    }

    public static IndexConstant getByQualityIndex(String code) {
        String templateName = IndexTypeEnum.QUALITY.getDesc() +code;
        return getByTemplateName(templateName);
    }


    public static IndexConstant getByCode(String code) {
        for (IndexConstant template : IndexConstant.values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }

    public  boolean isQualityIndex() {
        return IndexTypeEnum.QUALITY.getDesc().equals(type);
    }


    public IndexConstant getIndexTemplate(String code) {
        for (IndexConstant template : IndexConstant.values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }

}
