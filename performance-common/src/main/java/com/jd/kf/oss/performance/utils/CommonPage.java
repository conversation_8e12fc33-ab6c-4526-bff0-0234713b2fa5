package com.jd.kf.oss.performance.utils;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class CommonPage<T> {

    private Long page;

    private Long total;

    private Long size;

    private List<T> data;

    public static <T> CommonPage<T> getCommonPage(Long page, Long pageSize, Long total, List<T> data) {
        CommonPage<T> CommonPage = new CommonPage<T>();
        CommonPage.setPage(page);
        CommonPage.setSize(pageSize);
        CommonPage.setTotal(total);
        CommonPage.setData(data);
        return CommonPage;
    }

    public static <T> CommonPage<T> emptyPage() {
        return emptyPage(0L, 0L);
    }

    public static <T> CommonPage<T> emptyPage(Long page, Long size) {
        return getCommonPage(page, size, 0L, new ArrayList<T>());
    }

}
