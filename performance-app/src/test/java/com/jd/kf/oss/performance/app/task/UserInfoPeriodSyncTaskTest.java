package com.jd.kf.oss.performance.app.task;

import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.schedule.model.JobResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息绩效月同步任务测试
 */
@ExtendWith(MockitoExtension.class)
class UserInfoPeriodSyncTaskTest {

    @Mock
    private UserDomainService userDomainService;

    @InjectMocks
    private UserInfoPeriodSyncTask userInfoPeriodSyncTask;

    @Test
    void testExecute_Success() {
        // 准备测试数据
        String currentPeriod = "2025-07";
        
        // Mock当前用户数据（空列表，表示需要同步）
        when(userDomainService.queryAllUserByPeriod(anyString())).thenReturn(Collections.emptyList());
        
        // Mock WFC用户数据
        UserDO wfcUser = createTestWfcUser();
        when(userDomainService.queryAllWfcUserData()).thenReturn(Arrays.asList(wfcUser));
        
        // Mock保存操作
        when(userDomainService.saveBatchUsers(anyList())).thenReturn(1);

        // 执行测试
        JobResult result = userInfoPeriodSyncTask.execute(null);

        // 验证结果
        assertEquals(JobResult.SUCCESS, result);
        
        // 验证方法调用
        verify(userDomainService, times(1)).queryAllUserByPeriod(anyString());
        verify(userDomainService, times(1)).queryAllWfcUserData();
        verify(userDomainService, times(1)).saveBatchUsers(anyList());
    }

    @Test
    void testExecute_NoWfcData() {
        // Mock空的WFC数据
        when(userDomainService.queryAllWfcUserData()).thenReturn(Collections.emptyList());

        // 执行测试
        JobResult result = userInfoPeriodSyncTask.execute(null);

        // 验证结果
        assertEquals(JobResult.SUCCESS, result);
        
        // 验证不会调用保存方法
        verify(userDomainService, never()).saveBatchUsers(anyList());
    }

    @Test
    void testExecute_NoMissingUsers() {
        // 准备测试数据
        UserDO existingUser = createTestWfcUser();
        UserDO wfcUser = createTestWfcUser();
        
        // Mock当前用户数据（已存在）
        when(userDomainService.queryAllUserByPeriod(anyString())).thenReturn(Arrays.asList(existingUser));
        
        // Mock WFC用户数据（相同用户）
        when(userDomainService.queryAllWfcUserData()).thenReturn(Arrays.asList(wfcUser));

        // 执行测试
        JobResult result = userInfoPeriodSyncTask.execute(null);

        // 验证结果
        assertEquals(JobResult.SUCCESS, result);
        
        // 验证不会调用保存方法
        verify(userDomainService, never()).saveBatchUsers(anyList());
    }

    private UserDO createTestWfcUser() {
        UserDO user = new UserDO();
        user.setTenantCode("test-tenant");
        user.setErp("test.erp");
        user.setName("测试用户");
        user.setDeptId("dept001");
        user.setDeptName("测试部门");
        user.setEntryDate("2025-01-01");
        user.setTs(LocalDateTime.now());
        return user;
    }
}
