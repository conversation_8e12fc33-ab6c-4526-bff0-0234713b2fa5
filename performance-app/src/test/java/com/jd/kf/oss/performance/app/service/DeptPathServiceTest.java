package com.jd.kf.oss.performance.app.service;

import com.jd.kf.oss.performance.domain.config.aggregate.configuration.UserAggregateService;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.UserComposite;
import com.jd.kf.oss.performance.domain.config.domain.dept.DeptDomainService;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DeptPathUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 部门路径服务测试类（重构后）
 * 测试UserAggregateService和DeptDomainService的功能
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@SpringBootTest
public class DeptPathServiceTest {

    @Resource
    private IWfcDeptPOService wfcDeptPOService;

    @Resource
    private DeptDomainService deptDomainService;

    @Resource
    private UserDomainService userDomainService;

    @Resource
    private UserAggregateService userAggregateService;

    /**
     * 测试部门路径解析工具
     */
    @Test
    public void testDeptPathUtils() {
        // 测试正常的deptPath
        String deptPath1 = "1@5@19843@19844@21753@21754@";
        List<String> deptIds1 = DeptPathUtils.parseDeptPath(deptPath1);
        System.out.println("deptPath1: " + deptPath1);
        System.out.println("解析结果: " + deptIds1);
        
        // 测试没有首尾@的deptPath
        String deptPath2 = "1@5@19843@19844@21753@21754";
        List<String> deptIds2 = DeptPathUtils.parseDeptPath(deptPath2);
        System.out.println("deptPath2: " + deptPath2);
        System.out.println("解析结果: " + deptIds2);
        
        // 测试部门名称连接
        List<String> deptNames = Arrays.asList("一级部门", "二级部门", "三级部门", "四级部门");
        String joinedNames = DeptPathUtils.joinDeptNames(deptNames);
        System.out.println("部门名称列表: " + deptNames);
        System.out.println("连接结果: " + joinedNames);
        
        // 测试格式验证
        System.out.println("deptPath1格式验证: " + DeptPathUtils.isValidDeptPath(deptPath1));
        System.out.println("空字符串格式验证: " + DeptPathUtils.isValidDeptPath(""));
        System.out.println("无@符号格式验证: " + DeptPathUtils.isValidDeptPath("123456"));
    }

    /**
     * 测试根据部门ID获取部门路径名称
     * 注意：这个测试需要数据库中有实际的部门数据
     */
    @Test
    public void testGetDeptPathNameByDeptId() {
        String tenantCode = "test_tenant"; // 替换为实际的租户代码
        String deptId = "21754"; // 替换为实际的部门ID
        
        try {
            String deptPathName = wfcDeptPOService.getDeptPathNameByDeptId(tenantCode, deptId);
            System.out.println("租户: " + tenantCode);
            System.out.println("部门ID: " + deptId);
            System.out.println("部门路径名称: " + deptPathName);
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试DeptDomainService根据部门ID获取部门路径名称
     * 注意：这个测试需要数据库中有实际的部门数据
     */
    @Test
    public void testDeptDomainServiceGetDeptPathName() {
        String tenantCode = "test_tenant"; // 替换为实际的租户代码
        String deptId = "21754"; // 替换为实际的部门ID

        try {
            String deptPathName = deptDomainService.getDeptPathNameByDeptId(tenantCode, deptId);
            System.out.println("部门ID: " + deptId);
            System.out.println("部门路径名称: " + deptPathName);
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试DeptDomainService批量获取部门路径名称
     */
    @Test
    public void testDeptDomainServiceGetDeptPathNamesByDeptIds() {
        String tenantCode = "test_tenant"; // 替换为实际的租户代码
        List<String> deptIds = Arrays.asList("21754", "21753", "19844"); // 替换为实际的部门ID

        try {
            Map<String, String> deptPathMap = deptDomainService.getDeptPathNamesByDeptIds(tenantCode, deptIds);
            System.out.println("批量查询部门路径结果:");
            for (Map.Entry<String, String> entry : deptPathMap.entrySet()) {
                System.out.println("部门ID: " + entry.getKey() + " -> 部门路径: " + entry.getValue());
            }
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试UserAggregateService分页查询用户信息（包含部门路径）
     */
    @Test
    public void testUserAggregateServiceQueryUserPageInfo() {
        String tenantCode = "test_tenant"; // 替换为实际的租户代码
        String period = "2025-07"; // 替换为实际的绩效月

        try {
            CommonPage<UserComposite> result = userAggregateService.queryUserPageInfo(
                    tenantCode, period, null, null, null,
                    null, null, null, 1, 5);

            System.out.println("查询结果:");
            System.out.println("总数: " + result.getTotal());
            System.out.println("当前页: " + result.getCurrent());
            System.out.println("页面大小: " + result.getSize());

            if (result.getData() != null && !result.getData().isEmpty()) {
                System.out.println("用户信息:");
                for (UserComposite userComposite : result.getData()) {
                    UserDO user = userComposite.getUserDO();
                    String deptPathName = userComposite.getDeptDO() != null ?
                            userComposite.getDeptDO().getDeptPathName() : "";
                    System.out.println(String.format("用户: %s, 部门: %s, 部门路径: %s",
                            user.getName(), user.getDeptName(), deptPathName));
                }
            } else {
                System.out.println("未查询到用户数据");
            }
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试UserDomainService和UserAggregateService的区别
     */
    @Test
    public void testCompareUserDomainServiceAndUserAggregateService() {
        String tenantCode = "test_tenant"; // 替换为实际的租户代码
        String period = "2025-07"; // 替换为实际的绩效月

        try {
            // 使用UserDomainService查询（不包含部门路径）
            System.out.println("=== UserDomainService查询结果（不包含部门路径）===");
            CommonPage<UserDO> userDomainResult = userDomainService.queryUserInfoByConditions(
                    tenantCode, period, null, null, null,
                    null, null, null, 1, 3);

            if (userDomainResult.getData() != null && !userDomainResult.getData().isEmpty()) {
                for (UserDO user : userDomainResult.getData()) {
                    System.out.println(String.format("用户: %s, 部门: %s, 部门ID: %s",
                            user.getName(), user.getDeptName(), user.getDeptId()));
                }
            }

            // 使用UserAggregateService查询（包含部门路径）
            System.out.println("\n=== UserAggregateService查询结果（包含部门路径）===");
            CommonPage<UserComposite> aggregateResult = userAggregateService.queryUserPageInfo(
                    tenantCode, period, null, null, null,
                    null, null, null, 1, 3);

            if (aggregateResult.getData() != null && !aggregateResult.getData().isEmpty()) {
                for (UserComposite userComposite : aggregateResult.getData()) {
                    UserDO user = userComposite.getUserDO();
                    String deptPathName = userComposite.getDeptDO() != null ?
                            userComposite.getDeptDO().getDeptPathName() : "";
                    System.out.println(String.format("用户: %s, 部门: %s, 部门路径: %s",
                            user.getName(), user.getDeptName(), deptPathName));
                }
            }
        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
