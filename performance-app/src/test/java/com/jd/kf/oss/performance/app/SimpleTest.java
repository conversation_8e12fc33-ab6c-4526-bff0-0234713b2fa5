package com.jd.kf.oss.performance.app;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单测试类，验证基本功能
 */
class SimpleTest {

    @Test
    void testBasicFunctionality() {
        // 测试基本的字符串操作
        String testString = "Hello World";
        assertNotNull(testString);
        assertEquals("Hello World", testString);
        assertTrue(testString.contains("World"));
    }

    @Test
    void testMathOperations() {
        // 测试基本的数学运算
        int result = 2 + 3;
        assertEquals(5, result);
        
        double division = 10.0 / 3.0;
        assertTrue(division > 3.0);
        assertTrue(division < 4.0);
    }
}
