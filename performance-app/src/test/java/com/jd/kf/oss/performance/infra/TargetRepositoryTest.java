package com.jd.kf.oss.performance.infra;

import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.infra.configuration.performancetarget.TargetRepository;
import com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTargetPOMapper;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2025/07/11
 * 绩效目标仓储测试 - 验证分页查询优化
 */
@ExtendWith(MockitoExtension.class)
class TargetRepositoryTest {

    @Mock
    private PerformanceTargetPOMapper performanceTargetPOMapper;

    @InjectMocks
    private TargetRepository targetRepository;

    @Test
    void testQueryTargetByConditions_Success() {
        // 准备测试数据
        String tenantCode = "test-tenant";
        String period = "2025-07";
        String businessLineId = "bl001";
        String businessLineName = "测试业务线";
        String planCode = "plan001";
        int pageNum = 1;
        int pageSize = 10;

        // Mock count查询返回总数
        when(performanceTargetPOMapper.countTargetWithJoin(
                tenantCode, period, businessLineId, planCode, businessLineName))
                .thenReturn(25L);

        // Mock分页查询返回数据
        PerformanceTargetDO targetDO = createTestTargetDO();
        when(performanceTargetPOMapper.queryTargetWithJoin(
                tenantCode, period, businessLineId, planCode, businessLineName, 0, 10))
                .thenReturn(Arrays.asList(targetDO));

        // 执行测试
        CommonPage<PerformanceTargetDO> result = targetRepository.queryTargetByConditions(
                tenantCode, period, businessLineId, businessLineName, planCode, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(25L, result.getTotal());
        assertEquals(1L, result.getPage());
        assertEquals(10L, result.getSize());
        assertEquals(1, result.getData().size());
        assertEquals("target001", result.getData().get(0).getBusinessLineId());

        // 验证方法调用
        verify(performanceTargetPOMapper, times(1)).countTargetWithJoin(
                tenantCode, period, businessLineId, planCode, businessLineName);
        verify(performanceTargetPOMapper, times(1)).queryTargetWithJoin(
                tenantCode, period, businessLineId, planCode, businessLineName, 0, 10);
    }

    @Test
    void testQueryTargetByConditions_EmptyResult() {
        // 准备测试数据
        String tenantCode = "test-tenant";
        String period = "2025-07";
        int pageNum = 1;
        int pageSize = 10;

        // Mock count查询返回0
        when(performanceTargetPOMapper.countTargetWithJoin(
                tenantCode, period, null, null, null))
                .thenReturn(0L);

        // 执行测试
        CommonPage<PerformanceTargetDO> result = targetRepository.queryTargetByConditions(
                tenantCode, period, null, null, null, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(0L, result.getTotal());
        assertEquals(1L, result.getPage());
        assertEquals(10L, result.getSize());
        assertTrue(result.getData().isEmpty());

        // 验证只调用了count方法，没有调用分页查询方法
        verify(performanceTargetPOMapper, times(1)).countTargetWithJoin(
                tenantCode, period, null, null, null);
        verify(performanceTargetPOMapper, never()).queryTargetWithJoin(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt());
    }

    @Test
    void testQueryTargetByConditions_SecondPage() {
        // 准备测试数据
        String tenantCode = "test-tenant";
        String period = "2025-07";
        int pageNum = 2;
        int pageSize = 10;

        // Mock count查询返回总数
        when(performanceTargetPOMapper.countTargetWithJoin(
                tenantCode, period, null, null, null))
                .thenReturn(25L);

        // Mock分页查询返回数据（第二页，offset=10）
        PerformanceTargetDO targetDO = createTestTargetDO();
        when(performanceTargetPOMapper.queryTargetWithJoin(
                tenantCode, period, null, null, null, 10, 10))
                .thenReturn(Arrays.asList(targetDO));

        // 执行测试
        CommonPage<PerformanceTargetDO> result = targetRepository.queryTargetByConditions(
                tenantCode, period, null, null, null, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(25L, result.getTotal());
        assertEquals(2L, result.getPage());
        assertEquals(10L, result.getSize());
        assertEquals(1, result.getData().size());

        // 验证方法调用，offset应该是10
        verify(performanceTargetPOMapper, times(1)).queryTargetWithJoin(
                tenantCode, period, null, null, null, 10, 10);
    }

    @Test
    void testQueryTargetByConditions_InvalidParams() {
        // 测试无效参数
        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByConditions("tenant", "period", null, null, null, 0, 10);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByConditions("tenant", "period", null, null, null, 1, 0);
        });

        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByConditions("tenant", "period", null, null, null, 1, 10);
        });
    }

    @Test
    void testQueryTargetByBusinessLineId_Success() {
        // 准备测试数据
        String businessLineId = "bl001";
        String tenantCode = "test-tenant";
        String period = "2025-07";

        // Mock联表查询返回完整数据
        PerformanceTargetDO targetDO = createTestTargetDO();
        targetDO.setBusinessLineName("测试业务线");
        targetDO.setEvaluationPlan("测试绩效方案");
        targetDO.setEvaluationType("MONTHLY");

        when(performanceTargetPOMapper.queryTargetDetailByBusinessLineId(
                businessLineId, tenantCode, period))
                .thenReturn(targetDO);

        // 执行测试
        PerformanceTargetDO result = targetRepository.queryTargetByBusinessLineId(
                businessLineId, tenantCode, period);

        // 验证结果
        assertNotNull(result);
        assertEquals("target001", result.getBusinessLineId());
        assertEquals("测试业务线", result.getBusinessLineName());
        assertEquals("测试绩效方案", result.getEvaluationPlan());
        assertEquals("MONTHLY", result.getEvaluationType());

        // 验证方法调用
        verify(performanceTargetPOMapper, times(1)).queryTargetDetailByBusinessLineId(
                businessLineId, tenantCode, period);
    }

    @Test
    void testQueryTargetByBusinessLineId_NotFound() {
        // 准备测试数据
        String businessLineId = "nonexistent";
        String tenantCode = "test-tenant";
        String period = "2025-07";

        // Mock查询返回null
        when(performanceTargetPOMapper.queryTargetDetailByBusinessLineId(
                businessLineId, tenantCode, period))
                .thenReturn(null);

        // 执行测试
        PerformanceTargetDO result = targetRepository.queryTargetByBusinessLineId(
                businessLineId, tenantCode, period);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(performanceTargetPOMapper, times(1)).queryTargetDetailByBusinessLineId(
                businessLineId, tenantCode, period);
    }

    @Test
    void testQueryTargetByBusinessLineId_InvalidParams() {
        // 测试无效参数
        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByBusinessLineId(null, "tenant", "period");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByBusinessLineId("bl001", "", "period");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            targetRepository.queryTargetByBusinessLineId("bl001", "tenant", null);
        });
    }

    private PerformanceTargetDO createTestTargetDO() {
        PerformanceTargetDO targetDO = new PerformanceTargetDO();
        targetDO.setId(1L);
        targetDO.setTenantCode("test-tenant");
        targetDO.setBusinessLineId("target001");
        targetDO.setBusinessLineName("测试业务线");
        targetDO.setPeriod("2025-07");
        targetDO.setPrice("100");
        targetDO.setCpd("80");
        targetDO.setEvaluationPlan("测试绩效方案");
        targetDO.setEvaluationType("MONTHLY");
        return targetDO;
    }
}
