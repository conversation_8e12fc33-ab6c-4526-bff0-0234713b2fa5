package com.jd.kf.oss.performance.app.configuration.controller.controller;

import com.jd.kf.oss.performance.app.controller.dto.request.appeal.PageAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveImportVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 无效数据剔除控制器测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
class AppealRemoveControllerTest {

    private List<AppealRemoveImportVO> mockImportData;
    private List<AppealRemoveExportVO> mockExportData;
    private PageAppealRemoveRequest pageRequest;

    @BeforeEach
    void setUp() {
        // 初始化mock数据
        mockImportData = createMockImportData();
        mockExportData = createMockExportData();
        
        // 初始化分页请求
        pageRequest = createPageRequest();
    }

    @Test
    void testImportVOStructure() {
        // 验证导入VO结构符合Excel模板
        AppealRemoveImportVO importVO = mockImportData.get(0);
        
        assertNotNull(importVO.getPeriod());
        assertNotNull(importVO.getKpiName());
        assertNotNull(importVO.getTicketId());
        assertNotNull(importVO.getTenantCode());
        assertNotNull(importVO.getEditor());
        
        // 验证字段值
        assertEquals("2025-07", importVO.getPeriod());
        assertEquals("事件满意度", importVO.getKpiName());
        assertEquals(new BigDecimal("271595782"), importVO.getTicketId());
        assertEquals("tenant001", importVO.getTenantCode());
        assertEquals("admin", importVO.getEditor());
    }

    @Test
    void testExportVOStructure() {
        // 验证导出VO结构符合Excel模板
        AppealRemoveExportVO exportVO = mockExportData.get(0);
        
        assertNotNull(exportVO.getPeriod());
        assertNotNull(exportVO.getKpiName());
        assertNotNull(exportVO.getTicketId());
        
        // 验证字段值
        assertEquals("2025-07", exportVO.getPeriod());
        assertEquals("事件满意度", exportVO.getKpiName());
        assertEquals(new BigDecimal("271595782"), exportVO.getTicketId());
    }

    @Test
    void testPageRequestStructure() {
        // 验证分页请求结构
        assertNotNull(pageRequest.getPeriod());
        assertNotNull(pageRequest.getTenantCode());
        
        assertEquals("2025-02", pageRequest.getPeriod());
        assertEquals("tenant001", pageRequest.getTenantCode());
        assertEquals(1, pageRequest.getPage());
        assertEquals(10, pageRequest.getPageSize());
    }

    @Test
    void testExcelTemplateFormat() {
        // 验证Excel模板格式
        AppealRemoveExportVO template = new AppealRemoveExportVO();
        template.setPeriod("2025-07");
        template.setKpiName("事件满意度");
        template.setTicketId(new BigDecimal("271595782"));
        
        // 验证模板字段完整性
        assertNotNull(template.getPeriod());
        assertNotNull(template.getKpiName());
        assertNotNull(template.getTicketId());
        
        // 验证字段格式（根据Excel模板）
        // 绩效月 | 指标名称 | 需剔除的业务单号
        assertTrue(template.getPeriod().matches("\\d{4}-\\d{2}"));
        assertTrue(template.getTicketId().compareTo(BigDecimal.ZERO) > 0);
    }

    @Test
    void testDataValidation() {
        // 测试数据校验逻辑
        for (AppealRemoveImportVO importVO : mockImportData) {
            // 验证必填字段
            assertNotNull(importVO.getPeriod());
            assertNotNull(importVO.getKpiName());
            assertNotNull(importVO.getTicketId());
            assertNotNull(importVO.getTenantCode());
            
            // 验证绩效月格式
            assertTrue(importVO.getPeriod().matches("\\d{4}-\\d{2}"));
            
            // 验证单号格式（应该是正数）
            assertTrue(importVO.getTicketId().compareTo(BigDecimal.ZERO) > 0);
        }
    }

    @Test
    void testBusinessScenarios() {
        // 测试业务场景
        
        // 场景1：正常单号
        AppealRemoveImportVO normalTicket = createImportVO("2025-07", "事件满意度", "271595782");
        assertTrue(normalTicket.getTicketId().compareTo(BigDecimal.ZERO) > 0);
        
        // 场景2：大单号
        AppealRemoveImportVO largeTicket = createImportVO("2025-07", "通话满意度", "999999999");
        assertEquals(new BigDecimal("999999999"), largeTicket.getTicketId());
        
        // 场景3：不同指标
        AppealRemoveImportVO differentKpi = createImportVO("2025-07", "解决率", "123456789");
        assertEquals("解决率", differentKpi.getKpiName());
    }

    @Test
    void testFieldMapping() {
        // 测试字段映射正确性
        AppealRemoveImportVO importVO = mockImportData.get(0);
        
        // 验证字段名称符合Excel模板
        assertNotNull(importVO.getPeriod()); // 绩效月
        assertNotNull(importVO.getKpiName()); // 指标名称
        assertNotNull(importVO.getTicketId()); // 需剔除的业务单号
        
        // 验证导出VO字段映射
        AppealRemoveExportVO exportVO = mockExportData.get(0);
        assertNotNull(exportVO.getPeriod());
        assertNotNull(exportVO.getKpiName());
        assertNotNull(exportVO.getTicketId());
    }

    /**
     * 创建mock导入数据
     */
    private List<AppealRemoveImportVO> createMockImportData() {
        return Arrays.asList(
            createImportVO("2025-07", "事件满意度", "271595782"),
            createImportVO("2025-07", "通话满意度", "271595783"),
            createImportVO("2025-07", "解决率", "271595784"),
            createImportVO("2025-07", "首次解决率", "271595785"),
            createImportVO("2025-07", "响应时长", "271595786")
        );
    }

    /**
     * 创建mock导出数据
     */
    private List<AppealRemoveExportVO> createMockExportData() {
        return Arrays.asList(
            createExportVO("2025-07", "事件满意度", "271595782"),
            createExportVO("2025-07", "通话满意度", "271595783"),
            createExportVO("2025-07", "解决率", "271595784"),
            createExportVO("2025-07", "首次解决率", "271595785"),
            createExportVO("2025-07", "响应时长", "271595786")
        );
    }

    /**
     * 创建分页请求
     */
    private PageAppealRemoveRequest createPageRequest() {
        PageAppealRemoveRequest request = new PageAppealRemoveRequest();
        request.setPeriod("2025-02");
        request.setTenantCode("tenant001");
        request.setKpiName("事件满意度");
        request.setPage(1);
        request.setPageSize(10);
        return request;
    }

    /**
     * 创建导入VO对象
     */
    private AppealRemoveImportVO createImportVO(String period, String kpiName, String ticketId) {
        AppealRemoveImportVO importVO = new AppealRemoveImportVO();
        importVO.setPeriod(period);
        importVO.setKpiName(kpiName);
        importVO.setTicketId(new BigDecimal(ticketId));
        importVO.setTenantCode("tenant001");
        importVO.setKpiCd(kpiName.replaceAll("\\s+", "_").toUpperCase());
        importVO.setErp("system");
        importVO.setEditor("admin");
        return importVO;
    }

    /**
     * 创建导出VO对象
     */
    private AppealRemoveExportVO createExportVO(String period, String kpiName, String ticketId) {
        AppealRemoveExportVO exportVO = new AppealRemoveExportVO();
        exportVO.setPeriod(period);
        exportVO.setKpiName(kpiName);
        exportVO.setTicketId(new BigDecimal(ticketId));
        return exportVO;
    }
}
