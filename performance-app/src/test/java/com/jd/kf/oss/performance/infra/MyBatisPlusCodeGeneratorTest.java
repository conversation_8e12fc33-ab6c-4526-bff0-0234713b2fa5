package com.jd.kf.oss.performance.infra;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.List;

@SpringBootTest(classes = {MyBatisPlusCodeGeneratorTest.class})
public class MyBatisPlusCodeGeneratorTest {
    @Test
    public void testGenerator() {
        // 数据库连接信息
        List<String> tableNames = Lists.newArrayList("waiter_hour_adjustment");
        tableNames.forEach(a -> build(a));
    }

    private void build(String tableName) {
        String url = "*********************************************************************************************************************************";
        String username = "imtest_rw";
        String password = "5iht9BBHknVCMLQw";

        String projectPath1 = System.getProperty("user.dir");

        String projectPath = projectPath1.replaceAll("performance-app", "");

        FastAutoGenerator.create(url, username, password)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author("lilongguang.loong") // 设置作者
                            .outputDir(projectPath + "/performance-infra/src/main/java")// 指定输出目录
                            .disableOpenDir()
                            .fileOverride();
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent("com.jd.kf.oss.performance.infra.mybatis") // 设置父包名
                            .moduleName("") // 模块名，可留空
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/performance-infra/src/main/resources/mapper")); // 设置 mapper XML 文件的输出路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(tableName) // 设置需要生成的表名
                            // 实体策略配置
                            .entityBuilder()
                            .superClass("com.jd.kf.oss.performance.infra.mybatis.BaseEntity")
                            .enableLombok()
                            .addSuperEntityColumns("yn", "creator","tenantCode" ,"editor", "created", "modified")
                            .formatFileName("%sPO") // 实体类名添加 PO 后缀
                            .enableFileOverride()
                            // Mapper 策略配置
                            .mapperBuilder()
                            .formatMapperFileName("%sPOMapper") // Mapper 接口名添加 POMapper 后缀
                            .formatXmlFileName("%sPOMapper") // Mapper XML 文件添加 POMapper 后缀
                            // Service 策略配置
                            .serviceBuilder()
                            .formatServiceFileName("I%sPOService") // Service 接口名添加 IPOService 后缀
                            .formatServiceImplFileName("%sPOServiceImpl") // Service 实现类名添加 POServiceImpl 后缀
                            .enableFileOverride();
                })
                .templateEngine(new VelocityTemplateEngine())
                .execute();

    }
}
