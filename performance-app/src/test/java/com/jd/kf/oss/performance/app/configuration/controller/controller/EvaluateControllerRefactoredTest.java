package com.jd.kf.oss.performance.app.configuration.controller.controller;

import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.PageEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateImportVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 重构后的上级评价控制器测试类
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
class EvaluateControllerRefactoredTest {

    private List<EvaluateImportVO> mockImportData;
    private List<EvaluateExportVO> mockExportData;
    private PageEvaluateRequest pageRequest;

    @BeforeEach
    void setUp() {
        // 初始化mock数据
        mockImportData = createMockImportData();
        mockExportData = createMockExportData();
        
        // 初始化分页请求
        pageRequest = createPageRequest();
    }

    @Test
    void testImportVOStructure() {
        // 验证导入VO结构符合Excel模板
        EvaluateImportVO importVO = mockImportData.get(0);
        
        assertNotNull(importVO.getPeriod());
        assertNotNull(importVO.getManagerErp());
        assertNotNull(importVO.getEvaluationScore());
        //assertNotNull(importVO.getTenantCode());
        assertNotNull(importVO.getEditor());

        // 验证字段值
        assertEquals("2025-07", importVO.getPeriod());
        assertEquals("lanyue.9", importVO.getManagerErp());
        assertEquals("1", importVO.getEvaluationScore());
        //assertEquals("tenant001", importVO.getTenantCode());
        assertEquals("admin", importVO.getEditor());
    }

    @Test
    void testExportVOStructure() {
        // 验证导出VO结构符合Excel模板
        EvaluateExportVO exportVO = mockExportData.get(0);
        
        assertNotNull(exportVO.getPeriod());
        assertNotNull(exportVO.getManagerErp());
        assertNotNull(exportVO.getEvaluationScore());
        
        // 验证字段值
        assertEquals("2025-07", exportVO.getPeriod());
        assertEquals("lanyue.9", exportVO.getManagerErp());
        assertEquals("1.0", exportVO.getEvaluationScore());
    }

    @Test
    void testPageRequestStructure() {
        // 验证分页请求结构
        assertNotNull(pageRequest.getPeriod());
        assertNotNull(pageRequest.getManagerErp());
        
        assertEquals("2025-02", pageRequest.getPeriod());
        assertEquals("lanyue.9", pageRequest.getManagerErp());
    }

    @Test
    void testExcelTemplateFormat() {
        // 验证Excel模板格式
        EvaluateExportVO template = new EvaluateExportVO();
        template.setPeriod("2025-07");
        template.setManagerErp("lanyue.9");
        template.setEvaluationScore("1");

        // 验证模板字段完整性
        assertNotNull(template.getPeriod());
        assertNotNull(template.getManagerErp());
        assertNotNull(template.getEvaluationScore());

        // 验证字段顺序（根据Excel模板）
        // 绩效月 | 主管ERP | 评价分数
        assertTrue(template.getPeriod().matches("\\d{4}-\\d{2}"));
        assertTrue(template.getManagerErp().matches("^[a-zA-Z0-9._-]+$"));
        // 验证评价分数是数字格式
        try {
            double score = Double.parseDouble(template.getEvaluationScore());
            assertTrue(score >= 0);
        } catch (NumberFormatException e) {
            fail("评价分数应该是数字格式");
        }
    }

    @Test
    void testDataValidation() {
        // 测试数据校验逻辑
        for (EvaluateImportVO importVO : mockImportData) {
            // 验证必填字段
            assertNotNull(importVO.getPeriod());
            assertNotNull(importVO.getManagerErp());
            assertNotNull(importVO.getEvaluationScore());
            //assertNotNull(importVO.getTenantCode());
            
            // 验证绩效月格式
            assertTrue(importVO.getPeriod().matches("\\d{4}-\\d{2}"));
            
            // 验证ERP格式
            assertTrue(importVO.getManagerErp().matches("^[a-zA-Z0-9._-]+$"));
            
            // 验证评价分数范围（0-5分制）
            try {
                double score = Double.parseDouble(importVO.getEvaluationScore());
                assertTrue(score >= 0 && score <= 5);
            } catch (NumberFormatException e) {
                fail("评价分数应该是数字格式");
            }
        }
    }

    @Test
    void testFieldMapping() {
        // 测试字段映射正确性
        EvaluateImportVO importVO = mockImportData.get(0);
        
        // 验证字段名称符合新的命名规范
        assertNotNull(importVO.getManagerErp()); // 原来是erp，现在是managerErp
        assertNotNull(importVO.getEvaluationScore()); // 原来是score，现在是evaluationScore
        
        // 验证导出VO字段映射
        EvaluateExportVO exportVO = mockExportData.get(0);
        assertNotNull(exportVO.getManagerErp());
        assertNotNull(exportVO.getEvaluationScore());
    }

    @Test
    void testBusinessScenarios() {
        // 测试业务场景
        
        // 场景1：正常评价分数
        EvaluateImportVO normalScore = createImportVO("2025-07", "manager001", "3.5");
        double score1 = Double.parseDouble(normalScore.getEvaluationScore());
        assertTrue(score1 > 0 && score1 <= 5);

        // 场景2：最高评价分数
        EvaluateImportVO maxScore = createImportVO("2025-07", "manager002", "5");
        assertEquals("5", maxScore.getEvaluationScore());

        // 场景3：最低评价分数
        EvaluateImportVO minScore = createImportVO("2025-07", "manager003", "0");
        assertEquals("0", minScore.getEvaluationScore());
    }

    /**
     * 创建mock导入数据
     */
    private List<EvaluateImportVO> createMockImportData() {
        return Arrays.asList(
            createImportVO("2025-07", "lanyue.9", "1"),
            createImportVO("2025-07", "lanyue.9", "0.8"),
            createImportVO("2025-07", "manager001", "4.5"),
            createImportVO("2025-07", "manager002", "3.2"),
            createImportVO("2025-07", "manager003", "5.0")
        );
    }

    /**
     * 创建mock导出数据
     */
    private List<EvaluateExportVO> createMockExportData() {
        return Arrays.asList(
            createExportVO("2025-07", "lanyue.9", "1.0"),
            createExportVO("2025-07", "lanyue.9", "0.8"),
            createExportVO("2025-07", "manager001", "4.5"),
            createExportVO("2025-07", "manager002", "3.2"),
            createExportVO("2025-07", "manager003", "5.0")
        );
    }

    /**
     * 创建分页请求
     */
    private PageEvaluateRequest createPageRequest() {
        PageEvaluateRequest request = new PageEvaluateRequest();
        request.setPeriod("2025-02");
        request.setManagerErp("lanyue.9");
        request.setPage(1);
        request.setPageSize(10);
        return request;
    }

    /**
     * 创建导入VO对象
     */
    private EvaluateImportVO createImportVO(String period, String managerErp, String score) {
        EvaluateImportVO importVO = new EvaluateImportVO();
        importVO.setPeriod(period);
        importVO.setManagerErp(managerErp);
        importVO.setEvaluationScore(score);
        //importVO.setTenantCode("tenant001");
        importVO.setEditor("admin");
        return importVO;
    }

    /**
     * 创建导出VO对象
     */
    private EvaluateExportVO createExportVO(String period, String managerErp, String score) {
        EvaluateExportVO exportVO = new EvaluateExportVO();
        exportVO.setPeriod(period);
        exportVO.setManagerErp(managerErp);
        exportVO.setEvaluationScore(score);
        return exportVO;
    }
}
