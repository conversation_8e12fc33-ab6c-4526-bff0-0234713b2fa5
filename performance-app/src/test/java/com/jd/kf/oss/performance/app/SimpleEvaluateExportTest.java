package com.jd.kf.oss.performance.app;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化版评价数据导出测试
 * 不依赖外部类，专注于测试核心逻辑
 */
class SimpleEvaluateExportTest {

    private List<MockEvaluateDO> mockEvaluateData;
    private MockExportRequest exportRequest;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockEvaluateData = createMockEvaluateData();
        exportRequest = createExportRequest();
    }

    @Test
    @DisplayName("测试Mock数据创建")
    void testMockDataCreation() {
        // 验证mock数据创建正确
        assertNotNull(mockEvaluateData);
        assertEquals(5, mockEvaluateData.size());
        
        MockEvaluateDO firstEvaluate = mockEvaluateData.get(0);
        assertEquals("tenant001", firstEvaluate.getTenantCode());
        assertEquals("manager001", firstEvaluate.getErp());
        assertEquals(new BigDecimal("85.5"), firstEvaluate.getScore());
        assertEquals("2025-07", firstEvaluate.getPeriod());
        assertEquals(Long.valueOf(1), firstEvaluate.getId());
        
        // 验证所有数据的完整性
        for (MockEvaluateDO evaluate : mockEvaluateData) {
            assertNotNull(evaluate.getTenantCode());
            assertNotNull(evaluate.getErp());
            assertNotNull(evaluate.getScore());
            assertNotNull(evaluate.getPeriod());
            assertNotNull(evaluate.getId());
            
            // 验证评分在合理范围内
            assertTrue(evaluate.getScore().compareTo(BigDecimal.ZERO) >= 0);
            assertTrue(evaluate.getScore().compareTo(new BigDecimal("100")) <= 0);
        }
    }

    @Test
    @DisplayName("测试导出请求创建")
    void testExportRequestCreation() {
        // 验证导出请求创建正确
        assertNotNull(exportRequest);
        assertEquals("tenant001", exportRequest.getTenantCode());
        assertEquals("2025-07", exportRequest.getPeriod());
        assertNotNull(exportRequest.getManagerErps());
        assertEquals(5, exportRequest.getManagerErps().size());
        assertTrue(exportRequest.getManagerErps().contains("manager001"));
        assertTrue(exportRequest.getManagerErps().contains("manager005"));
    }

    @Test
    @DisplayName("测试数据过滤逻辑")
    void testDataFiltering() {
        // 创建包含干扰数据的完整数据集
        List<MockEvaluateDO> allData = createComprehensiveData();
        
        // 按条件过滤数据
        List<MockEvaluateDO> filteredData = filterDataByRequest(allData, exportRequest);
        
        // 验证过滤结果
        assertEquals(5, filteredData.size());
        
        // 验证所有过滤后的数据都符合条件
        for (MockEvaluateDO data : filteredData) {
            assertEquals(exportRequest.getTenantCode(), data.getTenantCode());
            assertEquals(exportRequest.getPeriod(), data.getPeriod());
            assertTrue(exportRequest.getManagerErps().contains(data.getErp()));
        }
    }

    @Test
    @DisplayName("测试数据转换逻辑")
    void testDataConversion() {
        // 测试DO到ExportVO的转换逻辑
        List<MockExportVO> exportVOList = convertDOListToExportVOList(mockEvaluateData);
        
        assertNotNull(exportVOList);
        assertEquals(mockEvaluateData.size(), exportVOList.size());
        
        for (int i = 0; i < mockEvaluateData.size(); i++) {
            MockEvaluateDO evaluateDO = mockEvaluateData.get(i);
            MockExportVO exportVO = exportVOList.get(i);
            
            assertEquals(evaluateDO.getTenantCode(), exportVO.getTenantCode());
            assertEquals(evaluateDO.getErp(), exportVO.getErp());
            assertEquals(evaluateDO.getScore(), exportVO.getScore());
            assertEquals(evaluateDO.getPeriod(), exportVO.getPeriod());
        }
    }

    @Test
    @DisplayName("测试不同评分范围的数据")
    void testDifferentScoreRanges() {
        // 验证数据包含不同评分范围
        List<BigDecimal> scores = mockEvaluateData.stream()
            .map(MockEvaluateDO::getScore)
            .collect(Collectors.toList());
        
        // 验证有低分、中分、高分数据
        assertTrue(scores.stream().anyMatch(score -> score.compareTo(new BigDecimal("80")) < 0)); // 低分
        assertTrue(scores.stream().anyMatch(score -> 
            score.compareTo(new BigDecimal("80")) >= 0 && score.compareTo(new BigDecimal("90")) < 0)); // 中分
        assertTrue(scores.stream().anyMatch(score -> score.compareTo(new BigDecimal("90")) >= 0)); // 高分
    }

    @Test
    @DisplayName("测试边界数据")
    void testBoundaryData() {
        // 创建边界数据
        List<MockEvaluateDO> boundaryData = Arrays.asList(
            createMockEvaluateDO("tenant001", "manager001", new BigDecimal("0"), "2025-07", 1L),
            createMockEvaluateDO("tenant001", "manager002", new BigDecimal("100"), "2025-07", 2L),
            createMockEvaluateDO("tenant001", "manager003", new BigDecimal("50.5"), "2025-07", 3L)
        );
        
        List<MockExportVO> exportVOList = convertDOListToExportVOList(boundaryData);
        
        assertEquals(new BigDecimal("0"), exportVOList.get(0).getScore());
        assertEquals(new BigDecimal("100"), exportVOList.get(1).getScore());
        assertEquals(new BigDecimal("50.5"), exportVOList.get(2).getScore());
    }

    @Test
    @DisplayName("测试Excel文件名生成")
    void testExcelFileNameGeneration() {
        String fileName = generateFileName(exportRequest.getPeriod());
        assertEquals("2025-07-评价数据", fileName);
        
        // 测试不同期间
        assertEquals("2025-06-评价数据", generateFileName("2025-06"));
        assertEquals("2024-12-评价数据", generateFileName("2024-12"));
    }

    /**
     * 创建mock评价数据
     */
    private List<MockEvaluateDO> createMockEvaluateData() {
        return Arrays.asList(
            createMockEvaluateDO("tenant001", "manager001", new BigDecimal("85.5"), "2025-07", 1L),
            createMockEvaluateDO("tenant001", "manager002", new BigDecimal("92.0"), "2025-07", 2L),
            createMockEvaluateDO("tenant001", "manager003", new BigDecimal("78.8"), "2025-07", 3L),
            createMockEvaluateDO("tenant001", "manager004", new BigDecimal("95.2"), "2025-07", 4L),
            createMockEvaluateDO("tenant001", "manager005", new BigDecimal("88.7"), "2025-07", 5L)
        );
    }

    /**
     * 创建综合测试数据（包含干扰数据）
     */
    private List<MockEvaluateDO> createComprehensiveData() {
        return Arrays.asList(
            // 目标导出数据
            createMockEvaluateDO("tenant001", "manager001", new BigDecimal("85.5"), "2025-07", 1L),
            createMockEvaluateDO("tenant001", "manager002", new BigDecimal("92.0"), "2025-07", 2L),
            createMockEvaluateDO("tenant001", "manager003", new BigDecimal("78.8"), "2025-07", 3L),
            createMockEvaluateDO("tenant001", "manager004", new BigDecimal("95.2"), "2025-07", 4L),
            createMockEvaluateDO("tenant001", "manager005", new BigDecimal("88.7"), "2025-07", 5L),
            
            // 干扰数据（不在导出范围内）
            createMockEvaluateDO("tenant001", "manager006", new BigDecimal("76.3"), "2025-07", 6L), // 不在ERP列表
            createMockEvaluateDO("tenant002", "manager001", new BigDecimal("82.4"), "2025-07", 7L), // 不同租户
            createMockEvaluateDO("tenant001", "manager001", new BigDecimal("87.6"), "2025-06", 8L)  // 不同期间
        );
    }

    /**
     * 创建导出请求
     */
    private MockExportRequest createExportRequest() {
        MockExportRequest request = new MockExportRequest();
        request.setTenantCode("tenant001");
        request.setPeriod("2025-07");
        request.setManagerErps(Arrays.asList("manager001", "manager002", "manager003", "manager004", "manager005"));
        return request;
    }

    /**
     * 创建mock评价DO对象
     */
    private MockEvaluateDO createMockEvaluateDO(String tenantCode, String erp, BigDecimal score, String period, Long id) {
        MockEvaluateDO evaluateDO = new MockEvaluateDO();
        evaluateDO.setId(id);
        evaluateDO.setTenantCode(tenantCode);
        evaluateDO.setErp(erp);
        evaluateDO.setScore(score);
        evaluateDO.setPeriod(period);
        evaluateDO.setCreated(LocalDateTime.now().minusDays(id));
        evaluateDO.setModified(LocalDateTime.now());
        return evaluateDO;
    }

    /**
     * 根据请求过滤数据
     */
    private List<MockEvaluateDO> filterDataByRequest(List<MockEvaluateDO> allData, MockExportRequest request) {
        return allData.stream()
            .filter(data -> request.getTenantCode().equals(data.getTenantCode()))
            .filter(data -> request.getPeriod().equals(data.getPeriod()))
            .filter(data -> request.getManagerErps() == null || request.getManagerErps().contains(data.getErp()))
            .collect(Collectors.toList());
    }

    /**
     * 转换为导出VO
     */
    private List<MockExportVO> convertDOListToExportVOList(List<MockEvaluateDO> evaluateDOList) {
        return evaluateDOList.stream()
            .map(this::convertDOToExportVO)
            .collect(Collectors.toList());
    }

    /**
     * DO转ExportVO
     */
    private MockExportVO convertDOToExportVO(MockEvaluateDO evaluateDO) {
        MockExportVO exportVO = new MockExportVO();
        exportVO.setTenantCode(evaluateDO.getTenantCode());
        exportVO.setErp(evaluateDO.getErp());
        exportVO.setScore(evaluateDO.getScore());
        exportVO.setPeriod(evaluateDO.getPeriod());
        return exportVO;
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String period) {
        return period + "-评价数据";
    }

    // Mock类定义
    static class MockEvaluateDO {
        private Long id;
        private String tenantCode;
        private String erp;
        private BigDecimal score;
        private String period;
        private LocalDateTime created;
        private LocalDateTime modified;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public String getErp() { return erp; }
        public void setErp(String erp) { this.erp = erp; }
        public BigDecimal getScore() { return score; }
        public void setScore(BigDecimal score) { this.score = score; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public LocalDateTime getCreated() { return created; }
        public void setCreated(LocalDateTime created) { this.created = created; }
        public LocalDateTime getModified() { return modified; }
        public void setModified(LocalDateTime modified) { this.modified = modified; }
    }

    static class MockExportVO {
        private String tenantCode;
        private String erp;
        private BigDecimal score;
        private String period;

        // Getters and Setters
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public String getErp() { return erp; }
        public void setErp(String erp) { this.erp = erp; }
        public BigDecimal getScore() { return score; }
        public void setScore(BigDecimal score) { this.score = score; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
    }

    static class MockExportRequest {
        private String tenantCode;
        private String period;
        private List<String> managerErps;

        // Getters and Setters
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        public List<String> getManagerErps() { return managerErps; }
        public void setManagerErps(List<String> managerErps) { this.managerErps = managerErps; }
    }
}
