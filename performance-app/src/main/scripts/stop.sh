#!/bin/bash
#预发测试

set -o errexit
set -o nounset

readonly APP_NAME="performance"          #定义当前应用的名称
readonly EXE_APP_NAME="org.springframework.boot.loader.JarLauncher"
#获取当前应用的进程 id
function get_running_pid
{
  pgrep -f "$EXE_APP_NAME"
}

readonly SELF_DIR=$(cd $(dirname $0) && pwd)

function stop
{
    local -i timeout=20
    local -i interval=
    local -r service_pid=$(get_running_pid) || true # ignore error
    [[ -n $service_pid ]] || {
        echo "WARNING: process not found, nothing to stop" >&2
        exit 0
    }
    echo "killing process $service_pid"
    kill $service_pid
    while (( timeout > 0 )) && get_running_pid > /dev/null; do
        echo -n "."ƒ
        sleep $interval
        timeout=$(( timeout - interval ))
    done
    if get_running_pid > /dev/null; then
        echo "WARNING: process still alive, sending SIGKILL ..." >&2
        kill -9 "$service_pid"
    fi
}
function main
{
    get_running_pid > /dev/null || {
        echo "WARNING: process not found, nothing to stop" >&2
        exit 0  # Ignore error
    }
    stop
}
main "$@"