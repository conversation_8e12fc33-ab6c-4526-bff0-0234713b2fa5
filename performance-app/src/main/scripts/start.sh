#! /bin/sh 

SCRIPT_PATH=`dirname $0`
cd $SCRIPT_PATH

CORE_ROOT_PATH=${PWD%/*}
CORE_CLASSPATH=$CORE_ROOT_PATH:$CORE_ROOT_PATH/config

for f in $CORE_ROOT_PATH/lib/*.jar; do
  CORE_CLASSPATH=$CORE_CLASSPATH:$f;
done

export CORE_CLASSPATH

#keeper start
cd /home/<USER>/App/lib
KEEPER_ROOT_PATH=${PWD%/*}
agentJarPath=
for f in $KEEPER_ROOT_PATH/lib/*.jar; do
  if [[ $f == *"keeper-client-agent"* ]]; then
        agentJarPath=$f
  fi
done
echo "Keeper agent jar path is : "$agentJarPath >> /export/Logs/keeper.log
if [[ ! -n $agentJarPath ]]; then
        echo "Keeper agent jar not found!!!" >> /export/Logs/keeper.log
else
        echo "Keeper agent jar found!!!" >> /export/Logs/keeper.log
        JAVA_AGENT='-javaagent:'$agentJarPath' '
fi
echo "JAVA_AGENT is : "$JAVA_AGENT >> /export/Logs/keeper.log
export JAVA_AGENT
#keeper end

JDK8_BIN=/export/servers/jdk1.8.0_191/bin
echo $JDK8_BIN

curl -s "http://pfinder-master.jd.com/access/script" -o /tmp/pfinder.sh ; source /tmp/pfinder.sh || :

JAVA_OPTS="$JAVA_OPTS -Xmx2g -Xms256M -XX:MetaspaceSize=256m  -XX:MaxMetaspaceSize=256m -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:-UseAdaptiveSizePolicy -XX:+PrintAdaptiveSizePolicy -XX:+HeapDumpBeforeFullGC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=export/Logs/ -Xloggc:/export/Logs/gclogs.log"


nohup $JDK8_BIN/java -server ${PFINDER_AGENT:-} $JAVA_OPTS $JAVA_AGENT -Ddeploy.app.name=im-scheduling -classpath $CORE_CLASSPATH com.jd.kf.oss.performance.app.PerformanceApplication >/dev/null 2>&1 &