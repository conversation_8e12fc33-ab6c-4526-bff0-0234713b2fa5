package com.jd.kf.oss.performance.app.controller.controller;

import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.BaseImportRequest;
import com.jd.kf.oss.performance.app.controller.service.AppealRemoveService;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.BatchDeleteAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.ExportAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.PageAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.converter.AppealRemoveConverter;
import com.jd.kf.oss.performance.app.controller.dto.vo.AppealRemoveVO;
import com.jd.kf.oss.performance.domain.config.domain.appealremove.AppealRemoveDO;
import com.jd.kf.oss.performance.domain.config.domain.appealremove.AppealRemoveDomainService;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.CommonPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 无效数据剔除控制器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Api(tags = "无效数据剔除管理")
@RestController
@RequestMapping("/performance/appeal_remove")
public class AppealRemoveController {

    @Resource
    private AppealRemoveDomainService appealRemoveDomainService;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private AppealRemoveService appealRemoveService;

    /**
     * 分页查询无效数据剔除
     * @param request 分页查询请求参数
     * @return 分页结果
     */
    @ApiOperation("分页查询无效数据剔除")
    @PostMapping("/page")
    public ApiResult<CommonPage<AppealRemoveVO>> pageAppealRemove(@RequestBody @Valid PageAppealRemoveRequest request) {
        // 使用字段参数方式调用DomainService
        CommonPage<AppealRemoveDO> pageResult = appealRemoveDomainService.queryAppealRemoveByConditions(
                request.getPeriod(),
                request.getKpiName(),
                request.getTicketId(),
                request.getPage(),
                request.getPageSize()
        );

        // 转换结果
        CommonPage<AppealRemoveVO> result = AppealRemoveConverter.INSTANCE.pageDO2PageVO(pageResult);

        return ApiResult.success(result);
    }

    /**
     * 批量删除无效数据剔除
     * @param request 批量删除请求参数
     * @return 操作结果
     */
    @ApiOperation("批量删除无效数据剔除")
    @PostMapping("/delete")
    public ApiResult<Void> batchDeleteAppealRemove(@RequestBody @Validated BatchDeleteAppealRemoveRequest request) {
        boolean success = appealRemoveDomainService.batchDelete(
            request.getIds());
        
        return success ? ApiResult.success() : ApiResult.error("删除失败");
    }

    /**
     * 导出无效数据剔除
     * @param request 导出请求参数
     * @throws IOException IO异常
     */
    @ApiOperation("导出无效数据剔除")
    @PostMapping("/export")
    public ApiResult<String> exportAppealRemove(@RequestBody @Valid ExportAppealRemoveRequest request,
                                   HttpServletRequest httpServletRequest) throws IOException {
        return ApiResult.success(appealRemoveService.exportAppealRemove(request, httpServletRequest));
    }

    /**
     * 导入无效数据剔除
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     * @throws IOException IO异常
     */
    @ApiOperation("导入无效数据剔除")
    @PostMapping("/import")
    public ApiResult<String> importAppealRemove(@RequestBody @Validated BaseImportRequest request,
                                             HttpServletRequest httpServletRequest) throws IOException {

        return appealRemoveService.importAppealRemove(request.getUrl(), httpServletRequest);
    }



    /**
     * 查询无效数据剔除Excel模板
     */
    @PostMapping("/excel")
    public ApiResult<String> queryAppealRemoveExcelTemplate() {
        return ApiResult.success(dynamicConfig.getAppealRemoveExcelTemplate());
    }
}
