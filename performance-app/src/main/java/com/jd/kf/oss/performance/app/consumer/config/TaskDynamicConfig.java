package com.jd.kf.oss.performance.app.consumer.config;

import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: songweijia1
 * @description: 消费任务相关动态配置
 * @date: 2025/7/2
 */

@Slf4j
@Data
@Component
public class TaskDynamicConfig {

    @LafValue("performance.build.wfc.waiter.info.open")
    private boolean buildWfcWaiterInfoOpen = true;

    @LafValue("performance.persistent.wfc.waiter.info.open")
    private boolean persistentWfcWaiterInfo = true;

}
