package com.jd.kf.oss.performance.app.consumer.mq.processor.adapter;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author: songweijia1
 * @description: WfcDeptPO的适配器实现
 * @date: 2025/7/7
 */

@Component
public class WfcDeptAdapter implements ModelAdapter<WfcDeptPO> {

    @Override
    public Long getId(WfcDeptPO model) {
        return model.getId();
    }

    @Override
    public void setId(WfcDeptPO model, Long id) {
        model.setId(id);
    }

    @Override
    public String getBusinessKey(WfcDeptPO model) {
        return model.getDeptId();
    }

    @Override
    public LocalDateTime getModified(WfcDeptPO model) {
        return model.getModified();
    }

    @Override
    public Boolean getYn(WfcDeptPO model) {
        return model.getYn();
    }

    @Override
    public void setYn(WfcDeptPO model, Boolean yn) {
        model.setYn(yn);
    }

    @Override
    public void setUniqId(WfcDeptPO model, Long uniqId) {
        model.setUniqId(uniqId);
    }

}
