package com.jd.kf.oss.performance.app.service;

import com.jd.jsl.base.account.dto.UserInfoResultDTO;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/4/1 23:23
 * @Description:
 * @Version 1.0
 */
public interface JslAccountService {

    UserInfoResultDTO getUserInfoByToken(String token);

    /**
     * 统一运营门户获取有权限的所有租户列表
     * @param token
     * @return
     */
    Set<String> getPortalResourceCodeListByToken(String token);

    /**
     * 根据token和资源类型查询资源code列表
     * @param token
     * @param type
     * @return
     */
    Set<String> getTenantCodeListByTokenAndType(String token, Integer type);

}
