package com.jd.kf.oss.performance.app.controller.dto.requestvalidator.coefficient;

import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class CoefficientTypeValidator implements ConstraintValidator<CoefficientTypeValid, String> {
        private boolean notNull;
        @Override
        public void initialize(CoefficientTypeValid constraintAnnotation) {
            this.notNull = constraintAnnotation.notNull();
        }
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            CoefficientTemplateTypeEnum type = CoefficientTemplateTypeEnum.getByType(value);
            // 非空校验
            if (notNull) {
                return type!=null;
            }
            // 空校验 如果value不为空，则type不能为空
            return value == null || type != null;
        }
    }
