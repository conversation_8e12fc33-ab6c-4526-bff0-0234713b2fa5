package com.jd.kf.oss.performance.app.controller.dto.vo.appeal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 无效数据剔除导出VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class AppealRemoveExportVO {

    /** 绩效月 */
    @ExcelProperty(value = "绩效月", index = 0)
    @ColumnWidth(12)
    private String period;

    /** 指标名称 */
    @ExcelProperty(value = "指标名称", index = 1)
    @ColumnWidth(20)
    private String kpiName;

    /** 需剔除的业务单号 */
    @ExcelProperty(value = "需剔除的业务单据", index = 2)
    @ColumnWidth(18)
    private BigDecimal ticketId;
}
