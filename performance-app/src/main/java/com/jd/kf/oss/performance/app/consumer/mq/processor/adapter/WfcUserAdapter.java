package com.jd.kf.oss.performance.app.consumer.mq.processor.adapter;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserPO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author: songweijia1
 * @description: WfcUserPO的适配器实现
 * @date: 2025/7/7
 */

@Component
public class WfcUserAdapter implements ModelAdapter<WfcUserPO> {

    @Override
    public Long getId(WfcUserPO model) {
        return model.getId();
    }

    @Override
    public void setId(WfcUserPO model, Long id) {
        model.setId(id);
    }

    @Override
    public String getBusinessKey(WfcUserPO model) {
        return model.getErp();
    }

    @Override
    public LocalDateTime getModified(WfcUserPO model) {
        return model.getModified();
    }

    @Override
    public Boolean getYn(WfcUserPO model) {
        return model.getYn();
    }

    @Override
    public void setYn(WfcUserPO model, Boolean yn) {
        model.setYn(yn);
    }

    @Override
    public void setUniqId(WfcUserPO model, Long uniqId) {
        model.setUniqId(uniqId);
    }

}
