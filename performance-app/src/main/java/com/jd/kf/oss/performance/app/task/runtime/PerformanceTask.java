package com.jd.kf.oss.performance.app.task.runtime;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/30
 * 绩效组
 */
@Component
@DongJob(name = "绩效组绩效月创建计算任务")
@Slf4j
public class PerformanceTask extends AbstractJobHandler {
    @Autowired
    private BusinessLineDomainService businessLineDomainService;

    /**
     * @param jobContext
     * @return {@link JobResult }
     */
    @Override
    public JobResult execute(JobContext jobContext) {
        String tenantCode = "kf_perform_retail";
        //String period = DateUtils.getCurrentPerformancePeriod();
        String period = "2025-07";
        List<BusinessLineDO> businessLines = businessLineDomainService.queryAllBusinessLine();
        PerformanceTaskDO performanceTaskDO = new PerformanceTaskDO();
        performanceTaskDO.create(tenantCode, period, businessLines);
        return JobResult.SUCCESS;
    }
}
