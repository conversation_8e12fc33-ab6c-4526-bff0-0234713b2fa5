package com.jd.kf.oss.performance.app.consumer.mq.processor;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: songweijia1
 * @description: 单表数据订阅处理结果实体类
 * @date: 2025/7/2
 */

@Getter
public class TableProcessResult<T> {

    private final List<T> total = new ArrayList<>();
    private final List<T> toUpsert = new ArrayList<>();
    private final List<T> toDelete = new ArrayList<>();

    public void addToTotal(T item) {
        if (Objects.nonNull(item)) {
            total.add(item);
        }
    }

    public void addToUpsert(T item) {
        if (Objects.nonNull(item)) {
            toUpsert.add(item);
        }
    }

    public void addToDelete(T item) {
        if (Objects.nonNull(item)) {
            toDelete.add(item);
        }
    }

    public boolean hasTotalData() {
        return CollectionUtils.isNotEmpty(total);
    }

    public boolean hasUpsertData() {
        return CollectionUtils.isNotEmpty(toUpsert);
    }

    public boolean hasToDeleteData() {
        return CollectionUtils.isNotEmpty(toDelete);
    }

    public boolean hasAnyData() {
        return hasTotalData();
    }

}
