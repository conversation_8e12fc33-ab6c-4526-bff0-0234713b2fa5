package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ImportCPDVO {

    @JsonProperty(value = "绩效月")
    private String period;

    /**
     * 绩效组id
     */
    @JsonProperty(value = "绩效组ID")
    private String businessLineId;

    /**
     * cpd
     */
    @JsonProperty(value = "目标CPD")
    private String cpd;



}
