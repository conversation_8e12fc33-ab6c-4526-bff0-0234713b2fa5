package com.jd.kf.oss.performance.app.controller.dto.request.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class BasePageRequest {

    /**
     * 当前页数
     */
    @NotNull
    @Min(1)
    @ApiModelProperty(value = "页号")
    private Integer page = 1;

    /**
     * 页面大小
     */
    @NotNull
    @Max(2000)
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize = 20;
}


