package com.jd.kf.oss.performance.app.controller.dto.request.index;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IndexBasicPageRequest extends BasePageRequest implements Serializable {
    /**
     * 指标code
     */
    @ApiModelProperty("指标code")
    private String kpiCd;
    /**
     * 指标名称
     */
    @ApiModelProperty("指标名称")
    private String kpiName;
    /**
     * 指标状态
     */
    @ApiModelProperty("指标状态")
    private IndexStatusEnum status;
}
