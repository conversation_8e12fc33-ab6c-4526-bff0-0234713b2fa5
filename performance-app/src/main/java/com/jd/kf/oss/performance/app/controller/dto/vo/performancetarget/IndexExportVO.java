package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.jd.kf.oss.performance.enums.IndexTemplate;
import lombok.Data;

@Data
public class IndexExportVO {

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 指标类型
     */
    private IndexTemplate template;


    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 样本下限
     */
    private String threshold;


    /**
     * 权重
     */
    private String weight;
}
