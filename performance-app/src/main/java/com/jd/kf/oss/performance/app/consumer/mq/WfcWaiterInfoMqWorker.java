package com.jd.kf.oss.performance.app.consumer.mq;

import com.jd.jmq.client.consumer.Consumer;
import com.jd.jsf.gd.util.NamedThreadFactory;
import com.jd.kf.oss.performance.app.consumer.config.JmqConfig;
import com.jd.kf.oss.performance.app.consumer.config.TaskDynamicConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 零售cchome系统客服基础信息数据订阅任务
 * <p>
 * 负责从JMQ消息队列订阅并处理客服基础信息数据
 * </p>
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class WfcWaiterInfoMqWorker implements InitializingBean, DisposableBean {

    private static final int SHUTDOWN_TIMEOUT_SECONDS = 30;
    private static final int BACKOFF_MULTIPLIER = 5;

    @Autowired(required = false)
    private Consumer consumer;

    @Resource
    private JmqConfig jmqConfig;

    @Resource
    private TaskDynamicConfig taskDynamicConfig;

    @Resource
    private WfcWaiterInfoListener wfcWaiterInfoListener;

    private ThreadPoolExecutor consumeExecutor;

    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    /**
     * Spring容器初始化后执行
     */
    @Override
    public void afterPropertiesSet() {
        if (!taskDynamicConfig.isBuildWfcWaiterInfoOpen()) {
            log.info("WfcWaiterInfo consumer is disabled by configuration");
            return;
        }

        try {
            initializeWorker();
            log.info("WfcWaiterInfo worker initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize WfcWaiterInfo worker", e);
        }
    }

    /**
     * 初始化工作组件
     */
    private void initializeWorker() {
        validateConfiguration();
        createThreadPool();
        startConsumer();

        if (isRunning.get()) {
            submitConsumeTasks();
        }
    }

    /**
     * 验证配置参数
     */
    private void validateConfiguration() {
        Assert.notNull(jmqConfig.getWfcWaiterInfoSyncTopic(), "Topic cannot be null");
        Assert.isTrue(jmqConfig.getWfcWaiterInfoConsumeThreadCount() > 0, "Thread count must be positive");
        Assert.isTrue(jmqConfig.getWfcWaiterInfoConsumeQueueLen() > 0, "Queue length must be positive");
        Assert.isTrue(jmqConfig.getWfcWaiterInfoConsumeInterval() >= 0, "Consume interval must be non-negative");
    }

    /**
     * 创建消费线程池
     */
    private void createThreadPool() {
        int threadCount = jmqConfig.getWfcWaiterInfoConsumeThreadCount();
        int queueCapacity = jmqConfig.getWfcWaiterInfoConsumeQueueLen();

        log.info("WfcWaiterInfoMqWorker Creating thread pool with {} threads and queue capacity {}",
                threadCount, queueCapacity);

        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(queueCapacity);
        consumeExecutor = new ThreadPoolExecutor(
                threadCount,
                threadCount,
                10L,
                TimeUnit.MINUTES,
                workQueue,
                new NamedThreadFactory("wfc-waiter-info-consumer"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 添加线程池监控
        consumeExecutor.setRejectedExecutionHandler((r, executor) -> {
            log.warn("WfcWaiterInfoMqWorker Task rejected, queue size: {}, active threads: {}",
                    executor.getQueue().size(), executor.getActiveCount());
            new ThreadPoolExecutor.CallerRunsPolicy().rejectedExecution(r, executor);
        });
    }

    /**
     * 启动JMQ消费者
     */
    private void startConsumer() {
        try {
            consumer.start();
            isRunning.set(true);
            log.info("WfcWaiterInfo consumer started successfully");
        } catch (Exception e) {
            isRunning.set(false);
            log.error("Failed to start WfcWaiterInfo consumer", e);
            throw new RuntimeException("Failed to start consumer", e);
        }
    }

    /**
     * 提交消费任务到线程池
     */
    private void submitConsumeTasks() {
        String topic = jmqConfig.getWfcWaiterInfoSyncTopic();
        int threadCount = jmqConfig.getWfcWaiterInfoConsumeThreadCount();

        log.info("WfcWaiterInfoMqWorker Submitting {} consume tasks for topic: {}", threadCount, topic);

        for (int i = 0; i < threadCount; i++) {
            consumeExecutor.execute(new MessageConsumerTask(topic, wfcWaiterInfoListener, consumer));
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void destroy() {
        log.info("Shutting down WfcWaiterInfo worker...");
        shutdownConsumer();
        shutdownThreadPool();
    }

    /**
     * 关闭消费者
     */
    private void shutdownConsumer() {
        isRunning.set(false);
        try {
            if (consumer != null) {
                consumer.stop();
                log.info("WfcWaiterInfo consumer stopped");
            }
        } catch (Exception e) {
            log.error("Error stopping WfcWaiterInfo consumer", e);
        }
    }

    /**
     * 关闭线程池
     */
    private void shutdownThreadPool() {
        if (consumeExecutor == null || consumeExecutor.isShutdown()) {
            return;
        }

        try {
            // 优雅关闭
            consumeExecutor.shutdown();

            if (!consumeExecutor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                // 强制关闭
                int remainingTasks = consumeExecutor.shutdownNow().size();
                log.warn("Force shutdown thread pool with {} remaining tasks", remainingTasks);

                // 再次等待
                if (!consumeExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("Thread pool did not terminate gracefully");
                }
            }

            log.info("Thread pool shutdown completed");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted during shutdown", e);
            // 确保线程池关闭
            consumeExecutor.shutdownNow();
        }
    }

    /**
     * 消息消费任务
     */
    private class MessageConsumerTask implements Runnable {
        private final String topic;
        private final WfcWaiterInfoListener listener;
        private final Consumer consumer;
        private int consecutiveErrors = 0;

        public MessageConsumerTask(String topic, WfcWaiterInfoListener listener, Consumer consumer) {
            this.topic = topic;
            this.listener = listener;
            this.consumer = consumer;
        }

        @Override
        public void run() {
            log.info("Starting consumer task for topic: {}", topic);

            while (isRunning.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    int messageCount = pullAndProcessMessages();
                    handleSuccessfulPull(messageCount);
                    resetErrorCounter();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Consumer task interrupted for topic: {}", topic);
                    break;
                } catch (Exception e) {
                    handleError(e);
                }
            }

            log.info("Consumer task stopped for topic: {}", topic);
        }

        /**
         * 拉取并处理消息
         */
        private int pullAndProcessMessages() {
            return consumer.pull(topic, listener);
        }

        /**
         * 处理成功拉取的情况
         */
        private void handleSuccessfulPull(int messageCount) throws InterruptedException {
            long interval = jmqConfig.getWfcWaiterInfoConsumeInterval();

            if (messageCount <= 0) {
                // 无消息时使用更长的间隔
                TimeUnit.MILLISECONDS.sleep(BACKOFF_MULTIPLIER * interval);
            } else {
                // 有消息时使用正常间隔
                TimeUnit.MILLISECONDS.sleep(interval);
            }
        }

        /**
         * 重置错误计数器
         */
        private void resetErrorCounter() {
            if (consecutiveErrors > 0) {
                consecutiveErrors = 0;
            }
        }

        /**
         * 处理错误
         */
        private void handleError(Exception e) {
            consecutiveErrors++;

            // 根据连续错误次数使用指数退避策略
            long backoffTime = calculateBackoffTime();

            log.error("Error processing messages from topic: {}, consecutive errors: {}, backing off for {} ms",
                    topic, consecutiveErrors, backoffTime, e);

            try {
                TimeUnit.MILLISECONDS.sleep(backoffTime);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                log.warn("Backoff sleep interrupted", ie);
            }
        }

        /**
         * 计算退避时间
         */
        private long calculateBackoffTime() {
            long baseInterval = jmqConfig.getWfcWaiterInfoConsumeInterval();
            // 指数退避，但设置上限
            return Math.min(baseInterval * (1L << Math.min(consecutiveErrors, 10)), 60000L);
        }
    }

    /**
     * 手动重启服务
     */
    public void restart() {
        log.info("Manually restarting WfcWaiterInfo worker");
        destroy();
        afterPropertiesSet();
    }

}