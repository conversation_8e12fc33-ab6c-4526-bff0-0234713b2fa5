package com.jd.kf.oss.performance.app.controller.dto.request.performancetarget;

import com.jd.kf.oss.performance.enums.IndexTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class TargetUpdateRequest {
    /**
     * 主键id
     */
    Long id;

    /**
     *绩效组Id
     */
    @ApiModelProperty("绩效组Id")
    @NotBlank(message = "绩效组Id不能为空")
    private String businessLineId;

    /**
     * 考核方案
     */
    @ApiModelProperty("绩效方案Code")
    private String evaluationPlanCode;


    @ApiModelProperty("绩效方案类型")
    private String type;

    /**
     * 月标准天数
     */
    @ApiModelProperty("月标准天数")
    private String days;

    /**
     * 产能目标
     */
    @ApiModelProperty("产能目标")
    private String cpd;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String price;

    /**
     *目标产能指标
     */
    @ApiModelProperty("目标指标")
    private List<IndexItem> indexes;

    @Data
    public static class IndexItem {
        /**
         * 指标code
         */
        private String kpiCd;

        /**
         * 指标模板  如 质量指标1
         */
        @NotNull(message = "指标模板不能为空")
        private IndexTemplate template;
        /**
         * 开始时间
         */

        private LocalDate startDate;

        /**
         * 结束时间
         */
        private LocalDate endDate;


        /**
         * 样本下限
         */
        private String threshold;
        /**
         * 权重
         */
        private String weight;

    }



}
