package com.jd.kf.oss.performance.app.service.impl;

import com.jd.jsf.gd.util.ConcurrentHashSet;
import com.jd.jsl.base.account.AccountService;
import com.jd.jsl.base.account.dto.GetUserInfoDTO;
import com.jd.jsl.base.account.dto.UserInfoResultDTO;
import com.jd.jsl.base.auth.AuthCheckService;
import com.jd.jsl.base.auth.dto.AuthCheckDTO;
import com.jd.jsl.base.common.Response;
import com.jd.kf.oss.performance.app.service.JslAccountService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.infra.config.LocalConfig;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/4/1 23:27
 * @Description:
 * @Version 1.0
 */
@Service
@Slf4j
public class JslAccountServiceImpl implements JslAccountService {

    @Resource
    private AuthCheckService authCheckService;

    @Resource
    private AccountService accountService;
    @Resource
    private DynamicConfig dynamicConfig;
    @Resource
    private LocalConfig localConfig;

    @Override
    public UserInfoResultDTO getUserInfoByToken(String token) {
        String umpKey = localConfig.getUmpKeyPrefix() + ".rpc.accountService.getUserInfo";
        CallerInfo callerInfo = Profiler.registerInfo(umpKey);
        try {
            GetUserInfoDTO getUserInfoDTO = new GetUserInfoDTO();
            getUserInfoDTO.setToken(token);
            Response<UserInfoResultDTO> response = accountService.getUserInfo(getUserInfoDTO);
            if (response == null || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("accountService-getUserInfoByToken Exception token : {} ", token, e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return null;
    }

    @Override
    public Set<String> getPortalResourceCodeListByToken(String token) {
        String umpKey = localConfig.getUmpKeyPrefix() + ".rpc.authCheckService.getPortalTenantCodeListByToken";
        CallerInfo callerInfo = Profiler.registerInfo(umpKey);
        Set<String> resultSet = new ConcurrentHashSet<>();
        try {
            Set<Integer> tenantResourceTypeSet = dynamicConfig.getPortalTenantResourceType();
            tenantResourceTypeSet.stream().parallel().forEach(resourceType -> {
                Set<String> tenantCodeSet = this.getTenantCodeListByTokenAndType(token, resourceType);
                resultSet.addAll(tenantCodeSet);
            });
        } catch (Exception e) {
            log.error("accountService-getPortalTenantCodeListByToken Exception token : {} ", token, e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return resultSet;
    }

    @Override
    public Set<String> getTenantCodeListByTokenAndType(String token, Integer type) {
        String umpKey = localConfig.getUmpKeyPrefix() + ".rpc.authCheckService.getTenantIdListByErpAndType";
        CallerInfo callerInfo = Profiler.registerInfo(umpKey);
        try {
            AuthCheckDTO authCheckDTO = new AuthCheckDTO();
            authCheckDTO.setToken(token);
            authCheckDTO.setType(type);
            Response<Set<String>> response = authCheckService.getResources(authCheckDTO);
            if (response == null || response.getData() == null) {
                return Collections.emptySet();
            }
            return response.getData();
        } catch (Exception e) {
            log.error("accountService-getTenantCodeListByTokenAndType Exception token={}, type={} ", token, type, e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return Collections.emptySet();
    }

}
