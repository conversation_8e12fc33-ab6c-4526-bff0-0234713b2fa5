package com.jd.kf.oss.performance.app.controller.dto.request.coefficient;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.YearMonth;


@Data
@EqualsAndHashCode(callSuper = true)
public class CoefficientPageReq extends BasePageRequest {
    /**
     * 绩效月，可选，默认当前月
     */
    private YearMonth period;


    /**
     * 系数名称模糊查询，可选
     */
    private String name;

    /**
     * 系数模板类型，可选
     */
    @ApiModelProperty("系数类型：分段系数、月周期系数、常量系数")
    private CoefficientTemplateTypeEnum type;


}
