package com.jd.kf.oss.performance.app.controller.controller;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.BaseImportRequest;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jd.kf.oss.performance.app.controller.converter.EvaluateConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.PageEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.DeleteEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.ExportEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.service.EvaluateService;
import com.jd.kf.oss.performance.app.controller.dto.vo.EvaluateVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDO;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDomainService;
import com.jd.kf.oss.performance.utils.CommonPage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "上级评价管理")
@RestController
@RequestMapping("/performance/evaluate")
public class EvaluateController {

    @Autowired
    private EvaluateDomainService evaluateDomainService;

    @Autowired
    private DynamicConfig dynamicConfig;

    @Autowired
    private EvaluateService evaluateService;

    /**
     * 分页查询上级评价数据
     * @param request 分页查询请求参数，包含绩效月、主管ERP、分页信息
     * @return 分页结果
     */
    @ApiOperation("分页查询上级评价数据")
    @PostMapping("/page")
    public ApiResult<CommonPage<EvaluateVO>> pageEvaluate(@RequestBody @Validated PageEvaluateRequest request) {

        CommonPage<EvaluateDO> commonPage = evaluateDomainService.queryEvaluateByConditions(
                request.getPeriod(),
                request.getManagerErp(),
                request.getPage(),
                request.getPageSize()
        );
        return ApiResult.success(EvaluateConverter.INSTANCE.pageDO2PageVO(commonPage));
    }

    /**
     * 批量删除上级评价
     * @param request 删除请求，包含待删除ID列表
     * @return 操作结果
     */
    @ApiOperation("批量删除上级评价")
    @PostMapping("/delete")
    public ApiResult<Void> deleteEvaluate(@RequestBody @Validated DeleteEvaluateRequest request) {
        evaluateDomainService.deleteEvaluate(request.getEvaluateIds(), request.getPeriod());
        return ApiResult.success();
    }

    /**
     * 导出评价数据
     * @param request 导出请求参数
     * @throws IOException IO异常
     */
    @ApiOperation("导出评价数据")
    @PostMapping("/export")
    public ApiResult<String> exportEvaluate(@RequestBody @Validated ExportEvaluateRequest request,
                               HttpServletRequest httpServletRequest) throws IOException {
        return ApiResult.success(evaluateService.exportEvaluate(request, httpServletRequest));
    }

    /**
     * 导入上级评价数据
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     * @throws IOException IO异常
     */
    @ApiOperation("导入上级评价数据")
    @PostMapping("/import")
    public ApiResult<String> importEvaluate(@RequestBody @Validated BaseImportRequest request,
                                         HttpServletRequest httpServletRequest) throws IOException {
        return evaluateService.importEvaluate(request.getUrl(), httpServletRequest);
    }

    /**
     * 查询无效数据剔除Excel模板
     */
    @PostMapping("/excel")
    public ApiResult<String> queryEvaluateExcelTemplate() {
        return ApiResult.success(dynamicConfig.getEvaluateExcelTemplate());
    }
}
