package com.jd.kf.oss.performance.app.controller.dto.vo.index;

import com.jd.kf.oss.performance.enums.IndexTemplate;
import lombok.Data;

@Data
public class IndexItem {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 一级租户标识
     */
    private String tenantCode;

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 指标类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    /**
     * 指标映射code
     * 如<质量指标1,index_quality_1>
     */
    private IndexTemplate template;

    /**
     * 开始时间
     */

    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 样本下限
     */
    private String threshold;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 权重
     */
    private String weight;

    /**
     * 状态
     */
    private String status;

}
