package com.jd.kf.oss.performance.app.service;

import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDomainService;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class IndexBasicPeriodSyncService {
    @Autowired
    private IndexDomainService indexDomainService;


    public void indexBasicPeriodSyncTaskExec() {
        CallerInfo callerInfo = Profiler.registerInfo("performance-indexBasicPeriodSyncTaskExec");
        try {
            log.info("[indexBasicPeriodSyncTaskExec] 任务开始");

            UserContextHolder.initAndSetUserContext(SystemConstants.TENANT_CODE_RETAIL, SystemConstants.EDITOR_SYSTEM);

            // 查询指标平台推表指标信息
            List<IndexDO> indexDOFromPlatform = indexDomainService.queryAllIndexDOFromOrigin();

            // 查询绩效系统指标
            List<IndexDO> indexDOFromBasic = indexDomainService.queryAllIndexBasic();

            // merge指标
            indexDomainService.syncIndexBasicWithPlatform(indexDOFromPlatform, indexDOFromBasic);
        } catch (Exception e) {
            log.error("indexBasicPeriodSyncTaskExec exception", e);
            Profiler.functionError(callerInfo);
        } finally {
            UserContextHolder.remove();
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
