package com.jd.kf.oss.performance.app.controller.utils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

public class HttpResponseUtil {

    public static final String JSON_SUCCESS_RESPONSE ="{\"data\":null,\"resultCode\":\"0\",\"resultMsg\":\"操作成功\"}";

    public static final String JSON_ERROR_RESPONSE ="{\"data\":null,\"resultCode\":\"2\",\"resultMsg\":\"{errorMsg}\"}";
    public static void JsonSuccessResponse(HttpServletResponse response) throws IOException {
        // 设置响应类型和编码
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        // 获取输出流
        PrintWriter out = response.getWriter();
        // 写入JSON数据
        out.print(JSON_SUCCESS_RESPONSE);
        out.flush();
        out.close();
    }

}
