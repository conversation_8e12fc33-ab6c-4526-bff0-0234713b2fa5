package com.jd.kf.oss.performance.app.service;


import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusinessLinePeriodSyncService {
    @Autowired
    private BusinessLineDomainService businessLineDomainService;

    @Autowired
    private PerformanceTargetDomainService targetDomainService;

    public void businessLinePeriodSyncTask() {
        CallerInfo callerInfo = Profiler.registerInfo("performance-businessLinePeriodSyncTask");
        try {
            log.info("[businessLinePeriodSyncTask] 任务开始");

            UserContextHolder.initAndSetUserContext(SystemConstants.TENANT_CODE_RETAIL, SystemConstants.EDITOR_SYSTEM);

            // 获取当前绩效月
            String currentPeriod = DateUtils.getCurrentPerformancePeriod();

            // 1. 查询当前绩效月的PerformanceTarget数据
            List<PerformanceTargetDO> currentTargets = targetDomainService.queryAllTargetByPeriod(currentPeriod);

            // 2. 查询所有BusinessLine数据
            List<BusinessLineDO> businessLines = businessLineDomainService.queryAllBusinessLine();

            if (businessLines == null || businessLines.isEmpty()) {
                log.info("BusinessLine表中无数据，无需同步");
                return;
            }

            // 3. 找出需要插入的绩效组（BusinessLine中有但PerformanceTarget中没有的）
            List<BusinessLineDO> missingBusinessLines = findMissingBusinessLines(businessLines, currentTargets);

            if (missingBusinessLines != null && !missingBusinessLines.isEmpty()) {
                // 将缺失的BusinessLine数据插入到PerformanceTarget，并设置当前绩效月
                copyBusinessLineToTarget(missingBusinessLines, currentPeriod);
                log.info("已从BusinessLine插入 " + missingBusinessLines.size() + " 条缺失数据到绩效月 " + currentPeriod);
            } else {
                log.info("当前绩效月 " + currentPeriod + " 已包含所有BusinessLine数据，无需同步");
            }

            log.info("绩效组同步任务完成");
        } catch (Exception e) {
            log.error("businessLinePeriodSyncTask exception", e);
            Profiler.functionError(callerInfo);
        } finally {
            UserContextHolder.remove();
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private List<BusinessLineDO> findMissingBusinessLines(List<BusinessLineDO> businessLines, List<PerformanceTargetDO> targets) {
        Set<String> existingBusinessLineIds = (targets == null || targets.isEmpty()) ? Collections.emptySet() :
                targets.stream().map(PerformanceTargetDO::getBusinessLineId).collect(Collectors.toSet());
        return businessLines.stream()
                .filter(b -> !existingBusinessLineIds.contains(b.getBusinessLineId()))
                .collect(Collectors.toList());
    }

    private void copyBusinessLineToTarget(List<BusinessLineDO> businessLines, String period) {
        List<PerformanceTargetDO> targets = businessLines.stream().map(b -> {
            PerformanceTargetDO t = new PerformanceTargetDO();
            t.setTenantCode(b.getTenantCode());
            t.setBusinessLineId(b.getBusinessLineId());
            t.setBusinessLineName(b.getName());
            t.setPeriod(period);
            t.setCreator(b.getCreator());
            t.setEditor(b.getEditor());
            return t;
        }).collect(Collectors.toList());
        targetDomainService.saveBatchTargets(targets);
    }
}
