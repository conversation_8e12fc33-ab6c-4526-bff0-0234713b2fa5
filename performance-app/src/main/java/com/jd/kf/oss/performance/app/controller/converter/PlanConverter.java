package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.plan.PlanSaveRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.plan.PlanVO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface PlanConverter {
    PlanConverter INSTANCE = Mappers.getMapper(PlanConverter.class);

    /**
     * 将DO转化为VO
     * @param factorDO
     * @return
     */
    PlanVO do2VO(PlanDO factorDO);

    /**
     * 将前端提交的保存对象转化为DO
     * @param request
     * @return
     */
    PlanDO do2DO(PlanSaveRequest request);
}
