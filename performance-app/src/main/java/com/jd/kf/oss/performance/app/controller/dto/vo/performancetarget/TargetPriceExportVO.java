package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TargetPriceExportVO {
    /**
     * 绩效月
     */
    @ExcelProperty(value = "绩效月", index = 0)
    private String period;

    /**
     * 绩效组id
     */
    @ExcelProperty(value = "绩效组ID", index = 1)
    private String businessLineId;

    /**
     * 绩效组名称
     */
    @ExcelProperty(value = "绩效组名称", index = 2)
    private String businessLineName;

    /**
     * 考核方案
     */
    @ExcelProperty(value = "关联绩效方案", index = 3)
    private String evaluationPlan;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价(元)", index =4)
    private String price;
    /**
     * 月标准天数
     */
    @ExcelProperty(value = "月标准天数", index = 5)
    private String days;


}