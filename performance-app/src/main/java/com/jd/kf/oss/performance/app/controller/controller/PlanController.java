package com.jd.kf.oss.performance.app.controller.controller;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.app.controller.converter.PlanConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.plan.*;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.plan.PlanVO;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.PlanAggregateService;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.domain.runtime.aggregate.PerformanceTaskAggregateService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Api(tags = "绩效方案管理")
@RestController
@RequestMapping("/performance/plan")
@Slf4j
public class PlanController {
    @Resource
    private PlanDomainService planDomainService;

    @Resource
    private PlanAggregateService planAggregateService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;

    @Resource
    private DynamicConfig dynamicConfig;

    /**
     * 查询绩效方案列表
     * @param request 查询请求参数
     * @return 绩效因子查询结果
     */
    @ApiOperation("查询绩效方案")
    @PostMapping("/list")
    public ApiResult<CommonPage<PlanVO>> list(@RequestBody @Validated PlanRequest request) {
        CommonPage<PlanDO> factorDOCommonPage = planDomainService.queryPlans(
                UserContextHolder.getTenantCode(),
                DateUtils.getCurrentPerformancePeriod(),
                request.getName(),
                request.getType(),
                request.getPageNum(),
                request.getPageSize()
        );
        CommonPage<PlanVO> result = new CommonPage<>();
        result.setSize(factorDOCommonPage.getSize());
        result.setPage(factorDOCommonPage.getPage());
        result.setTotal(factorDOCommonPage.getTotal());
        if (CollectionUtils.isNotEmpty(factorDOCommonPage.getData())) {
            result.setData(
                    factorDOCommonPage.getData().stream().map(PlanConverter.INSTANCE::do2VO)
                            .collect(Collectors.toList())
            );
        }
        return ApiResult.success(result);
    }

    /**
     * 保存绩效方案
     * @param request 创建绩效组请求对象，包含绩效组相关信息
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("保存绩效方案")
    @PostMapping("/save")
    public ApiResult<Long> save(@RequestBody @Validated PlanSaveRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        PlanDO planDO = PlanConverter.INSTANCE.do2DO(request);
        planDO.setTenantCode(UserContextHolder.getTenantCode());
        Long result = planDO.save();
        return ApiResult.success(result);
    }

    /**
     * 校验绩效方案成环
     * @param request 创建绩效组请求对象，包含绩效组相关信息
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("校验绩效方案成环")
    @PostMapping("/checkCircle")
    public ApiResult<Boolean> checkCircle(@RequestBody @Validated PlanSaveRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        PlanDO planDO = PlanConverter.INSTANCE.do2DO(request);
        planDO.setTenantCode(UserContextHolder.getTenantCode());
        Boolean result = null;
        try {
            result = planAggregateService.checkCircle(planDO);
        } catch (Exception e) {
            return ApiResult.error(e.getMessage());
        }
        return ApiResult.success(result);
    }

    /**
     * 取消关联绩效组
     */
    @PostMapping("/delete")
    public ApiResult<Boolean> delete(@RequestBody PlanDeleteRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        String period = DateUtils.getCurrentPerformancePeriod();
        if (performanceTargetDomainService.weatherAnyBusinessLineAssociatedThisPlan(period, request.getPlanCode())) {
            return ApiResult.error("改方案被引用，不能删除");
        }
        PlanDO planDO = new PlanDO(UserContextHolder.getTenantCode(), period, request.getPlanCode());
        return ApiResult.success(planDO.delete());
    }

    /**
     * 关联绩效组
     */
    @PostMapping("/associateBusinessLines")
    @ApiOperation("关联绩效组")
    public ApiResult<Void> associateBusinessLines(@RequestBody @Validated PlanAssociateRequest request) {
        associateBusinessLineAmountLimitCheck(request.getBusinessLinedIds());
        String period=DateUtils.getCurrentPerformancePeriod();
        planAggregateService.associateBusinessLines(request.getBusinessLinedIds(),period,request.getPlanCode());
        return ApiResult.success();
    }

    /**
     * 取消关联绩效组
     */
    @PostMapping("/disassociateBusinessLines")
    public ApiResult<Void> disassociateBusinessLines(@RequestBody @Validated PlanAssociateRequest request) {
        String period=DateUtils.getCurrentPerformancePeriod();
        planAggregateService.disassociateBusinessLines(request.getBusinessLinedIds(),period,request.getPlanCode());
        return ApiResult.success();
    }

    @Resource
    private BusinessLineDomainService businessLineDomainService;

    @GetMapping("/test")
    public ApiResult<Void> test(String period) {
        if (StringUtils.isBlank(period)) {
            period = DateUtils.getCurrentPerformancePeriod();
        }
        List<BusinessLineDO> businessLines = businessLineDomainService.queryAllBusinessLine();
        PerformanceTaskDO performanceTaskDO = new PerformanceTaskDO();
        performanceTaskDO.create(UserContextHolder.getTenantCode(), period, businessLines);
        return ApiResult.success();
    }


    @Resource
    private PerformanceTaskAggregateService performanceTaskAggregateService;
    @GetMapping("/testCalc")
    public ApiResult<Void> testCalc(String period) {
        if (StringUtils.isBlank(period)) {
            period = DateUtils.getCurrentPerformancePeriod();
        }
        String tenantCode = "kf_perform_retail";
        PerformanceTaskDO performanceTaskDO = new PerformanceTaskDO();
        performanceTaskDO = performanceTaskDO.load(tenantCode, period);
        performanceTaskAggregateService.calc(performanceTaskDO);
        return ApiResult.success();
    }
    /**
     * 根据方案解析需要的指标模板
     */
    @PostMapping("/analysisIndexTemplates")
    public ApiResult<List<String>> analysisIndexTemplatesOfPlan(@RequestBody PlanAnalysisIndexRequest request) {
        Set<String> indexTemplateNames = planAggregateService.analysisIndexTemplateNames(request.getFormula());
        return ApiResult.success(getSortedTemplateNames(indexTemplateNames));
    }

    private void associateBusinessLineAmountLimitCheck(List<String> businessLineIds) {
        int size=businessLineIds.size();
        int limit=dynamicConfig.getPlanAssociateBusinessLineMaxAmount();
        if(size>limit){
            throw new BizException("一次最多只能关联"+limit+"个绩效组");
        }
    }

    private List<String> getSortedTemplateNames(Set<String> templateNames) {
        if (CollectionUtils.isEmpty(templateNames)) {
            return new ArrayList<>();
        }
        List<String> templateNamesList = new ArrayList<>(templateNames);
        templateNamesList.sort(String::compareTo);
        return templateNamesList;
    }


}
