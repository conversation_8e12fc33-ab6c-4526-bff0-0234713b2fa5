package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.kf.oss.performance.app.controller.dto.vo.index.IndexItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class TargetDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("自增主键")
    private Long id;

    /**
     * 绩效月
     */
    @ApiModelProperty("绩效月")
    private String period;

    /**
     *绩效组Id
     */
    @ApiModelProperty("绩效组Id")
    private String businessLineId;

    /**
     *绩效组名称
     */
    @ApiModelProperty("绩效组名称")
    private String businessLineName;


    /**
     * 月标准天数
     */
    @ApiModelProperty("月标准天数")
    private String days;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道")
    private String channel;

    /**
     * 考核方案类型
     */
    @ApiModelProperty("考核方案类型")
    private String evaluationType;

    /**
     * 考核方案
     */
    @ApiModelProperty("考核方案")
    private String evaluationPlan;


    /**
     * 考核方案
     */
    @ApiModelProperty("考核方案Code")
    private String evaluationPlanCode;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String price;

    /**
     * 产能目标
     */
    @ApiModelProperty("产能目标")
    private String cpd;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String creator;

    /**
     * 修改者
     */
    @ApiModelProperty("修改者")
    private String editor;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;

    /**
     *目标产能指标
     */
    @ApiModelProperty("目标指标")
    private List<IndexItem>  indexes;




}
