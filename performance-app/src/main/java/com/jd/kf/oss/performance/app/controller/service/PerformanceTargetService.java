package com.jd.kf.oss.performance.app.controller.service;

import com.alibaba.excel.EasyExcel;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.app.controller.converter.TargetConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.*;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget.*;
import com.jd.kf.oss.performance.app.controller.utils.EasyExcelBatchImportListener;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.TargetAggregateService;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

@Service
@Slf4j
public class PerformanceTargetService {

    @Resource
    private TargetAggregateService targetAggregateService;

    @Resource
    private PerformanceTargetDomainService targetDomainService;

    @Resource
    private FileHelperService fileHelperService;

    public ApiResult<String> importCPD( BaseImportRequest request,
                                       HttpServletRequest httpServletRequest) throws IOException {
        logImportRequestInfo("导入绩效目标CPD数据", request.getUrl());
        File excelFile = fileHelperService.downloadFile(request.getUrl());
        try {
            fileHelperService.checkExcelFileRowCount(excelFile);
            return importCPD(excelFile, httpServletRequest);
        }catch (Exception e){
            log.error("批量导入绩效目标CPD失败{}",e.getMessage());
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 导入CPD数据
     */
    public ApiResult<String> importCPD(File excelFile,
                                       HttpServletRequest httpServletRequest) {
        Consumer<ExcelCheckContext<PerformanceTargetDO>> checkFunction = targetAggregateService::checkForImportedTargetCPD;
        Consumer<List<PerformanceTargetDO>> saveFunction = targetDomainService::importTargetCPD;
        Function<LinkedHashMap<Integer, ImportCPDVO>, LinkedHashMap<Integer, PerformanceTargetDO>> convertFunction =
                TargetConverter.INSTANCE::importCPDVoMap2TargetDOMap;
        EasyExcelBatchImportListener<PerformanceTargetDO, ImportCPDVO> importHelper =
                new EasyExcelBatchImportListener<>(saveFunction, ImportCPDVO.class, checkFunction, convertFunction);
        EasyExcel.read(excelFile, importHelper).sheet().doRead();
        if (CollectionUtils.isEmpty(importHelper.getErrorMessages())) {
            return ApiResult.success();
        }
        String url = fileHelperService.uploadToOSS(httpServletRequest, "CPD导入失败条目", importHelper.buildErrorSheet());
        log.error("部分绩效目标CPD数据导入失败，回写文件地址: {}", url);
        return ApiResult.error(url);
    }


    /**
     * 批量导入价格和天数数据
     * @param request 基础导入请求对象，包含文件下载URL
     */
    public ApiResult<String> importPriceAndDays(BaseImportRequest request, HttpServletRequest httpServletRequest){
        logImportRequestInfo("导入价格和天数数据", request.getUrl());
        File excelFile = fileHelperService.downloadFile(request.getUrl());
        try {
            fileHelperService.checkExcelFileRowCount(excelFile);
            return importPriceAndDays(excelFile, httpServletRequest);
        }catch (Exception e){
            log.error("批量导入价格和天数数据失败{}",e.getMessage());
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 导入单价和天数
     */

    public ApiResult<String> importPriceAndDays(File excelFile, HttpServletRequest httpServletRequest) {
        Consumer<ExcelCheckContext<PerformanceTargetDO>> checkFunction = targetAggregateService::checkForImportedTargetPriceAndDays;
        Consumer<List<PerformanceTargetDO>> saveFunction = targetDomainService::importTargetPriceAndDays;
        Function<LinkedHashMap<Integer, ImportPriceAndDaysVO>, LinkedHashMap<Integer, PerformanceTargetDO>> convertFunction =
                TargetConverter.INSTANCE::importPriceAndDaysVoMap2TargetDOMap;
        EasyExcelBatchImportListener<PerformanceTargetDO, ImportPriceAndDaysVO> importer =
                new EasyExcelBatchImportListener<>(saveFunction, ImportPriceAndDaysVO.class, checkFunction, convertFunction);
        EasyExcel.read(excelFile, importer).sheet().doRead();
        if (CollectionUtils.isEmpty(importer.getErrorMessages())) {
            return ApiResult.success();
        }
        String url = fileHelperService.uploadToOSS(httpServletRequest, "单价以及天数导入失败条目", importer.buildErrorSheet());
        log.error("部分价格和天数数据导入失败，回写文件地址: {}",url);
        return ApiResult.error(url);

    }

    /**
     * 从指定URL下载Excel文件并导入绩效指标数据
     */
    public ApiResult<String> importTargetAndIndex(BaseImportRequest request,
                                                  HttpServletRequest httpServletRequest){
        logImportRequestInfo("导入绩效指标数据", request.getUrl());
        File excelFile = fileHelperService.downloadFile(request.getUrl());
        try {
            fileHelperService.checkExcelFileRowCount(excelFile);
            return importTargetAndIndex(excelFile, httpServletRequest);
        }catch (Exception e){
            log.error("批量导入绩效指标时失败{}",e.getMessage());
            throw new BizException(e.getMessage());
        }
    }


    /**
     * 导入调整指标数据
     */
    public ApiResult<String> importTargetAndIndex(File excelFile,
                                                  HttpServletRequest httpServletRequest) {
        AggTargetExcelUtil aggTargetExportSheetExtract = new AggTargetExcelUtil();
        // 将 excel 行数据转为VO对象
        BiFunction<Map<Integer, String>, Map<Integer, String>, TargetComposite> mapToVOFunction =
                aggTargetExportSheetExtract::parseTargetComposite;
        // 批量校验方法
        Consumer<ExcelCheckContext<TargetComposite>> checkFunction = targetAggregateService::checkForImportedPlanAndIndex;
        // 批量导入方法
        Consumer<List<TargetComposite>> consumerFunction = targetAggregateService::updateImportIndexes;
        // vo转为do的方法
        Function<LinkedHashMap<Integer, TargetComposite>, LinkedHashMap<Integer, TargetComposite>> convertFunction =
                Function.identity();
        // 构建excel导入监听器
        EasyExcelBatchImportListener<TargetComposite, TargetComposite> importer =
                new EasyExcelBatchImportListener<>(consumerFunction, checkFunction, mapToVOFunction, convertFunction);
        EasyExcel.read(excelFile, importer).sheet().doRead();
        if (CollectionUtils.isEmpty(importer.getErrorMessages())) {
            return ApiResult.success();
        }
        String url = fileHelperService.uploadToOSS(httpServletRequest, "绩效方案导入失败条目", importer.buildErrorSheet());
        log.error("部分绩效指标导入失败，回写文件地址: {}", url);
        return ApiResult.error(url);

    }

    /**
     * 导出CPD数据
     */
    public String exportCPD(ExportCPDRequest request, HttpServletRequest httpServletRequest,
                            HttpServletResponse httpServletResponse) {
        String period = request.getPeriod().toString();
        List<PerformanceTargetDO> performanceTargetDOS = targetDomainService
                .queryTargetListByBusinessLineCodeAndPeriod(period, request.getBusinessLineIds());
        fileHelperService.checkExportAmountLimit(performanceTargetDOS);
        List<TargetCPDExportVO> vos = TargetConverter.INSTANCE.do2CPDVO(performanceTargetDOS);
        return fileHelperService.uploadToOSS(httpServletRequest, request.getPeriod() + "-CPD数据",
                TargetCPDExportVO.class, vos);
    }

    public String exportPrice(ExportPriceRequest request, HttpServletRequest httpServletRequest,
                              HttpServletResponse httpServletResponse) {
        String period = request.getPeriod().toString();
        List<PerformanceTargetDO> performanceTargetDOS = targetDomainService
                .queryTargetListByBusinessLineCodeAndPeriod(period, request.getBusinessLineIds());
        fileHelperService.checkExportAmountLimit(performanceTargetDOS);
        List<TargetPriceExportVO> targetExportVoList = TargetConverter.INSTANCE.DO2PriceVO(performanceTargetDOS);
        String ossFileName = request.getPeriod() + "-单价和标准天数数据";
        return fileHelperService.uploadToOSS(httpServletRequest, ossFileName, TargetPriceExportVO.class, targetExportVoList);
    }

    /**
     * 导出绩效目标和指标
     *
     * @param request
     */

    public String exportTargetAndIndex(ExportTargetAndIndexRequest request,
                                       HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        String period = request.getPeriod().toString();
        List<TargetComposite> targetComposites = targetAggregateService
                .listAggTargetByBusinessCodeAndPeriod(request.getBusinessLineIds(), period);
        fileHelperService.checkExportAmountLimit(targetComposites);
        List<AggTargetExportVO> vos = TargetConverter.INSTANCE.targetComposite2VO(targetComposites);
        AggTargetExcelUtil singleSheet = AggTargetExcelUtil.generateSingleSheet(vos);
        String fileName = request.getPeriod() + "-绩效方案关联的指标数据";
        return fileHelperService.uploadToOSS(httpServletRequest, fileName, singleSheet);
    }

    /**
     * 更新绩效目标
     *
     * @param request
     */
    public void updateTargetOfBusinessLine(TargetUpdateRequest request) {
        PerformanceTargetDO targetDO = TargetConverter.INSTANCE.updateRequest2TargetDO(request);
        // 只能更新当前绩效月的绩效目标
        targetDO.setPeriod(DateUtils.getCurrentPerformancePeriod());
        TargetComposite targetComposite = new TargetComposite();
        targetComposite.setTargetDO(targetDO);
        targetComposite.setIndexes(TargetConverter.INSTANCE.updateRequest2IndexDO(request.getIndexes()));
        targetAggregateService.updateTargetComposite(targetComposite);
    }

    private static void logImportRequestInfo(String importType,String url) {
        String tenantCode = UserContextHolder.getTenantCode();
        String operator = UserContextHolder.getOperator();
        log.info("租户{} 操作人{}开始批量{}，导入excel的URL: {}",tenantCode,operator,importType,url);
    }



}
