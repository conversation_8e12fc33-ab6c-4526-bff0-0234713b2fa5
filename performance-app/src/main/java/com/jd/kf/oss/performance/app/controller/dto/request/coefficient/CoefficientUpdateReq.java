package com.jd.kf.oss.performance.app.controller.dto.request.coefficient;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
@Data
public class CoefficientUpdateReq {


    @NotBlank(message = "系数code不能为空")
    private String code;

    /**
     * 系数名称
     */
    private String name;

    /**
     * 系数描述
     */
    private String description;

    /**
     * 系数项列表
     */
    private List<CoefficientItem> coefficientItems;

    /**
     * 状态
     */
    private String status;

    @Data
    public static class CoefficientItem {
        /**
         * 左边界
         */
        private String leftEndpoint;

        /**
         * 右边界
         */
        private String rightEndpoint;

        /**
         * 系数值
         */
        private String coefficientNum;

        /**
         * 状态
         */
        private String status;
    }
}
