package com.jd.kf.oss.performance.app.controller.dto.request.userinfo;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 员工信息分页查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserInfoPageRequest extends BasePageRequest {

    /**
     * 绩效月
     */
    @ApiModelProperty("绩效月")
    @NotBlank(message = "绩效月不能为空")
    private String period;

    /**
     * 主管erp
     */
    @ApiModelProperty("主管erp")
    private String managerErp;

    /**
     * 客服erp
     */
    @ApiModelProperty("客服erp")
    private String erp;

    /**
     * 客服姓名
     */
    @ApiModelProperty("客服姓名")
    private String name;

    /**
     * 部门ID
     */
    @ApiModelProperty("部门ID")
    private String deptId;


    /**
     * BusinessLine名称（关联绩效方案）
     */
    @ApiModelProperty("BusinessLine名称")
    private String businessLineName;

    /**
     * BusinessLine名称（关联绩效方案）
     */
    @ApiModelProperty("BusinessLineId")
    private String businessLineId;

    /**
     * 绩效方案名称(绩效目标中的)
     */
    @ApiModelProperty("绩效方案Code")
    private String planCode;
}
