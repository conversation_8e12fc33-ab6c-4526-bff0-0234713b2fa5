package com.jd.kf.oss.performance.app.controller.controller;

import com.alibaba.fastjson.JSON;
import com.jd.kf.oss.performance.app.controller.converter.BusinessLineConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.BusinessLinePageRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.CreateBusinessLineRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.UpdateBusinessLineRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.businessLine.BusinessLinePageVO;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.BusinessLineAggregateService;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLineComposite;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/06/24
 * 绩效组(绩效条线)管理 http 接口
 */
@Api("绩效组管理")
@RestController
@RequestMapping("/performance/businessLine")
@Slf4j
public class BusinessLineController {
    @Resource
    private BusinessLineAggregateService businessLineAggregateService;

    /**
     * 查询绩效组列表
     * @param request 包含分页参数和查询条件的请求对象，需通过校验
     * @return 包含分页绩效组数据的通用分页结果对象
     */
    @ApiOperation("查询绩效组列表")
    @PostMapping("/page")
    public ApiResult<CommonPage<BusinessLinePageVO>> businessLinePageList(@RequestBody @Validated BusinessLinePageRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-businessLinePageList");
        try {
            // 参数校验
            CheckUtil.notNull(request, "请求体不能为空");
            // 使用字段参数方式调用DomainService
            CommonPage<BusinessLineComposite> commonPage = businessLineAggregateService.queryBusinessLineWithPlanInfo(
                    UserContextHolder.getTenantCode(),
                    request.getEditor(),
                    request.getName(),
                    request.getPlanCode(),
                    request.getPage(),
                    request.getPageSize()
            );
            return ApiResult.success(BusinessLineConverter.INSTANCE.pageComposite2PageVO(commonPage));
        } catch (Exception e) {
            log.error("businessLinePageList exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return ApiResult.illegal(e.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    /**
     * 新建绩效组
     * @param request 创建绩效组请求对象，包含绩效组相关信息
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("新建绩效组")
    @PostMapping("/create")
    public ApiResult<Void> createBusinessLine(@RequestBody @Validated CreateBusinessLineRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-createBusinessLine");
        try {
            // 参数校验
            CheckUtil.notNull(request, "请求体不能为空");
            BusinessLineDO businessLineDO = BusinessLineConverter.INSTANCE.request2DO(request);
            boolean success = businessLineAggregateService.saveBusinessLineAndCreateTarget(businessLineDO);
            return success ? ApiResult.success() : ApiResult.error(ResultCodeEnum.ERROR);
        }catch (Exception e) {
            log.error("createBusinessLine exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return ApiResult.illegal(e.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    /**
     * 编辑绩效组信息
     * @param request 更新绩效组请求参数，包含需要更新的绩效组信息
     * @return 返回操作结果，成功返回成功信息，失败返回错误信息
     */
    @ApiOperation("编辑绩效组")
    @PostMapping("/update")
    public ApiResult<Void> updateBusinessLine(@RequestBody @Validated UpdateBusinessLineRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-updateBusinessLine");
        try {
            BusinessLineDO businessLineDO = BusinessLineConverter.INSTANCE.request2DO(request);
            boolean success = businessLineDO.update();
            return success ? ApiResult.success() : ApiResult.error(ResultCodeEnum.ERROR);
        }catch (Exception e) {
            log.error("updateBusinessLine exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return ApiResult.illegal(e.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
