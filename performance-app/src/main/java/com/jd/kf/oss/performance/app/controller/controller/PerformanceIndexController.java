package com.jd.kf.oss.performance.app.controller.controller;


import com.alibaba.fastjson.JSON;
import com.jd.kf.oss.performance.app.controller.converter.IndexConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.index.IndexBasicPageRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.index.IndexPageBasicVO;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDomainService;
import com.jd.kf.oss.performance.enums.IndexConstant;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/24
 * 绩效指标相关http接口
 */
@Api(tags = "绩效指标管理")
@RestController
@RequestMapping("/performance/index")
@Slf4j
public class PerformanceIndexController {

    @Resource
    private IndexDomainService indexDomainService;

    /**
     * 查询指标列表
     * @param request 指标基础分页请求参数，包含分页信息和查询条件
     * @return 包含分页指标基础信息的API结果，其中分页数据已转换为VO对象
     */
    @ApiOperation("查询指标列表")
    @PostMapping("/page")
    public ApiResult<CommonPage<IndexPageBasicVO>> indexPageList(@RequestBody @Validated IndexBasicPageRequest request) {
            CallerInfo callerInfo = Profiler.registerInfo("performance-indexPageList");
        try {
            CheckUtil.notNull(request, "请求体不能为null");

            String statusStr = request.getStatus() != null ? request.getStatus().getStatus() : null;
            CommonPage<IndexDO> commonPage = indexDomainService.queryIndexByConditions(
                    UserContextHolder.getTenantCode(),
                    request.getKpiCd(),
                    request.getKpiName(),
                    statusStr,
                    request.getPage(),
                    request.getPageSize()
            );
            return ApiResult.success(IndexConverter.INSTANCE.pageDO2PageVO(commonPage));
        } catch (Exception e) {
            log.error("indexPageList exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return ApiResult.illegal(e.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @ApiOperation("查询指标列表")
    @GetMapping("/sysIndex")
    public ApiResult<List<IndexPageBasicVO>> sysIndex() {
    List<IndexPageBasicVO> indexPageBasicVOS = Lists.newArrayList();
        for (IndexTemplate value : IndexTemplate.values()) {
            IndexPageBasicVO vo = new IndexPageBasicVO();
            vo.setKpiCd(value.getCode());
            vo.setKpiName(value.getTemplateName());
            vo.setDescription(value.getType());
            indexPageBasicVOS.add(vo);
        }
        for (IndexConstant value : IndexConstant.values()) {
            IndexPageBasicVO vo = new IndexPageBasicVO();
            vo.setKpiCd(value.getCode());
            vo.setKpiName(value.getTemplateName());
            vo.setDescription(value.getType());
            indexPageBasicVOS.add(vo);
        }
        return ApiResult.success(indexPageBasicVOS);
    }
}
