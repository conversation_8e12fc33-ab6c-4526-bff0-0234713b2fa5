package com.jd.kf.oss.performance.app.controller.dto.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.time.LocalDateTime;

/**
 * 归属数据修改VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("归属数据修改VO")
@Data
public class AppealModifyVO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 客服ERP */
    @ApiModelProperty(value = "客服ERP", example = "customer001")
    private String erp;

    /** 单号 */
    @ApiModelProperty(value = "单号", example = "271595782")
    private String ticketId;

    /** 技能ID */
    @ApiModelProperty(value = "技能ID", example = "10307907")
    private String skillId;

    /** 指标名称 */
    @ApiModelProperty(value = "指标名称", example = "事件满意度")
    private String kpiName;

    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", example = "2025-07")
    private String period;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "2025-07-02 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime created;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-07-02 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;

    /** 创建时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-07-02 10:00:00")
    private String editor;
}
