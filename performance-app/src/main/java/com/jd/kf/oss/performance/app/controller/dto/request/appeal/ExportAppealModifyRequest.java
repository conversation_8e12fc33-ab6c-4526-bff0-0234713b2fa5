package com.jd.kf.oss.performance.app.controller.dto.request.appeal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 导出归属数据修改请求参数
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("导出归属数据修改请求参数")
@Data
public class ExportAppealModifyRequest {

    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", required = true, example = "2025-07")
    @NotBlank(message = "绩效月不能为空")
    private String period;

    /** 指标名称 */
    @ApiModelProperty(value = "指标名称", example = "事件满意度")
    private String kpiName;

    /** 技能ID */
    @ApiModelProperty(value = "技能ID", example = "10307907")
    private String skillId;

    /** 单号 */
    @ApiModelProperty(value = "单号", example = "271595782")
    private String ticketId;
}
