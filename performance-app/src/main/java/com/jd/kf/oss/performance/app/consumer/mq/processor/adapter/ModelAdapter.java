package com.jd.kf.oss.performance.app.consumer.mq.processor.adapter;

import java.time.LocalDateTime;

/**
 * @author: songweijia1
 * @description: 业务模型适配器接口
 * @date: 2025/7/2
 */

public interface ModelAdapter<T> {

    /**
     * 获取唯一标识符
     * @param model 模型对象
     * @return 模型的唯一标识符
     */
    Long getId(T model);

    /**
     * 设置模型的ID
     * @param model 模型对象
     * @param id ID值
     */
    void setId(T model, Long id);

    /**
     * 获取业务主键
     * @param model 模型对象
     * @return 业务主键
     */
    String getBusinessKey(T model);

    /**
     * 获取指定模型的最后修改日期。
     * @param model 要获取其最后修改日期的模型对象。
     * @return 指定模型的最后修改日期。
     */
    LocalDateTime getModified(T model);

    /**
     * 获取模型的 yn 属性值。
     * @param model 模型对象
     * @return yn 属性的值
     */
    Boolean getYn(T model);

    /**
     * 设置模型的 yn 属性。
     * @param model 模型对象
     * @param yn yn 属性的值
     */
    void setYn(T model, Boolean yn);

    /**
     * 设置模型的唯一索引标识符，当有效时，该字段为0，删除时为主键id的值
     * @param model 模型对象
     * @param uniqId 唯一索引标识符
     */
    void setUniqId(T model, Long uniqId);

}
