package com.jd.kf.oss.performance.app.consumer.mq.processor;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.ModelAdapter;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.WfcDeptAdapter;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.enums.SyncTableEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import com.jd.kf.oss.performance.utils.MapHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: songweijia1
 * @description: 零售cchome部门主表Binlog消息处理
 * @date: 2025/7/4
 */

@Slf4j
@Component
public class AuthDeptProcessor extends SingleTableProcessor<WfcDeptPO> {
    
    @Resource
    private IWfcDeptPOService iWfcDeptPOService;
    
    @Resource
    private WfcDeptAdapter wfcDeptAdapter;

    @Override
    protected ModelAdapter<WfcDeptPO> getModelAdapter() {
        return wfcDeptAdapter;
    }

    @Override
    protected WfcDeptPO convertToModelData(WaveEntry.EventType eventType, Map<String, String> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            log.warn("[{}] Data map is null or empty for event type: {}", getSyncTable(), eventType);
            return null;
        }

        try {
            switch (eventType) {
                case INSERT:
                case UPDATE:
                    return convertToWfcDeptPO(dataMap, false);
                case DELETE:
                    return convertToWfcDeptPO(dataMap, true);
                default:
                    log.warn("[{}] Unhandled event type: {}", getSyncTable(), eventType);
                    return null;
            }
        } catch (Exception e) {
            log.error("[{}] Failed to convert data for event type: {}, error: {}",
                    getSyncTable(), eventType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    protected Map<String, WfcDeptPO> getExistingDataMap(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            return Collections.emptyMap();
        }
        return iWfcDeptPOService.getWfcDeptListByDeptIds(businessKeys)
                .stream()
                .collect(Collectors.toMap(WfcDeptPO::getDeptId, Function.identity()));
    }

    @Override
    protected String getSyncTable() {
        return SyncTableEnum.TABLE_AUTH_DEPT.getTableName();
    }

    @Override
    protected boolean shouldPersist() {
        return taskDynamicConfig.isPersistentWfcWaiterInfo();
    }

    @Override
    protected void persistData(List<WfcDeptPO> toUpsertData) {
        if (CollectionUtils.isEmpty(toUpsertData)) {
            log.info("[{}] No data to persist", getSyncTable());
            return;
        }

        try {
            iWfcDeptPOService.batchUpsertDept(toUpsertData);
        } catch (Exception e) {
            log.error("[{}] Error persisting data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("持久化部门数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void logisticRemoveData(List<WfcDeptPO> toDelete) {
        if (CollectionUtils.isEmpty(toDelete)) {
            log.debug("[{}] No data to logically delete", getSyncTable());
            return;
        }

        try {
            iWfcDeptPOService.batchRemoveDeptById(toDelete);
        } catch (Exception e) {
            log.error("[{}] Error logically deleting data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("逻辑删除部门数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 统一的数据转换方法
     * @param dataMap 原始数据映射
     * @param isDelete 是否为删除操作
     * @return 转换后的WfcDeptPO对象
     */
    private WfcDeptPO convertToWfcDeptPO(Map<String, String> dataMap, boolean isDelete) {
        // 数据校验
        String deptId = MapHelper.getMapString(dataMap, "id");
        if (StringUtils.isBlank(deptId)) {
            log.warn("[{}] primary business ID is blank, skipping conversion，data: {}", getSyncTable(), JSON.toJSONString(dataMap));
            return null;
        }

        String tenantCode = UserContextHolder.getTenantCode();
        WfcDeptPO wfcDept = new WfcDeptPO();

        // 设置业务字段
        setBusinessFields(wfcDept, dataMap);

        // 设置系统字段
        setSystemFields(wfcDept, dataMap, tenantCode, isDelete);

        // 设置审计字段
        setAuditFields(wfcDept, dataMap);

        return wfcDept;
    }

    /**
     * 设置业务相关字段
     */
    private void setBusinessFields(WfcDeptPO wfcDept, Map<String, String> dataMap) {
        wfcDept.setDeptId(MapHelper.getMapString(dataMap, "id"));
        wfcDept.setDeptName(MapHelper.getMapString(dataMap, "dept_name"));
        wfcDept.setDeptCode(MapHelper.getMapString(dataMap, "dept_code"));
        wfcDept.setDeptLevel(MapHelper.getMapInt(dataMap, "dept_level"));
        wfcDept.setDeptPath(MapHelper.getMapString(dataMap, "dept_path"));
        wfcDept.setDeptPid(MapHelper.getMapString(dataMap, "dept_pid"));
        wfcDept.setAllowDel(MapHelper.getMapBoolean(dataMap, "is_allow_delete"));
    }

    /**
     * 设置系统相关字段
     */
    private void setSystemFields(WfcDeptPO wfcDept, Map<String, String> dataMap, String tenantCode, boolean isDelete) {
        wfcDept.setTenantCode(tenantCode);
        wfcDept.setUniqId(0L); // 默认为0，删除时会在处理逻辑中设置为主键ID

        // 设置有效性标志
        if (isDelete) {
            wfcDept.setYn(Boolean.FALSE);
        } else {
            Integer isDeleteFlag = MapHelper.getMapInt(dataMap, "is_delete");
            wfcDept.setYn(isDeleteFlag == null || isDeleteFlag == 0);
        }
    }

    /**
     * 设置审计相关字段
     */
    private void setAuditFields(WfcDeptPO wfcDept, Map<String, String> dataMap) {
        LocalDateTime now = LocalDateTime.now();

        // 创建时间和创建人
        wfcDept.setCreated(
                Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "create_time"))
                        .orElse(now)
        );
        wfcDept.setCreator(
                Optional.ofNullable(MapHelper.getMapString(dataMap, "create_user"))
                        .filter(StringUtils::isNotBlank)
                        .orElse(DEFAULT_CREATOR)
        );

        // 修改时间和修改人
        wfcDept.setModified(
                Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "update_time"))
                        .orElse(now)
        );
        wfcDept.setEditor(
                Optional.ofNullable(MapHelper.getMapString(dataMap, "update_user"))
                        .filter(StringUtils::isNotBlank)
                        .orElse(DEFAULT_CREATOR)
        );
    }
    
}
