package com.jd.kf.oss.performance.app.task;

import com.jd.kf.oss.performance.app.service.UserInfoPeriodSyncService;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息绩效月同步任务
 */
@Component
@DongJob(name = "用户信息绩效月同步任务")
@Slf4j
public class UserInfoPeriodSyncTask extends AbstractJobHandler {

    @Autowired
    private UserInfoPeriodSyncService userInfoPeriodSyncService;

    /**
     * 定时执行用户信息周期性同步任务
     * @param jobContext 作业上下文对象，包含作业执行所需的相关信息
     * @return 作业执行结果，成功时返回JobResult.SUCCESS
     */
    @Override
    public JobResult execute(JobContext jobContext) {
        userInfoPeriodSyncService.userInfoPeriodSyncTask();
        return JobResult.SUCCESS;
    }
}
