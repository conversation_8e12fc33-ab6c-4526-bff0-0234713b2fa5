package com.jd.kf.oss.performance.app.consumer.mq.processor;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.WaveEntry;
import com.jd.kf.oss.performance.app.consumer.config.TaskDynamicConfig;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.ModelAdapter;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: songweijia1
 * @description: 多表数据订阅处理基类，支持一个源表拆分到多个目标表
 * @date: 2025/7/8
 */

@Slf4j
public abstract class MultiTableProcessor<T, U> {

    protected static final String OFFICIAL_TENANT_CODE_STR = "kf_perform_retail";
    protected static final String DEFAULT_CREATOR = "sys";

    @Resource
    protected TaskDynamicConfig taskDynamicConfig;

    /**
     * 处理数据库Binlog消息
     *
     * @param entryMessage 要处理的消息对象
     */
    public void process(EntryMessage entryMessage) {
        if (entryMessage == null) {
            log.warn("[{}] EntryMessage is null, skipping processing", getSyncTable());
            return;
        }

        try {
            // 初始化上下文
            UserContextHolder.initAndSetUserContext(OFFICIAL_TENANT_CODE_STR, DEFAULT_CREATOR);

            // 处理消息
            MultiTableProcessResult<T, U> result = processEntryMessage(entryMessage);

            // 记录处理结果统计
            logProcessingStatistics(result);

            // 持久化数据
            if (shouldPersist()) {
                persistDataWithTransaction(result);
            } else {
                log.info("[{}] Persistence is disabled, skipping data persistence", getSyncTable());
            }

        } catch (Exception e) {
            log.error("[{}] Failed to process message, table: {}, error: {}",
                    getSyncTable(), entryMessage.getHeader().getTableName(), e.getMessage(), e);
            throw new RuntimeException("处理消息失败: " + e.getMessage(), e);
        } finally {
            UserContextHolder.remove();
        }
    }

    /**
     * 记录处理结果统计信息
     */
    private void logProcessingStatistics(MultiTableProcessResult<T, U> result) {
        if (log.isInfoEnabled()) {
            log.info("[{}] Processing completed - Main(Total: {}, ToUpsert: {}, ToDelete: {}), Ext(Total: {}, ToUpsert: {}, ToDelete: {})",
                    getSyncTable(),
                    result.getTotalMain().size(),
                    result.getToUpsertMain().size(),
                    result.getToDeleteMain().size(),
                    result.getTotalExt().size(),
                    result.getToUpsertExt().size(),
                    result.getToDeleteExt().size());
        }
    }

    /**
     * 带事务的数据持久化处理
     */
    private void persistDataWithTransaction(MultiTableProcessResult<T, U> result) {
        try {
            // 先处理主表的插入/更新操作
            if (result.hasUpsertMainData()) {
                persistMainData(result.getToUpsertMain());
                log.debug("[{}] Persisted {} main records", getSyncTable(), result.getToUpsertMain().size());
            }

            // 处理扩展表的插入/更新操作
            if (result.hasUpsertExtData()) {
                persistExtData(result.getToUpsertExt());
                log.debug("[{}] Persisted {} ext records", getSyncTable(), result.getToUpsertExt().size());
            }

            // 处理主表的删除数据
            if (result.hasToDeleteMainData()) {
                logisticRemoveMainData(result.getToDeleteMain());
                log.debug("[{}] Processed {} main delete records", getSyncTable(), result.getToDeleteMain().size());
            }

            // 处理扩展表的删除数据
            if (result.hasToDeleteExtData()) {
                logisticRemoveExtData(result.getToDeleteExt());
                log.debug("[{}] Processed {} ext delete records", getSyncTable(), result.getToDeleteExt().size());
            }
        } catch (Exception e) {
            log.error("[{}] Failed to persist data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("数据持久化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理EntryMessage
     */
    private MultiTableProcessResult<T, U> processEntryMessage(EntryMessage entryMessage) {
        MultiTableProcessResult<T, U> result = new MultiTableProcessResult<>();

        WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
        List<WaveEntry.RowData> rowDataList = entryMessage.getRowChange().getRowDatasList();

        if (CollectionUtils.isEmpty(rowDataList)) {
            log.warn("[{}] No row data found in message", getSyncTable());
            return result;
        }

        // 批量处理行数据
        for (WaveEntry.RowData rowData : rowDataList) {
            try {
                processRowData(rowData, eventType, result);
            } catch (Exception e) {
                log.error("[{}] Failed to process row data for event type: {}, error: {}",
                        getSyncTable(), eventType, e.getMessage(), e);
                // 继续处理其他行数据，不因单行失败而中断整个批次
            }
        }

        // 处理转换后的数据
        if (result.hasAnyData()) {
            processWithExistsData(result);
        } else {
            log.info("[{}] No valid data to process after conversion", getSyncTable());
        }

        return result;
    }

    /**
     * 处理单行数据
     */
    private void processRowData(WaveEntry.RowData rowData, WaveEntry.EventType eventType, MultiTableProcessResult<T, U> result) {
        Map<String, String> dataMap = getDataMap(rowData, eventType);

        if (log.isInfoEnabled()) {
            log.info("[{}] [{}] Processing row data: {}", getSyncTable(), eventType, JSON.toJSONString(dataMap));
        }

        // 数据转换 - 转换为主表数据
        T mainData = convertToMainData(eventType, dataMap);
        if (mainData != null) {
            result.addToTotalMain(mainData);
        }

        // 数据转换 - 转换为扩展表数据
        U extData = convertToExtData(eventType, dataMap);
        if (extData != null) {
            result.addToTotalExt(extData);
        }

        if (mainData == null && extData == null) {
            log.warn("[{}] Failed to convert data for event type: {}, data: {}",
                    getSyncTable(), eventType, JSON.toJSONString(dataMap));
        }
    }

    protected Map<String, String> getDataMap(WaveEntry.RowData rowData, WaveEntry.EventType eventType) {
        List<WaveEntry.Column> columns = eventType == WaveEntry.EventType.DELETE ?
                rowData.getBeforeColumnsList() :
                rowData.getAfterColumnsList();

        return columns.stream()
                .collect(Collectors.toMap(
                        WaveEntry.Column::getName,
                        WaveEntry.Column::getValue,
                        (a, b) -> a
                ));
    }

    /**
     * 处理数据的通用流程
     */
    protected void processWithExistsData(MultiTableProcessResult<T, U> result) {
        // 处理主表数据
        if (result.hasTotalMainData()) {
            processMainTableData(result);
        }

        // 处理扩展表数据
        if (result.hasTotalExtData()) {
            processExtTableData(result);
        }
    }

    /**
     * 处理主表数据
     */
    private void processMainTableData(MultiTableProcessResult<T, U> result) {
        ModelAdapter<T> mainAdapter = getMainModelAdapter();
        
        // 1. 获取业务主键列表并去重
        List<String> businessKeys = extractBusinessKeys(result.getTotalMain(), mainAdapter);
        if (CollectionUtils.isEmpty(businessKeys)) {
            log.warn("[{}] No valid main business keys found, skipping main data processing", getSyncTable());
            return;
        }

        // 2. 批量获取现有数据
        Map<String, T> existingDataMap = getExistingMainDataMap(businessKeys);

        // 3. 批量处理数据
        int processedCount = 0;
        for (T newData : result.getTotalMain()) {
            try {
                String businessKey = mainAdapter.getBusinessKey(newData);
                if (StringUtils.isBlank(businessKey)) {
                    log.warn("[{}] Main business key is blank for data {}, skipping", getSyncTable(), JSON.toJSONString(newData));
                    continue;
                }

                T existingData = existingDataMap.get(businessKey);
                processOneMainData(newData, existingData, result, mainAdapter);
                processedCount++;
            } catch (Exception e) {
                log.error("[{}] Failed to process single main data record: {}", getSyncTable(), e.getMessage(), e);
                // 继续处理其他数据
            }
        }

        log.info("[{}] Processed {} out of {} main records", getSyncTable(), processedCount, result.getTotalMain().size());
    }

    /**
     * 处理扩展表数据
     */
    private void processExtTableData(MultiTableProcessResult<T, U> result) {
        ModelAdapter<U> extAdapter = getExtModelAdapter();
        
        // 1. 获取业务主键列表并去重
        List<String> businessKeys = extractBusinessKeys(result.getTotalExt(), extAdapter);
        if (CollectionUtils.isEmpty(businessKeys)) {
            log.warn("[{}] No valid ext business keys found, skipping ext data processing", getSyncTable());
            return;
        }

        // 2. 批量获取现有数据
        Map<String, U> existingDataMap = getExistingExtDataMap(businessKeys);

        // 3. 批量处理数据
        int processedCount = 0;
        for (U newData : result.getTotalExt()) {
            try {
                String businessKey = extAdapter.getBusinessKey(newData);
                if (StringUtils.isBlank(businessKey)) {
                    log.warn("[{}] Ext business key is blank for data {}, skipping", getSyncTable(), JSON.toJSONString(newData));
                    continue;
                }

                U existingData = existingDataMap.get(businessKey);
                processOneExtData(newData, existingData, result, extAdapter);
                processedCount++;
            } catch (Exception e) {
                log.error("[{}] Failed to process single ext data record: {}", getSyncTable(), e.getMessage(), e);
                // 继续处理其他数据
            }
        }

        log.info("[{}] Processed {} out of {} ext records", getSyncTable(), processedCount, result.getTotalExt().size());
    }

    /**
     * 处理单条主表数据的通用逻辑
     */
    protected void processOneMainData(T newData, T existingData, MultiTableProcessResult<T, U> result, ModelAdapter<T> adapter) {
        if (existingData == null) {
            // 新增数据处理
            handleNewMainData(newData, result, adapter);
        } else {
            // 更新数据处理
            handleUpdateMainData(newData, existingData, result, adapter);
        }
    }

    /**
     * 处理单条扩展表数据的通用逻辑
     */
    protected void processOneExtData(U newData, U existingData, MultiTableProcessResult<T, U> result, ModelAdapter<U> adapter) {
        if (existingData == null) {
            // 新增数据处理
            handleNewExtData(newData, result, adapter);
        } else {
            // 更新数据处理
            handleUpdateExtData(newData, existingData, result, adapter);
        }
    }

    /**
     * 处理新增主表数据
     */
    private void handleNewMainData(T newData, MultiTableProcessResult<T, U> result, ModelAdapter<T> adapter) {
        Boolean isValid = adapter.getYn(newData);

        if (Boolean.FALSE.equals(isValid)) {
            // 如果新数据标记为删除，则设置为有效并加入删除队列
            adapter.setYn(newData, Boolean.TRUE);
            result.addToUpsertMain(newData);
            result.addToDeleteMain(newData);
        } else if (Boolean.TRUE.equals(isValid)) {
            // 有效的新数据直接插入
            result.addToUpsertMain(newData);
        }
        // 如果isValid为null，则不处理
    }

    /**
     * 处理新增扩展表数据
     */
    private void handleNewExtData(U newData, MultiTableProcessResult<T, U> result, ModelAdapter<U> adapter) {
        Boolean isValid = adapter.getYn(newData);

        if (Boolean.FALSE.equals(isValid)) {
            // 如果新数据标记为删除，则设置为有效并加入删除队列
            adapter.setYn(newData, Boolean.TRUE);
            result.addToUpsertExt(newData);
            result.addToDeleteExt(newData);
        } else if (Boolean.TRUE.equals(isValid)) {
            // 有效的新数据直接插入
            result.addToUpsertExt(newData);
        }
        // 如果isValid为null，则不处理
    }

    /**
     * 处理更新主表数据
     */
    private void handleUpdateMainData(T newData, T existingData, MultiTableProcessResult<T, U> result, ModelAdapter<T> adapter) {
        // 设置现有数据的ID
        Long existingId = adapter.getId(existingData);
        adapter.setId(newData, existingId);

        // 检查数据是否需要更新（基于修改时间）
        if (shouldUpdateData(existingData, newData, adapter)) {
            // 如果新数据标记为删除，需要特殊处理
            if (Boolean.FALSE.equals(adapter.getYn(newData))) {
                // 设置uniqId为主键ID，用于软删除的唯一索引处理
                result.addToDeleteMain(newData);
            }
            result.addToUpsertMain(newData);
        }
    }

    /**
     * 处理更新扩展表数据
     */
    private void handleUpdateExtData(U newData, U existingData, MultiTableProcessResult<T, U> result, ModelAdapter<U> adapter) {
        // 设置现有数据的ID
        Long existingId = adapter.getId(existingData);
        adapter.setId(newData, existingId);

        // 检查数据是否需要更新（基于修改时间）
        if (shouldUpdateData(existingData, newData, adapter)) {
            // 如果新数据标记为删除，需要特殊处理
            if (Boolean.FALSE.equals(adapter.getYn(newData))) {
                // 设置uniqId为主键ID，用于软删除的唯一索引处理
                result.addToDeleteExt(newData);
            }
            result.addToUpsertExt(newData);
        }
    }

    /**
     * 判断是否需要更新数据
     * 基于修改时间比较，只有新数据的修改时间晚于现有数据时才更新
     */
    private <V> boolean shouldUpdateData(V existingData, V newData, ModelAdapter<V> adapter) {
        LocalDateTime existingModified = adapter.getModified(existingData);
        LocalDateTime newModified = adapter.getModified(newData);

        // 如果任一时间为null，则进行更新（保守策略）
        if (existingModified == null || newModified == null) {
            return true;
        }

        return DateUtils.isEqualOrBefore(existingModified, newModified);
    }

    /**
     * 从给定列表中提取业务主键
     */
    protected <V> List<String> extractBusinessKeys(List<V> dataList, ModelAdapter<V> modelAdapter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream()
                .filter(Objects::nonNull)
                .map(data -> {
                    try {
                        return modelAdapter.getBusinessKey(data);
                    } catch (Exception e) {
                        log.warn("[{}] Failed to extract business key from data: {}", getSyncTable(), e.getMessage());
                        return null;
                    }
                })
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    // ========== 抽象方法，子类需要实现 ==========

    /**
     * 获取主表模型适配器
     */
    protected abstract ModelAdapter<T> getMainModelAdapter();

    /**
     * 获取扩展表模型适配器
     */
    protected abstract ModelAdapter<U> getExtModelAdapter();

    /**
     * 将WaveEntry.EventType类型和Map<String, String>数据转换为主表模型对象T。
     *
     * @param eventType WaveEntry.EventType类型的事件类型。
     * @param dataMap   包含转换所需数据的Map对象。
     * @return 转换后的主表模型对象T。
     */
    protected abstract T convertToMainData(WaveEntry.EventType eventType, Map<String, String> dataMap);

    /**
     * 将WaveEntry.EventType类型和Map<String, String>数据转换为扩展表模型对象U。
     *
     * @param eventType WaveEntry.EventType类型的事件类型。
     * @param dataMap   包含转换所需数据的Map对象。
     * @return 转换后的扩展表模型对象U。
     */
    protected abstract U convertToExtData(WaveEntry.EventType eventType, Map<String, String> dataMap);

    /**
     * 获取指定业务键的现有主表数据映射。
     *
     * @param businessKeys 业务键列表
     * @return 现有主表数据的映射，键为业务键，值为对应的数据对象
     */
    protected abstract Map<String, T> getExistingMainDataMap(List<String> businessKeys);

    /**
     * 获取指定业务键的现有扩展表数据映射。
     *
     * @param businessKeys 业务键列表
     * @return 现有扩展表数据的映射，键为业务键，值为对应的数据对象
     */
    protected abstract Map<String, U> getExistingExtDataMap(List<String> businessKeys);

    /**
     * 获取同步表枚举类型
     *
     * @return 同步表枚举类型
     */
    protected abstract String getSyncTable();

    /**
     * 是否需要持久化
     */
    protected abstract boolean shouldPersist();

    /**
     * 持久化主表数据
     */
    protected abstract void persistMainData(List<T> toUpsert);

    /**
     * 持久化扩展表数据
     */
    protected abstract void persistExtData(List<U> toUpsert);

    /**
     * 逻辑删除主表数据
     */
    protected abstract void logisticRemoveMainData(List<T> toDelete);

    /**
     * 逻辑删除扩展表数据
     */
    protected abstract void logisticRemoveExtData(List<U> toDelete);

}