package com.jd.kf.oss.performance.app;

import com.jd.framework.boot.autoconfigure.DongBootApplication;
import com.jd.framework.boot.core.DongApplication;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import javax.annotation.PostConstruct;
import java.io.IOException;

@DongBootApplication
@PropertySources(@PropertySource(value = { "classpath:**.xml","classpath:/spring/*.xml", "classpath:/profile/dongdal.properties", "classpath:/profile/jsf.properties", "classpath:/profile/jmq.properties", "classpath:/profile/jimdb.properties", "classpath:/profile/ducc.properties", "classpath:/profile/*.properties", "classpath:/profile/dongcache.properties" }, ignoreResourceNotFound = true))
@EnableAsync
@EnableAspectJAutoProxy
@EnableScheduling
@ImportResource(locations = {"classpath*:spring/spring-**.xml"})
@ComponentScan(basePackages = {"com.jd.kf.oss.performance"})
@MapperScan(basePackages={"com.jd.kf.oss.performance.infra.mybatis.mapper"})
@Slf4j
public class PerformanceApplication {

    @PostConstruct
    public void debugXmlResources() {
        log.info("=== DEBUG: 检查Spring XML配置文件加载情况 ===");
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            // 检查classpath*:spring/spring-**.xml匹配的所有文件
            Resource[] resources = resolver.getResources("classpath*:spring/spring-**.xml");
            log.info("找到 " + resources.length + " 个匹配的XML配置文件:");
            for (int i = 0; i < resources.length; i++) {
                log.info((i + 1) + ". " + resources[i].getURL().toString());
                if (resources[i].getURL().toString().contains("spring-plugin-jsf.xml")) {
                    log.info("   *** 发现JSF配置文件: " + resources[i].getURL().toString());
                }
            }
            
            // 特别检查jsfRegistry相关的文件
            log.info("\n=== 特别检查包含jsfRegistry的文件 ===");
            for (Resource resource : resources) {
                if (resource.getURL().toString().contains("jsf")) {
                    log.info("JSF相关文件: " + resource.getURL().toString());
                }
            }
        } catch (IOException e) {
            log.error("检查XML资源时出错: " + e.getMessage());
        }
        log.info("=== DEBUG结束 ===\n");
    }

    public static void main(String[] args) {
        DongApplication.run(PerformanceApplication.class, args);
    }
}
