package com.jd.kf.oss.performance.app.controller.controller;

import com.jd.kf.oss.performance.app.controller.converter.CoefficientConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientCreateReq;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientPageReq;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientUniqueReq;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientUpdateReq;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.coefficient.CoefficientDetailVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.coefficient.CoefficientPageVO;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.CoefficientAggregateService;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDomainService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系数配置管理控制器
 */
@Api(tags = "系数配置管理")
@RestController
@RequestMapping("/performance/coefficient")
public class CoefficientController {

    @Autowired
    private CoefficientDomainService coefficientDomainService;

    @Autowired
    private CoefficientAggregateService coefficientAggregateService;

    /**
     * 创建系数配置
     */
    @ApiOperation("创建系数配置")
    @PostMapping("/create")
    public ApiResult<Void> createCoefficient(@RequestBody @Validated CoefficientCreateReq request) {
        CoefficientDO coefficientDO = CoefficientConverter.INSTANCE.request2DO(request);
        // 设置当前绩效月
        coefficientDO.setPeriod(DateUtils.getCurrentPerformancePeriod());
        coefficientDomainService.saveCoefficientDO(coefficientDO);
        return ApiResult.success();
    }

    /**
     * 更新系数配置
     */
    @ApiOperation("更新系数配置")
    @PostMapping("/update")
    public ApiResult<Void> updateCoefficient(@Validated @RequestBody CoefficientUpdateReq request) {
        CoefficientDO coefficientDO = CoefficientConverter.INSTANCE.request2DO(request);
        // 只能修改当前绩效月的系数配置
        coefficientDO.setPeriod(DateUtils.getCurrentPerformancePeriod());
        coefficientDomainService.updateCoefficientDO(coefficientDO);
        return ApiResult.success();
    }

    /**
     * 删除系数配置
     */
    @ApiOperation("删除系数配置")
    @PostMapping("/delete")
    public ApiResult<Void> deleteCoefficient(@RequestBody @Validated CoefficientUniqueReq request) {
        CheckUtil.equals(request.getPeriod().toString(), DateUtils.getCurrentPerformancePeriod(), "只能删除当前绩效月的系数配置");
        boolean isDeleted= coefficientAggregateService.deleteCoefficientDOByCodeAndPeriod(request.getCode(), request.getPeriod().toString());
        return isDeleted?ApiResult.success():ApiResult.error("删除失败");
    }

    /**
     * 获取系数配置详情
     */
    @ApiOperation("获取系数配置详情")
    @PostMapping("/detail")
    public ApiResult<CoefficientDetailVO> getCoefficientDetail(@RequestBody @Validated CoefficientUniqueReq request) {
        CoefficientDO coefficientDO = coefficientDomainService.queryCoefficientDOByCodeAndPeriod(request.getCode(), request.getPeriod().toString());
        return ApiResult.success(CoefficientConverter.INSTANCE.do2VO(coefficientDO));
    }

    /**
     * 分页查询系数配置列表
     */
    @ApiOperation(value = "分页查询系数配置",notes = "functionId=performanceCoefficientPage")
    @PostMapping("/page")
    public ApiResult<CommonPage<CoefficientPageVO>> pageCoefficient(@RequestBody @Validated CoefficientPageReq request) {
        //默认绩效月
        String period=request.getPeriod()==null ? DateUtils.getCurrentPerformancePeriod():request.getPeriod().toString();
        // 使用字段参数方式调用DomainService
        CommonPage<CoefficientDO> commonPage = coefficientDomainService.queryCoefficientByNameAndType(
                request.getName(),
                period,
                request.getType(),
                request.getPage(),
                request.getPageSize()
        );
        return ApiResult.success(CoefficientConverter.INSTANCE.pageDO2VO(commonPage));
    }
}