package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.TargetPageRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.TargetUpdateRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.index.IndexItem;
import com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget.*;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;

@Mapper
public interface TargetConverter {
    TargetConverter INSTANCE = Mappers.getMapper(TargetConverter.class);

    PerformanceTargetDO request2DO(TargetPageRequest request);

    CommonPage<TargetPageVO> pageDO2VO(CommonPage<PerformanceTargetDO> commonPage);

    TargetDetailVO target2VO(PerformanceTargetDO DO);
    List<TargetCPDExportVO> do2CPDVO(List<PerformanceTargetDO> performanceTargetDOs);
    List<TargetPriceExportVO> DO2PriceVO(List<PerformanceTargetDO> performanceTargetDOs);
    TargetDetailVO targetComposite2VO(TargetComposite targetComposite);

    @BeforeMapping
    default void targetDO2VO(TargetComposite targetComposite, @MappingTarget TargetDetailVO targetDetailVO) {
        if (targetComposite != null) {
            targetDetailVO = target2VO(targetComposite.getTargetDO());
        }
    }

    List<IndexItem> indexDO2Item(List<IndexDO> indexDOS);

    IndexItem  indexDO2Item(IndexDO indexDO);

    List<AggTargetExportVO> targetComposite2VO(List<TargetComposite> targetComposites);

    @Mapping(source="indexes", target="indexes")
    @Mapping(source = "targetDO.businessLineId", target = "businessLineId")
    @Mapping(source = "targetDO.businessLineName", target = "businessLineName")
    @Mapping(source = "targetDO.evaluationPlan", target = "evaluationPlan")
    @Mapping(source = "targetDO.period", target = "period")
    AggTargetExportVO targetComposite2AggTargetExportVO(TargetComposite targetComposite);

    IndexExportVO  indexDO2IndexExportVO(IndexDO indexDO);

    @Mapping(source="template", target="template")
    List<IndexExportVO>  do2IndexExportVO(List<IndexDO> indexDOS);

    List<AggTargetExportVO> target2AggTargetExportVO(List<PerformanceTargetDO> performanceTargetDOs);

    PerformanceTargetDO updateRequest2TargetDO(TargetUpdateRequest request);

    List<IndexDO> updateRequest2IndexDO(List<TargetUpdateRequest.IndexItem> indexItems);
    @Mapping(source = "startDate", target = "startDate", qualifiedByName = "localDateToString")
    @Mapping(source = "endDate", target = "endDate", qualifiedByName = "localDateToString")
    IndexDO indexItem2IndexDO(TargetUpdateRequest.IndexItem indexItem);

    LinkedHashMap<Integer,PerformanceTargetDO>  importCPDVoMap2TargetDOMap(LinkedHashMap<Integer, ImportCPDVO> importCPDVOList);
    LinkedHashMap<Integer,PerformanceTargetDO> importPriceAndDaysVoMap2TargetDOMap(LinkedHashMap<Integer, ImportPriceAndDaysVO> voList);
    @Named("yearMonthToString")
    default String yearMonthToString(YearMonth yearMonth) {
        if (yearMonth == null) {
            return null;
        }
        return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    @Named("localDateToString")
    default String localDateToString(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
