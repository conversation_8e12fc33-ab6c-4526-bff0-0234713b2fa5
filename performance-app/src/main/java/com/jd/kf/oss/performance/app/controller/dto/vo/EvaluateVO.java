package com.jd.kf.oss.performance.app.controller.dto.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上级评价VO
 * 用于前端展示上级评价的基本信息
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("上级评价VO")
@Data
public class EvaluateVO {

    /** 自增主键 */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", example = "2025-07")
    private String period;

    /** 主管ERP */
    @ApiModelProperty(value = "主管ERP", example = "lanyue.9")
    private String managerErp;

    /** 评价分数 */
    @ApiModelProperty(value = "评价分数", example = "1.0")
    private String evaluationScore;

    /** 一级租户标识 */
    @ApiModelProperty(value = "租户标识", example = "tenant001")
    private String tenantCode;

    /** 更新人 */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String editor;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-07-02T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;
}