package com.jd.kf.oss.performance.app.task.runtime;

import com.jd.kf.oss.performance.domain.runtime.aggregate.PerformanceTaskAggregateService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/30
 * 绩效组
 */
@Component
@DongJob(name = "绩效组绩效月运行任务")
@Slf4j
public class PerformanceRunTask extends AbstractJobHandler {
    @Autowired
    private PerformanceTaskAggregateService performanceTaskAggregateService;



    /**
     * @param jobContext
     * @return {@link JobResult }
     */
    @Override
    public JobResult execute(JobContext jobContext) {
        String tenantCode = "kf_perform_retail";
        String period = DateUtils.getCurrentPerformancePeriod();
        PerformanceTaskDO performanceTaskDO = new PerformanceTaskDO();
        performanceTaskDO = performanceTaskDO.load(tenantCode, period);
        performanceTaskAggregateService.calc(performanceTaskDO);


        return JobResult.SUCCESS;
    }
}
