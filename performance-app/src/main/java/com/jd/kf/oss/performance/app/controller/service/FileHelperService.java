package com.jd.kf.oss.performance.app.controller.service;

import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.kf.oss.performance.utils.IDUtils;
import com.jd.kf.oss.performance.utils.excel.EasyExcelExportStringSheetExtract;
import com.jd.kf.oss.performance.utils.excel.EasyExcelUtils;
import com.jd.kf.oss.performance.utils.file.FileStorageUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;

@Service
public class FileHelperService {


    @Resource
    private FileStorageUtils fileStorageUtils;

    @Resource
    private DynamicConfig dynamicConfig;


    /**
     * 将数据列表导出为Excel并上传至OSS存储
     * @param request HTTP请求对象，用于获取导出Excel的相关配置
     * @param ossFileName OSS存储的文件名称（不包含后缀）
     * @param tClass 数据对象的Class类型，用于定义Excel表头结构
     * @param data 待导出的数据列表
     * @return 返回OSS存储的文件访问路径
     */
    public <T>  String uploadToOSS(HttpServletRequest request, String ossFileName, Class<T> tClass, List<T> data){
       ByteArrayOutputStream outputStream =  EasyExcelUtils.exportSingleSheet(ossFileName,tClass, data, request);
       return fileStorageUtils.upload(generateCompleteFileName(ossFileName +excelFormatSuffix(request)),
                outputStream);
    }



    /**
     * 将EasyExcel生成的数据上传至OSS存储
     * @param request HTTP请求对象，用于获取请求相关信息
     * @param ossFileName OSS存储的文件名（不包含后缀）
     * @param sheetExtract EasyExcel导出数据配置对象，包含导出内容和格式设置
     * @return 返回OSS存储后的文件完整访问路径
     */
    public String uploadToOSS(HttpServletRequest request, String ossFileName, EasyExcelExportStringSheetExtract sheetExtract){
        ByteArrayOutputStream outputStream =  EasyExcelUtils.generateExcelAndExport(sheetExtract, request);
        return fileStorageUtils.upload(generateCompleteFileName( ossFileName + excelFormatSuffix(request)),
                outputStream);
    }


    /**
     * 从指定URL下载文件并返回文件对象
     * @param url 要下载文件的URL地址
     * @return 下载后的文件对象
     */
    public File downloadFile(String url) {
        return fileStorageUtils.getFile(url);
    }


    /**
     * 上传文件到存储系统
     * @param file 要上传的多部分文件对象，不能为空且必须包含有效文件名
     * @return 返回文件上传后的存储路径或标识符
     * @throws IllegalArgumentException 当文件为空或文件名为空时抛出
     * @throws BizException 当文件上传过程中发生IO异常时抛出
     */
    public String uploadFile(MultipartFile file) {
        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 检查文件是否为空
            if (file.isEmpty()) {
                throw new IllegalArgumentException("上传文件不能为空");
            }
            // 获取原始文件名
            String originalFileName = file.getOriginalFilename();
            if (originalFileName == null || originalFileName.trim().isEmpty()) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            String fileName = generateCompleteFileName(originalFileName);
            outputStream.write(file.getBytes());
            return fileStorageUtils.upload (fileName, outputStream);
        } catch (IOException e) {
            throw new BizException("文件上传失败: " + e.getMessage(), e);
        }
    }



    public  String generateCompleteFileName(String fileName) {
        String projectPrefix=dynamicConfig.getExportPerformanceBatchDataFileNamePrefix();
        String id = IDUtils.generateFileId();
        return projectPrefix+"_"+ id + "_" + fileName;
    }


    /**
     * 检查导出数据行数是否超过配置的最大限制
     * @param rowData 待导出的数据列表，可为null
     */
    public void checkExportAmountLimit(List<?> rowData){
        int exportCount=rowData==null? 0 : rowData.size();
        int countLimit = dynamicConfig.getExportExcelMaxRowCount();
        if ( exportCount > countLimit) {
            throw new IllegalArgumentException("excel导出行数不能超过" + countLimit);
        }
    }

    /**
     * 检查Excel文件行数是否超过限制
     * @param excelFile 待检查的Excel文件对象，不能为null
     * @throws IOException 当读取文件发生IO异常时抛出
     * @throws IllegalArgumentException 当传入的文件对象为null时抛出
     */
    public void checkExcelFileRowCount(File excelFile) throws IOException {
        if (excelFile==null) {
            throw new IllegalArgumentException("上传的文件不能为空");
        }
        // 通过EasyExcel read 检查excel文件行数
        Integer count = EasyExcelUtils.countFileRows(excelFile);
        checkImportAmountLimit(count);
    }



    /**
     * 检查导入Excel文件的数据行数是否超过限制
     * @param count 待检查的Excel文件数据行数
     */
    public void checkImportAmountLimit(Integer count){
        int countLimit = dynamicConfig.getImportExcelMaxRowCount();
        if (count != null && count > countLimit) {
            throw new IllegalArgumentException(String.format("excel文件数据行数%d不能超过%d", count, countLimit));
        }
    }


    /**
     * 根据请求头中的User-Agent获取对应的Excel格式后缀，若无匹配则返回默认格式后缀
     * @param request HTTP请求对象，用于获取请求头中的User-Agent信息
     * @return 返回Excel文件格式后缀名(如：".xls"或".xlsx")
     */
    public static String excelFormatSuffix(HttpServletRequest request) {
       return EasyExcelUtils.getExcelTypeByAgentOrDefault(request).getValue();
    }


    public boolean deleteFile(File file){
        try {
            if (file.exists() && !file.isDirectory()) {
                return file.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


}
