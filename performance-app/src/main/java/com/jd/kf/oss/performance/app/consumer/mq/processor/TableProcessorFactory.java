package com.jd.kf.oss.performance.app.consumer.mq.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: songweijia1
 * @description: 数据订阅处理器工厂类
 * @date: 2025/7/2
 */

@Slf4j
@Component
public class TableProcessorFactory {

    @Autowired
    private List<SingleTableProcessor<?>> singleTableProcessorList;

    @Autowired
    private List<MultiTableProcessor<?, ?>> multiTableProcessorList;

    private final Map<String, Object> processorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 多表处理器
        for (MultiTableProcessor<?, ?> multiTableProcessor : multiTableProcessorList) {
            processorMap.put(multiTableProcessor.getSyncTable(), multiTableProcessor);
        }

        // 单表处理器
        for (SingleTableProcessor<?> singleTableProcessor : singleTableProcessorList) {
            processorMap.put(singleTableProcessor.getSyncTable(), singleTableProcessor);
        }
    }

    /**
     * 根据表名获取对应的处理器
     *
     * @param tableName 表名
     * @return 对应的处理器，如果没有找到则返回null
     */
    public Object getProcessor(String tableName) {
        Object processor = processorMap.get(tableName);
        if (processor == null) {
            log.warn("No processor found for table: {}", tableName);
        }
        return processor;
    }

    /**
     * 检查是否存在指定表的处理器
     *
     * @param tableName 表名
     * @return 是否存在处理器
     */
    public boolean hasProcessor(String tableName) {
        return processorMap.containsKey(tableName);
    }

    /**
     * 获取所有支持的表名
     *
     * @return 支持的表名集合
     */
    public java.util.Set<String> getSupportedTables() {
        return processorMap.keySet();
    }

}