package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class TargetPageVO {

    /**
     * 绩效组名称
     */
    private String businessLineName;

    /**
     * 绩效组id
     */
    private String businessLineId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 考核方案类型
     */
    private String evaluationType;

    /**
     * 考核方案
     */
    private String evaluationPlan;

    /**
     * 考核方案Code
     */
    private String evaluationPlanCode;

    /**
     * 单价
     */
    private String price;

    /**
     * 产能目标
     */
    private String cpd;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String editor;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime created;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;

    /**
     * 绩效月
     */
    private String period;


    /**
     * 月标准天数
     */
    private String days;
}
