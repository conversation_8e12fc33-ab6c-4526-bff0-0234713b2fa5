package com.jd.kf.oss.performance.app.controller.dto.vo.index;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "指标分页查询结果")
public class IndexPageBasicVO implements Serializable {
    /**
     * 指标编码
     */
    @ApiModelProperty(value = "指标编码", example = "213422", required = true)
    private String kpiCd;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", example = "平均处理时长", required = true)
    private String kpiName;

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述", example = "这是示例指标1的描述")
    private String description;

    /**
     * 指标状态
     */
    @ApiModelProperty(value = "指标状态", example = "有效，无效", notes = "可能的值: 有效, 已失效")
    private IndexStatusEnum status;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-06-20 13:50:19", dataType = "java.util.Date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", example = "", notes = "xxx.123")
    private String editor;

    /**
     * 指标平台URL
     */
    @ApiModelProperty(value = "指标平台URL", example = "http://one-service.jd.com/#/indicator/detail?index=16424")
    private String indexPlatformUrl;
}
