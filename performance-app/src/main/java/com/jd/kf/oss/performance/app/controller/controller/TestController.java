package com.jd.kf.oss.performance.app.controller.controller;

//import com.alibaba.druid.support.json.JSONUtils;

import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.service.FileHelperService;
import com.jd.kf.oss.performance.app.service.UserInfoPeriodSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * Description: Web Demo
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {
    @Autowired
    private UserInfoPeriodSyncService userInfoPeriodSyncService;


    @Autowired
    private FileHelperService fileHelperService;


    /**
     * 测试接口，返回问候语
     * @return 问候语字符串
     */
    @GetMapping("/hello")
    public String hello() {
//        userInfoPeriodSyncService.userInfoPeriodSyncTask();
        return "hello world";
    }

    /**
     * 本地上传oss获取url
     */
    @PostMapping("/import/excel")
    public ApiResult<String> importExcelAndGetOssUrl(@RequestParam("file") MultipartFile excelFile){
        String fileOssUrl = fileHelperService.uploadFile(excelFile);
        return ApiResult.success(fileOssUrl);
    }

}
