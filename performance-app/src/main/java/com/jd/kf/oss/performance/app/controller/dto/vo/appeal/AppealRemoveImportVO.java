package com.jd.kf.oss.performance.app.controller.dto.vo.appeal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 无效数据剔除导入VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class AppealRemoveImportVO {

    /** 绩效月 */
    @JsonProperty(value = "绩效月", index = 0)
    private String period;

    /** 指标名称 */
    @JsonProperty(value = "指标名称", index = 1)
    private String kpiName;

    /** 需剔除的业务单号 */
    @JsonProperty(value = "需剔除的业务单据", index = 2)
    private String ticketId;

    /** 编辑人（导入时需要，不在Excel中） */
    private String editor;
}
