package com.jd.kf.oss.performance.app.service;

import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import com.jd.kf.oss.performance.utils.CollectionDiffUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/07/04
 */

@Service
@Slf4j
public class UserInfoPeriodSyncService {

    /**
     * 用于操作用户数据领域服务的依赖注入实例
     */
    @Autowired
    private UserDomainService userDomainService;
    /**
     * 同步WFC用户数据到当前绩效月的用户表中
     */
    public void userInfoPeriodSyncTask() {
        CallerInfo callerInfo = Profiler.registerInfo("performance-userInfoPeriodSyncTask");
        try {
            log.info("[userInfoPeriodSyncTask] 任务开始");

            UserContextHolder.initAndSetUserContext(SystemConstants.TENANT_CODE_RETAIL, SystemConstants.EDITOR_SYSTEM);

            // 获取当前绩效月
            String currentPeriod = DateUtils.getCurrentPerformancePeriod();

            // 1. 查询当前绩效月的User数据
            List<UserDO> currentUsers = userDomainService.queryAllUserByPeriod(currentPeriod);

            // 2. 查询所有WFC用户数据
            List<UserDO> wfcUsers = userDomainService.queryAllWfcUserData();

            if (wfcUsers == null || wfcUsers.isEmpty()) {
                log.info("WFC表中无用户数据，无需同步");
                return;
            }

            // 3. 找出需要插入的用户以及更新的用户（WFC中有但User表当前绩效月中没有的）
            Map<String, List<UserDO>> missingUsers = findMissingUsers(wfcUsers, currentUsers);
            List<UserDO> updateUserList = missingUsers.get(CollectionDiffUtil.DIFF_UPDATE);
            List<UserDO> insertUserList = missingUsers.get(CollectionDiffUtil.DIFF_ADD);

            if (insertUserList != null && !insertUserList.isEmpty()) {
                // 将缺失的WFC用户数据插入到User表，并设置当前绩效月
                copyWfcUserToUser(insertUserList, currentPeriod);
                log.info("已从WFC表插入 " + missingUsers.size() + " 条缺失数据到绩效月 " + currentPeriod);
            } else {
                log.info("当前绩效月 " + currentPeriod + " 已包含所有WFC用户数据，无需插入");
            }

            if (updateUserList != null && !updateUserList.isEmpty()) {
                // 将缺失的WFC用户数据插入到User表，并设置当前绩效月
                updateUser(updateUserList, currentPeriod);
                log.info("已从WFC表更新 " + updateUserList.size() + " 条缺失数据到绩效月 " + currentPeriod);
            } else {
                log.info("当前绩效月 " + currentPeriod + " 无需要更新数据");
            }

            log.info("用户信息同步任务完成");
        } catch (Exception e) {
            log.error("userInfoPeriodSyncTask exception", e);
            Profiler.functionError(callerInfo);
        } finally {
            UserContextHolder.remove();
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private Map<String, List<UserDO>> findMissingUsers(List<UserDO> wfcUsers, List<UserDO> currentUsers) {
        // 获取差异集合
        String[] uniqueFields = new String[]{"tenantCode", "erp"};
        String[] checkFields = new String[]{"businessLineId", "deptId"};
        return CollectionDiffUtil.diffCollections(currentUsers, wfcUsers,
                uniqueFields, checkFields);
    }

    private void copyWfcUserToUser(List<UserDO> wfcUsers, String period) {
        List<UserDO> users = wfcUsers.stream().map(u -> {
            return getUserDO(period, u, CollectionDiffUtil.DIFF_ADD);
        }).collect(Collectors.toList());
        userDomainService.saveBatchUsers(users);
    }


    private void updateUser(List<UserDO> wfcUsers, String period) {
        List<UserDO> users = wfcUsers.stream().map(u -> {
            return getUserDO(period, u, CollectionDiffUtil.DIFF_UPDATE);
        }).collect(Collectors.toList());
        userDomainService.updateBatchUsers(users);
    }

    @NotNull
    private static UserDO getUserDO(String period, UserDO u, String diffType) {
        UserDO user = new UserDO();
        user.setTenantCode(u.getTenantCode());
        user.setBusinessLineId(u.getBusinessLineId());
        user.setBusinessLineName(u.getBusinessLineName());
        user.setErp(u.getErp());
        user.setManagerErp(u.getManagerErp());
        user.setName(u.getName());
        user.setDeptId(u.getDeptId());
        user.setDeptName(u.getDeptName());
        user.setEntryDate(u.getEntryDate());
        user.setQuitTime(u.getQuitTime());
        user.setPeriod(period);
        user.setCreator(u.getCreator());
        if (diffType.equals(CollectionDiffUtil.DIFF_UPDATE)) {
            user.setEditor(u.getEditor());
        }
        return user;
    }
}
