package com.jd.kf.oss.performance.app.controller.dto.vo.evaluate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 上级评价导入VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class EvaluateImportVO {

    /** 绩效月 */
    @JsonProperty(value = "绩效月")
    private String period;

    /** 主管ERP */
    @JsonProperty(value = "主管ERP")
    private String managerErp;

    /** 评价分数 */
    @JsonProperty(value = "评价分数")
    private String evaluationScore;

    /** 编辑人（导入时需要，不在Excel中） */
    private String editor;
}
