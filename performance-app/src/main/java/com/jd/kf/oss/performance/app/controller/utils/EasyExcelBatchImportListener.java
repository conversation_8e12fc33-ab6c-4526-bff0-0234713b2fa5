package com.jd.kf.oss.performance.app.controller.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Maps;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;
import com.jd.kf.oss.performance.utils.excel.EasyExcelExportStringSheetExtract;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * excel批量导入工具类
 * 只能通过提供class元信息或者自定义转换函数实例化
 * T和V可以是同一个类型，通过jackson注解对一些字段进行隔离
 * @param <T> 最终要转成的业务对象
 * @param <V> VO对象，对一些不必要的excel表格字段进行隔离，
 */
@Slf4j
@Getter
@Setter
public class EasyExcelBatchImportListener<T, V> extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 默认批处理200行数据
     */
    private int batchCount = 200;
    /**
     * 单个sheet 表头
     */
    private Map<Integer, String> singleHeadMap = Maps.newHashMap();
    /**
     * 错误信息sheet的表头
     */
    private final List<List<String>> errorMessageHead = new ArrayList<>();
    /**
     * 错误信息sheet的行数据
     */
    private final List<List<String>> errorMessages = new ArrayList<>();

    /**
     * 存储批量导入的数据 key:行号，value为行数据
     */
    private final LinkedHashMap<Integer, Map<Integer, String>> dataMapList = new LinkedHashMap<>(batchCount);
    /**
     * 存储批量导入的数据 key:行号，value为行数据转换后的VO对象
     */
    private final LinkedHashMap<Integer, V> dataList = new LinkedHashMap<>(batchCount);
    /**
     * 使用jackson进行转换行数据为VO对象
     */
    private static final ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .registerModule(new JavaTimeModule());
    /**
     * 消费导入数据 VO转为DO对象T
     */
    private final Consumer<List<T>> dataListProcessConsumer;
    /**
     * 用于构建VO对象的元信息
     */
    private Class<V> voClass;
    /**
     * 校验数据
     */
    private final Consumer<ExcelCheckContext<T>> checkFunction;
    /**
     * 自定义构建VO对象的函数
     */
    private BiFunction<Map<Integer, String>, Map<Integer, String>, V> mapToVOFunction;
    /**
     * 自定义转换方法将VO对象转为DO对象
     */
    private final Function<LinkedHashMap<Integer, V>, LinkedHashMap<Integer, T>> convertFunction;

    /**
     * 通过class进行映射，使用jsonProperty
     */
    public EasyExcelBatchImportListener(Consumer<List<T>> dataListProcessConsumer, Class<V> voClass,
                                        Consumer<ExcelCheckContext<T>> checkFunction,
                                        Function<LinkedHashMap<Integer, V>, LinkedHashMap<Integer, T>> convertFunction) {
        this(dataListProcessConsumer, null, checkFunction, voClass, convertFunction);
    }

    /**
     * 通过自定义函数进行映射
     */
    public EasyExcelBatchImportListener(Consumer<List<T>> dataListProcessConsumer,
                                        Consumer<ExcelCheckContext<T>> checkFunction,
                                        BiFunction<Map<Integer, String>, Map<Integer, String>, V> mapToVOFunction,
                                        Function<LinkedHashMap<Integer, V>, LinkedHashMap<Integer, T>> convertFunction) {
        this(dataListProcessConsumer, mapToVOFunction, checkFunction, null, convertFunction);
    }

    private EasyExcelBatchImportListener(Consumer<List<T>> dataListProcessConsumer,
                                         BiFunction<Map<Integer, String>, Map<Integer, String>, V> mapToVOFunction,
                                         Consumer<ExcelCheckContext<T>> checkFunction,
                                         Class<V> voClass,
                                         Function<LinkedHashMap<Integer, V>, LinkedHashMap<Integer, T>> convertFunction) {
        this.voClass = voClass;
        this.dataListProcessConsumer = dataListProcessConsumer;
        this.checkFunction = checkFunction;
        this.mapToVOFunction = mapToVOFunction;
        this.convertFunction = convertFunction;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        V obj = null;
        try {
            if (mapToVOFunction != null) {
                // 自定义转换方法
                obj = mapToVOFunction.apply(singleHeadMap, data);
            } else {
                // 通过jackson进行映射
                obj = buildUserModel(data, voClass);
            }
        } catch (Exception e) {
            int rowIndex = getRowIndex(context);
            log.error("Error building user model from data: rowIndex={}, data={}", rowIndex, data, e);
            buildErrorMessage(data, rowIndex, truncateString(e.getMessage()));
            return;
        }
        dataList.put(getRowIndex(context), obj);
        dataMapList.put(getRowIndex(context), data);
        if (dataList.size() >= batchCount) {
            tryConsumeDateList();
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        singleHeadMap = headMap;
        buildErrorMessageHeadMap(headMap);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("Error during Excel analysis: rowIndex={}, error={}", getRowIndex(context), exception.getMessage(), exception);
        buildTerminateErrorMessage(getRowIndex(context), exception.getMessage());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!dataList.isEmpty()) {
            tryConsumeDateList();
        }
        log.info("all data analysed！");
    }

    /**
     * excel获取行号
     */
    private int getRowIndex(AnalysisContext context) {
        return context.readRowHolder().getRowIndex() + 1;
    }

    /**
     * 获取行号列表
     */
    private List<Integer> getRowIndexes(LinkedHashMap<Integer, V> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.keySet().stream().sorted().collect(Collectors.toList());
    }

    /**
     * 构建导入失败sheet的列名
     */
    private void buildErrorMessageHeadMap(Map<Integer, String> headMap) {
        // 最大的列索引，然后添加一列错误信息
        errorMessageHead.add(Collections.singletonList("行号"));
        List<List<String>> originHead = headMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(e -> Collections.singletonList(e.getValue())).collect(Collectors.toList());
        errorMessageHead.addAll(originHead);
        errorMessageHead.add(Collections.singletonList("失败原因"));
    }

    private void buildErrorMessage(Map<Integer, String> rowData, int rowIndex, String errorMessage) {
        // rowData转为List<String>, 然后添加错误信息
        List<String> rowDataList = new ArrayList<>();
        rowDataList.add(String.valueOf(rowIndex));
        List<String> rowContent = rowData.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(Map.Entry::getValue).collect(Collectors.toList());
        rowDataList.addAll(rowContent);
        rowDataList.add(errorMessage);
        errorMessages.add(rowDataList);
    }

    /**
     * 构建批量消费失败的错误信息
     */
    private void buildErrorMessage(Map<Integer, Map<Integer, String>> dataMapList, String errorMessage) {
        for (Map.Entry<Integer, Map<Integer, String>> entry : dataMapList.entrySet()) {
            buildErrorMessage(entry.getValue(), entry.getKey(), errorMessage);
        }
    }

    /**
     * check后的错误信息
     *
     * @param errorInfo key为行号，value为错误信息
     */
    private void buildErrorMessage(Map<Integer, String> errorInfo) {
        if (CollectionUtils.isEmpty(errorInfo)) {
            return;
        }
        errorInfo.forEach((rowIndex, errorMessage) -> {
            buildErrorMessage(dataMapList.get(rowIndex), rowIndex, errorMessage);
        });
    }

    /**
     * 解析终止时，构建错误信息
     */
    private void buildTerminateErrorMessage(int rowIndex, String errorMessage) {
        List<String> rowDataList = new ArrayList<>();
        rowDataList.add(String.valueOf(rowIndex));
        rowDataList.add(errorMessage);
        errorMessages.add(rowDataList);

    }

    /**
     * 截取字符串到指定长度，如果字符串为空或长度不超过限制则返回原字符串
     */
    private String truncateString(String originalStr) {
        // 字符串为空或长度不超过限制时直接返回
        if (StringUtils.isBlank(originalStr) || originalStr.length() <= 200) {
            return originalStr;
        }
        return originalStr.substring(0, 200);
    }

    /**
     * 使用jackson进行转换行数据为VO对象
     */
    private V buildUserModel(Map<Integer, String> rowValue, Class<V> tclass) {
        Map<String, String> map = new HashMap<>();
        for (Map.Entry<Integer, String> entry : rowValue.entrySet()) {
            Integer index = entry.getKey();
            String value = entry.getValue();
            String key = singleHeadMap.get(index);
            if (key != null) {
                map.put(key, value);
            }
        }
        return mapper.convertValue(map, tclass);
    }

    /**
     * 批量消费数据
     */
    private void tryConsumeDateList() {
        List<Integer> rowIndexes = getRowIndexes(dataList);
        try {
            LinkedHashMap<Integer, T> rows = convertFunction.apply(dataList);
            ExcelCheckContext<T> checkContext = new ExcelCheckContext<>(rows);
            checkFunction.accept(checkContext);
            LinkedHashMap<Integer, String> errorMap = checkContext.getErrorMap();
            buildErrorMessage(errorMap);
            Map<Integer, T> afterCheckedDataMap = checkContext.getDataMap();
            if (CollectionUtils.isNotEmpty(afterCheckedDataMap)) {
                dataListProcessConsumer.accept(new ArrayList<>(afterCheckedDataMap.values()));
            }
        } catch (Exception e) {
            log.error("批量消费导入数据失败，失败行{}", rowIndexes, e);
            buildErrorMessage(dataMapList, truncateString(e.getMessage()));
        } finally {
            dataList.clear();
            dataMapList.clear();
        }
    }


    /**
     * 构建全部失败信息的sheet
     */
    public EasyExcelExportStringSheetExtract buildErrorSheet() {
        // 按照列顺序排序
        errorMessages.sort(Comparator.comparing(row -> Integer.parseInt(row.get(0))));
        return new EasyExcelExportStringSheetExtract(errorMessageHead, errorMessages, 1, "导入失败数据条目");
    }
}
