package com.jd.kf.oss.performance.app.controller.dto.request.performancetarget;

import com.jd.registry.annotation.NotEmpty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;
import java.util.List;

@Data
public class BaseExportRequest {
    /**
     * 绩效月
     */
    @ApiModelProperty("绩效月，格式：yyyy-MM")
    @NotNull(message = "绩效月不能为空")
    private YearMonth period;


    /**
     * 绩效组id
     */
    @ApiModelProperty("绩效组id")
    @NotEmpty(message = "绩效组id不能为空")
    private List<String> businessLineIds;
}
