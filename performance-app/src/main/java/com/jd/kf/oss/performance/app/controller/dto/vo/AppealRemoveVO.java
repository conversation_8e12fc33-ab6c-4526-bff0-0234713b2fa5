package com.jd.kf.oss.performance.app.controller.dto.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 无效数据剔除VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("无效数据剔除VO")
@Data
public class AppealRemoveVO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", example = "2025-07")
    private String period;

    /** 指标名称 */
    @ApiModelProperty(value = "指标名称", example = "事件满意度")
    private String kpiName;

    /** 需剔除的业务单号 */
    @ApiModelProperty(value = "需剔除的业务单号", example = "271595782")
    private BigDecimal ticketId;

    /** 租户标识 */
    @ApiModelProperty(value = "租户标识", example = "tenant001")
    private String tenantCode;

    /** 更新人 */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String editor;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "2025-07-02T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;
}
