package com.jd.kf.oss.performance.app.consumer.mq.processor;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.WaveEntry;
import com.jd.kf.oss.performance.app.consumer.config.TaskDynamicConfig;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.ModelAdapter;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: songweijia1
 * @description: 单表数据订阅处理基类
 * @date: 2025/7/8
 */

@Slf4j
public abstract class SingleTableProcessor<T> {

    protected static final String OFFICIAL_TENANT_CODE_STR = "kf_perform_retail";
    protected static final String DEFAULT_CREATOR = "sys";

    @Resource
    protected TaskDynamicConfig taskDynamicConfig;

    /**
     * 处理数据库Binlog消息
     *
     * @param entryMessage 要处理的消息对象
     */
    public void process(EntryMessage entryMessage) {
        if (entryMessage == null) {
            log.warn("[{}] EntryMessage is null, skipping processing", getSyncTable());
            return;
        }

        try {
            // 初始化上下文
            UserContextHolder.initAndSetUserContext(OFFICIAL_TENANT_CODE_STR, DEFAULT_CREATOR);

            // 处理消息
            TableProcessResult<T> result = processEntryMessage(entryMessage);

            // 记录处理结果统计
            logProcessingStatistics(result);

            // 持久化数据
            if (shouldPersist()) {
                persistDataWithTransaction(result);
            } else {
                log.info("[{}] Persistence is disabled, skipping data persistence", getSyncTable());
            }

        } catch (Exception e) {
            log.error("[{}] Failed to process message, table: {}, error: {}",
                    getSyncTable(), entryMessage.getHeader().getTableName(), e.getMessage(), e);
            throw new RuntimeException("处理消息失败: " + e.getMessage(), e);
        } finally {
            UserContextHolder.remove();
        }
    }

    /**
     * 记录处理结果统计信息
     */
    private void logProcessingStatistics(TableProcessResult<T> result) {
        if (log.isInfoEnabled()) {
            log.info("[{}] Processing completed - Total: {}, ToUpsert: {}, ToDelete: {}",
                    getSyncTable(),
                    result.getTotal().size(),
                    result.getToUpsert().size(),
                    result.getToDelete().size());
        }
    }

    /**
     * 带事务的数据持久化处理
     */
    private void persistDataWithTransaction(TableProcessResult<T> result) {
        try {
            // 先处理常规的插入/更新操作
            if (result.hasUpsertData()) {
                persistData(result.getToUpsert());
                log.debug("[{}] Persisted {} records", getSyncTable(), result.getToUpsert().size());
            }

            // 再处理特殊的删除数据
            if (result.hasToDeleteData()) {
                logisticRemoveData(result.getToDelete());
                log.debug("[{}] Processed {} delete records", getSyncTable(), result.getToDelete().size());
            }
        } catch (Exception e) {
            log.error("[{}] Failed to persist data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("数据持久化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理EntryMessage
     */
    private TableProcessResult<T> processEntryMessage(EntryMessage entryMessage) {
        TableProcessResult<T> result = new TableProcessResult<>();

        WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
        List<WaveEntry.RowData> rowDataList = entryMessage.getRowChange().getRowDatasList();

        if (CollectionUtils.isEmpty(rowDataList)) {
            log.warn("[{}] No row data found in message", getSyncTable());
            return result;
        }

        // 批量处理行数据
        for (WaveEntry.RowData rowData : rowDataList) {
            try {
                processRowData(rowData, eventType, result);
            } catch (Exception e) {
                log.error("[{}] Failed to process row data for event type: {}, error: {}",
                        getSyncTable(), eventType, e.getMessage(), e);
                // 继续处理其他行数据，不因单行失败而中断整个批次
            }
        }

        // 处理转换后的数据
        if (result.hasAnyData()) {
            processWithExistsData(result);
        } else {
            log.info("[{}] No valid data to process after conversion", getSyncTable());
        }

        return result;
    }

    /**
     * 处理单行数据
     */
    private void processRowData(WaveEntry.RowData rowData, WaveEntry.EventType eventType, TableProcessResult<T> result) {
        Map<String, String> dataMap = getDataMap(rowData, eventType);

        if (log.isInfoEnabled()) {
            log.info("[{}] [{}] Processing row data: {}", getSyncTable(), eventType, JSON.toJSONString(dataMap));
        }

        // 数据转换
        T data = convertToModelData(eventType, dataMap);
        if (data != null) {
            result.addToTotal(data);
        } else {
            log.warn("[{}] Failed to convert data for event type: {}, data: {}",
                    getSyncTable(), eventType, JSON.toJSONString(dataMap));
        }
    }

    protected Map<String, String> getDataMap(WaveEntry.RowData rowData, WaveEntry.EventType eventType) {
        List<WaveEntry.Column> columns = eventType == WaveEntry.EventType.DELETE ?
                rowData.getBeforeColumnsList() :
                rowData.getAfterColumnsList();

        return columns.stream()
                .collect(Collectors.toMap(
                        WaveEntry.Column::getName,
                        WaveEntry.Column::getValue,
                        (a, b) -> a
                ));
    }

    /**
     * 处理数据的通用流程
     */
    protected void processWithExistsData(TableProcessResult<T> result) {
        ModelAdapter<T> modelAdapter = getModelAdapter();
        
        // 1. 获取业务主键列表并去重
        List<String> businessKeys = extractBusinessKeys(result.getTotal(), modelAdapter);
        if (CollectionUtils.isEmpty(businessKeys)) {
            log.warn("[{}] No valid business keys found, skipping data processing", getSyncTable());
            return;
        }

        // 2. 批量获取现有数据
        Map<String, T> existingDataMap = getExistingDataMap(businessKeys);

        // 3. 批量处理数据
        int processedCount = 0;
        for (T newData : result.getTotal()) {
            try {
                String businessKey = modelAdapter.getBusinessKey(newData);
                if (StringUtils.isBlank(businessKey)) {
                    log.warn("[{}] Business key is blank for data {}, skipping", getSyncTable(), JSON.toJSONString(newData));
                    continue;
                }

                T existingData = existingDataMap.get(businessKey);
                processOneData(newData, existingData, result, modelAdapter);
                processedCount++;
            } catch (Exception e) {
                log.error("[{}] Failed to process single data record: {}", getSyncTable(), e.getMessage(), e);
                // 继续处理其他数据
            }
        }

        log.info("[{}] Processed {} out of {} records", getSyncTable(), processedCount, result.getTotal().size());
    }

    /**
     * 处理单条数据的通用逻辑
     */
    protected void processOneData(T newData, T existingData, TableProcessResult<T> result, ModelAdapter<T> modelAdapter) {
        if (existingData == null) {
            // 新增数据处理
            handleNewData(newData, result, modelAdapter);
        } else {
            // 更新数据处理
            handleUpdateData(newData, existingData, result, modelAdapter);
        }
    }

    /**
     * 处理新增数据
     */
    private void handleNewData(T newData, TableProcessResult<T> result, ModelAdapter<T> modelAdapter) {
        Boolean isValid = modelAdapter.getYn(newData);

        if (Boolean.FALSE.equals(isValid)) {
            // 如果新数据标记为删除，则设置为有效并加入删除队列
            modelAdapter.setYn(newData, Boolean.TRUE);
            result.addToUpsert(newData);
            result.addToDelete(newData);
        } else if (Boolean.TRUE.equals(isValid)) {
            // 有效的新数据直接插入
            result.addToUpsert(newData);
        }
        // 如果isValid为null，则不处理
    }

    /**
     * 处理更新数据
     */
    private void handleUpdateData(T newData, T existingData, TableProcessResult<T> result, ModelAdapter<T> modelAdapter) {
        // 设置现有数据的ID
        Long existingId = modelAdapter.getId(existingData);
        modelAdapter.setId(newData, existingId);

        // 检查数据是否需要更新（基于修改时间）
        if (shouldUpdateData(existingData, newData, modelAdapter)) {
            // 如果新数据标记为删除，需要特殊处理
            if (Boolean.FALSE.equals(modelAdapter.getYn(newData))) {
                // 设置uniqId为主键ID，用于软删除的唯一索引处理
                result.addToDelete(newData);
            }
            result.addToUpsert(newData);
        }
    }

    /**
     * 判断是否需要更新数据
     * 基于修改时间比较，只有新数据的修改时间晚于现有数据时才更新
     */
    private boolean shouldUpdateData(T existingData, T newData, ModelAdapter<T> modelAdapter) {
        LocalDateTime existingModified = modelAdapter.getModified(existingData);
        LocalDateTime newModified = modelAdapter.getModified(newData);

        // 如果任一时间为null，则进行更新（保守策略）
        if (existingModified == null || newModified == null) {
            return true;
        }

        return DateUtils.isEqualOrBefore(existingModified, newModified);
    }

    /**
     * 从给定列表中提取业务主键
     */
    protected List<String> extractBusinessKeys(List<T> dataList, ModelAdapter<T> modelAdapter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream()
                .filter(Objects::nonNull)
                .map(data -> {
                    try {
                        return modelAdapter.getBusinessKey(data);
                    } catch (Exception e) {
                        log.warn("[{}] Failed to extract business key from data: {}", getSyncTable(), e.getMessage());
                        return null;
                    }
                })
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    // ========== 抽象方法，子类需要实现 ==========

    /**
     * 获取模型适配器
     */
    protected abstract ModelAdapter<T> getModelAdapter();

    /**
     * 将WaveEntry.EventType类型和Map<String, String>数据转换为模型对象T。
     *
     * @param eventType WaveEntry.EventType类型的事件类型。
     * @param dataMap   包含转换所需数据的Map对象。
     * @return 转换后的模型对象T。
     */
    protected abstract T convertToModelData(WaveEntry.EventType eventType, Map<String, String> dataMap);

    /**
     * 获取指定业务键的现有数据映射。
     *
     * @param businessKeys 业务键列表
     * @return 现有数据的映射，键为业务键，值为对应的数据对象
     */
    protected abstract Map<String, T> getExistingDataMap(List<String> businessKeys);

    /**
     * 获取同步表枚举类型
     *
     * @return 同步表枚举类型
     */
    protected abstract String getSyncTable();

    /**
     * 是否需要持久化
     */
    protected abstract boolean shouldPersist();

    /**
     * 持久化数据
     */
    protected abstract void persistData(List<T> toUpsert);

    /**
     * 逻辑删除数据
     */
    protected abstract void logisticRemoveData(List<T> toDelete);

}
