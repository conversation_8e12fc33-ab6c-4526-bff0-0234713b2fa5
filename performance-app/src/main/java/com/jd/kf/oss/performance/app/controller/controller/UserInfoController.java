package com.jd.kf.oss.performance.app.controller.controller;

import com.alibaba.fastjson.JSON;
import com.jd.kf.oss.performance.app.controller.converter.UserInfoConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.userinfo.UserInfoPageRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.userinfo.UserInfoPageVO;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.UserAggregateService;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.UserComposite;
import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 员工信息管理 http 接口
 */
@Api("员工信息管理")
@RestController
@RequestMapping("/performance/user_info")
@Slf4j
public class UserInfoController {

    @Resource
    private UserAggregateService userAggregateService;

    /**
     * 查询员工信息列表（包含完整的部门路径信息）
     * @param request 包含分页参数和查询条件的请求对象，需通过校验
     * @return 包含分页员工信息数据的通用分页结果对象（包含部门路径信息）
     */
    @ApiOperation("查询员工信息列表")
    @PostMapping("/page")
    public ApiResult<CommonPage<UserInfoPageVO>> userInfoPageList(@RequestBody @Validated UserInfoPageRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-userInfoPageList");
        try {
            // 参数校验
            CheckUtil.notNull(request, "请求体不能为空");
            
            // 调用用户聚合服务进行多表关联查询（包含部门路径信息）
            CommonPage<UserComposite> commonPage = userAggregateService.queryUserPageInfo(
                    UserContextHolder.getTenantCode(),
                    request.getPeriod(),
                    request.getManagerErp(),
                    request.getDeptId(),
                    request.getBusinessLineId(),
                    request.getPlanCode(),
                    request.getErp(),
                    request.getName(),
                    request.getPage(),
                    request.getPageSize()
            );

            return ApiResult.success(UserInfoConverter.INSTANCE.pageComposite2PageVO(commonPage));
        } catch (Exception e) {
            log.error("userInfoPageList exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return ApiResult.illegal(e.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }



}
