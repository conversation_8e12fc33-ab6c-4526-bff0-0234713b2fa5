package com.jd.kf.oss.performance.app.controller.dto.vo.appeal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;



/**
 * 归属数据修改导出VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class AppealModifyExportVO {

    /** 绩效月 */
    @ExcelProperty(value = "绩效月", index = 0)
    @ColumnWidth(12)
    private String period;

    /** 需修改指标名称 */
    @ExcelProperty(value = "需修改指标名称", index = 1)
    @ColumnWidth(20)
    private String kpiName;

    /** 需修改单据ID */
    @ExcelProperty(value = "需修改单据ID", index = 2)
    @ColumnWidth(15)
    private String ticketId;

    /** 客服ERP */
    @ExcelProperty(value = "客服ERP", index = 3)
    @ColumnWidth(15)
    private String erp;

    /** 修改后绩效组ID */
    @ExcelProperty(value = "修改后技能组ID", index = 4)
    @ColumnWidth(18)
    private String skillId;
}
