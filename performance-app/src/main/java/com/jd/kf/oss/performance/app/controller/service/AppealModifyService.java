package com.jd.kf.oss.performance.app.controller.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.app.controller.converter.AppealModifyConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.ExportAppealModifyRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealModifyExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealModifyImportVO;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.AppealModifyDO;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.AppealModifyDomainService;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.app.controller.utils.EasyExcelBatchImportListener;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;

/**
 * <AUTHOR>
 * @date 2025/07/03
 */
@Service
@Slf4j
public class AppealModifyService {
    @Autowired
    private  AppealModifyDomainService appealModifyDomainService;

    @Autowired
    private DynamicConfig dynamicConfig;

    @Autowired
    private FileHelperService fileHelperService;

    @Autowired
    private UserDomainService userDomainService;

    /**
     * 导出归属数据修改
     * @param request 导出请求参数
     * @param httpServletRequest HTTP请求
     * @return OSS文件URL
     */
    public String exportAppealModify(ExportAppealModifyRequest request, HttpServletRequest httpServletRequest) {
        // 查询数据
        List<AppealModifyDO> appealModifyList = appealModifyDomainService.queryAppealModifyListByConditions(
                request.getPeriod(), request.getKpiName(),
                request.getSkillId(), request.getTicketId());
        // 校验行数
        fileHelperService.checkExportAmountLimit(appealModifyList);
        // 转换为导出VO
        List<AppealModifyExportVO> exportVOList = AppealModifyConverter.INSTANCE.doList2ExportVOList(appealModifyList);
        // 上传OSS并返回URL
        String fileName = request.getPeriod() + "-归属数据修改";
        return fileHelperService.uploadToOSS(httpServletRequest, fileName, AppealModifyExportVO.class, exportVOList);
    }


    /**
     * 导入归属数据修改
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     */
    public ApiResult<String> importAppealModify(String url, HttpServletRequest httpServletRequest) throws IOException {
        File excelFile = fileHelperService.downloadFile(url);
        try {
            fileHelperService.checkExcelFileRowCount(excelFile);
            return importModify(excelFile, httpServletRequest);
        } catch (Exception e) {
            log.error("导入归属数据修改失败, 文件行数校验失败{}", e.getMessage());
            throw new BizException(e.getMessage());
        } finally {
            fileHelperService.deleteFile(excelFile);
        }
    }



    /**
     * 导入归属数据修改
     * @param excelFile Excel文件
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     */
    public ApiResult<String> importModify(File excelFile, HttpServletRequest httpServletRequest) {
        Consumer<ExcelCheckContext<AppealModifyDO>> checkFunction = this::checkImportData;
        EasyExcelBatchImportListener<AppealModifyDO, AppealModifyImportVO> importHelper = getImportListener(checkFunction);
        EasyExcel.read(excelFile, importHelper).sheet().doRead();
        if (CollectionUtils.isEmpty(importHelper.getErrorMessages())) {
            return ApiResult.success();
        }
        String url = fileHelperService.uploadToOSS(httpServletRequest, "归属数据修改导入失败条目", importHelper.buildErrorSheet());
        return ApiResult.error(url);
    }

    private EasyExcelBatchImportListener<AppealModifyDO, AppealModifyImportVO> getImportListener(Consumer<ExcelCheckContext<AppealModifyDO>> checkFunction) {
        Consumer<List<AppealModifyDO>> saveFunction = appealModifyDomainService::saveOrUpdateBatch;
        Function<LinkedHashMap<Integer, AppealModifyImportVO>, LinkedHashMap<Integer, AppealModifyDO>> convertFunction =
            voMap -> {
                LinkedHashMap<Integer, AppealModifyDO> result = new LinkedHashMap<>();
                voMap.forEach((k, v) -> result.put(k, AppealModifyConverter.INSTANCE.importVO2DO(v)));
                return result;
            };
        EasyExcelBatchImportListener<AppealModifyDO, AppealModifyImportVO> importHelper =
            new EasyExcelBatchImportListener<>(saveFunction, AppealModifyImportVO.class, checkFunction, convertFunction);
        return importHelper;
    }

    private void checkImportData(ExcelCheckContext<AppealModifyDO> context) {
        Consumer<AppealModifyDO> checkFunction = AppealModifyDO::validateForSave;
        checkModifyExist(context, checkFunction);
    }

    /**
     * @param context
     * @param checkFunction
     * 检查该绩效月客服是否存在
     */
    private void checkModifyExist(ExcelCheckContext<AppealModifyDO> context, Consumer<AppealModifyDO> checkFunction) {
        Map<Integer, AppealModifyDO> dataMap = context.getDataMap();
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();

        // 按period分组收集erp列表
        Map<String, List<String>> periodToErpsMap = new HashMap<>();
        dataMap.forEach((k, v) -> {
            if (StringUtils.isNotBlank(v.getPeriod()) && StringUtils.isNotBlank(v.getErp())) {
                periodToErpsMap.computeIfAbsent(v.getPeriod(), period -> new ArrayList<>()).add(v.getErp());
            }
        });

        // 构建ERP+period组合键到UserDO的映射
        Map<String, UserDO> erpPeriodToUserMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : periodToErpsMap.entrySet()) {
            String period = entry.getKey();
            List<String> erps = entry.getValue();

            List<UserDO> users = userDomainService.getUsersByErp(erps, period);
            for (UserDO user : users) {
                String key = user.getErp() + "_" + user.getPeriod();
                erpPeriodToUserMap.put(key, user);
            }
        }

        Iterator<Map.Entry<Integer, AppealModifyDO>> iterator = dataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, AppealModifyDO> entry = iterator.next();
            Integer rowNO = entry.getKey();
            AppealModifyDO modifyDO = entry.getValue();
            try {
                // 构建ERP+period组合键进行查找
                String key = modifyDO.getErp() + "_" + modifyDO.getPeriod();
                if (!erpPeriodToUserMap.containsKey(key)) {
                    errorMap.put(rowNO, "该客服在指定绩效月不存在");
                    iterator.remove();
                    continue;
                }
                checkFunction.accept(modifyDO);
            } catch (IllegalArgumentException e) {
                errorMap.put(rowNO, e.getMessage());
                iterator.remove();
            }
        }
    }
}
