package com.jd.kf.oss.performance.app.controller.dto.request.performancetarget;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class TargetPageRequest extends BasePageRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 绩效月
     */
    @ApiModelProperty("绩效月")
    @Pattern(regexp = "\\d{4}-\\d{2}", message = "绩效月格式必须为 yyyy-MM")
    @NotBlank(message = "绩效月不能为空")
    private String period;


    /**
     *绩效组Id
     */
    @ApiModelProperty("绩效组Id")
    private String businessLineId;

    /**
     *绩效组名称
     */
    @ApiModelProperty("绩效组名称")
    private String businessLineName;

    /**
     *绩效组名称
     */
    @ApiModelProperty("绩效方案")
    private String planCode;
}
