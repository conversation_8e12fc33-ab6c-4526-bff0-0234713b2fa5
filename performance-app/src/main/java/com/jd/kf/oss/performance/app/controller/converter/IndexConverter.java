package com.jd.kf.oss.performance.app.controller.converter;


import com.jd.kf.oss.performance.app.controller.dto.request.index.IndexBasicPageRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.index.IndexItem;
import com.jd.kf.oss.performance.app.controller.dto.vo.index.IndexPageBasicVO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface IndexConverter {
    IndexConverter INSTANCE = Mappers.getMapper(IndexConverter.class);

    CommonPage<IndexPageBasicVO> pageDO2PageVO(CommonPage<IndexDO> commonPage);

    IndexDO request2DO(IndexBasicPageRequest request);

    List<IndexItem> DO2IndexItem(List<IndexDO> indexDOs);
}
