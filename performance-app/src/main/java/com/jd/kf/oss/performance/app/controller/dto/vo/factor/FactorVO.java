package com.jd.kf.oss.performance.app.controller.dto.vo.factor;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 因子
 */
@Data
public class FactorVO {
    /**
     * ID
     */
    private Long id;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    private String type;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 公式展示信息
     */
    private String formulaDisplayInfo;

    /**
     * 公式描述信息
     */
    private String displayInfo;

    /**
     * code
     */
    private String code;

    /**
     * 租户标识
     */
    private String tenantCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String editor;

    /**
     * 修改人
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}
