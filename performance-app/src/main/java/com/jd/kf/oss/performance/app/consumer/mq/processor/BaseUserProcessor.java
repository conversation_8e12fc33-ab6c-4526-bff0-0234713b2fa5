package com.jd.kf.oss.performance.app.consumer.mq.processor;

import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.WaveEntry;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.ModelAdapter;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.WfcUserAdapter;
import com.jd.kf.oss.performance.app.consumer.mq.processor.adapter.WfcUserExtAdapter;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.enums.SyncTableEnum;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserExtPO;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcUserExtPOService;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcUserPOService;
import com.jd.kf.oss.performance.utils.MapHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: songweijia1
 * @description: 优化版WfcUser和WfcUserExt多表处理器
 * @date: 2025/7/8
 */

@Slf4j
@Component
public class BaseUserProcessor extends MultiTableProcessor<WfcUserPO, WfcUserExtPO> {

    @Resource
    private IWfcUserPOService wfcUserPOService;

    @Resource
    private IWfcUserExtPOService wfcUserExtPOService;

    @Resource
    private WfcUserAdapter wfcUserAdapter;

    @Resource
    private WfcUserExtAdapter wfcUserExtAdapter;

    @Override
    protected ModelAdapter<WfcUserPO> getMainModelAdapter() {
        return wfcUserAdapter;
    }

    @Override
    protected ModelAdapter<WfcUserExtPO> getExtModelAdapter() {
        return wfcUserExtAdapter;
    }

    @Override
    protected WfcUserPO convertToMainData(WaveEntry.EventType eventType, Map<String, String> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            log.warn("[{}] Data map is null or empty for main table conversion, event type: {}", getSyncTable(), eventType);
            return null;
        }

        try {
            switch (eventType) {
                case INSERT:
                case UPDATE:
                    return buildWfcUserPO(dataMap, false);
                case DELETE:
                    return buildWfcUserPO(dataMap, true);
                default:
                    log.warn("[{}] Unhandled event type for main table: {}", getSyncTable(), eventType);
                    return null;
            }
        } catch (Exception e) {
            log.error("[{}] Failed to convert main table data for event type: {}, error: {}",
                    getSyncTable(), eventType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    protected WfcUserExtPO convertToExtData(WaveEntry.EventType eventType, Map<String, String> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            log.warn("[{}] Data map is null or empty for ext table conversion, event type: {}", getSyncTable(), eventType);
            return null;
        }

        try {
            switch (eventType) {
                case INSERT:
                case UPDATE:
                    return buildWfcUserExtPO(dataMap, false);
                case DELETE:
                    return buildWfcUserExtPO(dataMap, true);
                default:
                    log.warn("[{}] Unhandled event type for ext table: {}", getSyncTable(), eventType);
                    return null;
            }
        } catch (Exception e) {
            log.error("[{}] Failed to convert ext table data for event type: {}, error: {}",
                    getSyncTable(), eventType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    protected Map<String, WfcUserPO> getExistingMainDataMap(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            return Collections.emptyMap();
        }
        
        try {
            return wfcUserPOService.getWfcUserListByErps(businessKeys)
                    .stream()
                    .collect(Collectors.toMap(WfcUserPO::getErp, Function.identity()));
        } catch (Exception e) {
            log.error("[{}] Failed to get existing main data: {}", getSyncTable(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    protected Map<String, WfcUserExtPO> getExistingExtDataMap(List<String> businessKeys) {
        if (CollectionUtils.isEmpty(businessKeys)) {
            return Collections.emptyMap();
        }
        
        try {
            return wfcUserExtPOService.getWfcUserExtListByErps(businessKeys)
                    .stream()
                    .collect(Collectors.toMap(WfcUserExtPO::getErp, Function.identity()));
        } catch (Exception e) {
            log.error("[{}] Failed to get existing ext data: {}", getSyncTable(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    protected String getSyncTable() {
        return SyncTableEnum.TABLE_HR_BASE_USER.getTableName();
    }

    @Override
    protected boolean shouldPersist() {
        return taskDynamicConfig.isPersistentWfcWaiterInfo();
    }

    @Override
    protected void persistMainData(List<WfcUserPO> toUpsert) {
        if (CollectionUtils.isEmpty(toUpsert)) {
            log.debug("[{}] No main data to persist", getSyncTable());
            return;
        }

        try {
            wfcUserPOService.batchUpsertUser(toUpsert);
            log.info("[{}] Successfully persisted {} main records", getSyncTable(), toUpsert.size());
        } catch (Exception e) {
            log.error("[{}] Error persisting main data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("持久化用户主表数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void persistExtData(List<WfcUserExtPO> toUpsert) {
        if (CollectionUtils.isEmpty(toUpsert)) {
            log.debug("[{}] No ext data to persist", getSyncTable());
            return;
        }

        try {
            wfcUserExtPOService.batchUpsertUserExt(toUpsert);
            log.info("[{}] Successfully persisted {} ext records", getSyncTable(), toUpsert.size());
        } catch (Exception e) {
            log.error("[{}] Error persisting ext data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("持久化用户扩展表数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void logisticRemoveMainData(List<WfcUserPO> toDelete) {
        if (CollectionUtils.isEmpty(toDelete)) {
            log.debug("[{}] No main data to logically delete", getSyncTable());
            return;
        }

        try {
            wfcUserPOService.batchRemoveUserById(toDelete);
            log.info("[{}] Successfully logically deleted {} main records", getSyncTable(), toDelete.size());
        } catch (Exception e) {
            log.error("[{}] Error logically deleting main data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("逻辑删除用户主表数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void logisticRemoveExtData(List<WfcUserExtPO> toDelete) {
        if (CollectionUtils.isEmpty(toDelete)) {
            log.debug("[{}] No ext data to logically delete", getSyncTable());
            return;
        }

        try {
            wfcUserExtPOService.batchRemoveUserExtById(toDelete);
            log.info("[{}] Successfully logically deleted {} ext records", getSyncTable(), toDelete.size());
        } catch (Exception e) {
            log.error("[{}] Error logically deleting ext data: {}", getSyncTable(), e.getMessage(), e);
            throw new RuntimeException("逻辑删除用户扩展表数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建WfcUserPO对象
     */
    private WfcUserPO buildWfcUserPO(Map<String, String> dataMap, boolean isDelete) {
        // 数据校验
        String erp = MapHelper.getMapString(dataMap, "user_pin");
        if (StringUtils.isBlank(erp)) {
            log.warn("[{}] ERP is blank for main table, skipping conversion, data: {}", 
                    getSyncTable(), JSON.toJSONString(dataMap));
            return null;
        }

        WfcUserPO wfcUser = new WfcUserPO();
        String tenantCode = UserContextHolder.getTenantCode();

        // 设置业务字段
        wfcUser.setErp(erp);
        wfcUser.setName(MapHelper.getMapString(dataMap, "user_name"));
        wfcUser.setEmail(MapHelper.getMapString(dataMap, "email"));
        wfcUser.setBizDeptId(MapHelper.getMapString(dataMap, "org_id"));
        wfcUser.setBusinessLineId(MapHelper.getMapString(dataMap, "line_id"));
        wfcUser.setBizTenantCode(MapHelper.getMapString(dataMap, "tenant_code"));

        // 设置系统字段
        wfcUser.setTenantCode(tenantCode);
        wfcUser.setUniqId(0L); // 默认为0，删除时会在处理逻辑中设置为主键ID

        // 设置有效性标志
        if (isDelete) {
            wfcUser.setYn(Boolean.FALSE);
        } else {
            Integer isDeleteFlag = MapHelper.getMapInt(dataMap, "is_delete");
            wfcUser.setYn(isDeleteFlag == null || isDeleteFlag == 0);
        }

        // 设置审计字段
        setAuditFields(wfcUser, dataMap);

        return wfcUser;
    }

    /**
     * 构建WfcUserExtPO对象
     */
    private WfcUserExtPO buildWfcUserExtPO(Map<String, String> dataMap, boolean isDelete) {
        // 数据校验
        String erp = MapHelper.getMapString(dataMap, "user_pin");
        if (StringUtils.isBlank(erp)) {
            log.warn("[{}] ERP is blank for ext table, skipping conversion, data: {}", 
                    getSyncTable(), JSON.toJSONString(dataMap));
            return null;
        }

        WfcUserExtPO wfcUserExt = new WfcUserExtPO();
        String tenantCode = UserContextHolder.getTenantCode();

        // 设置业务字段
        wfcUserExt.setErp(erp);
        wfcUserExt.setBizTenantCode(MapHelper.getMapString(dataMap, "tenant_code"));
        wfcUserExt.setPositionName(MapHelper.getMapString(dataMap, "work_position"));
        wfcUserExt.setEmploymentNature(MapHelper.getMapString(dataMap, "employment_nature"));
        wfcUserExt.setPersonnelState(MapHelper.getMapInt(dataMap, "personnel_state"));
        wfcUserExt.setEntryTime(MapHelper.getMapLocalDateTime(dataMap, "entry_time"));
        wfcUserExt.setQuitTime(MapHelper.getMapLocalDateTime(dataMap, "quit_time"));
        wfcUserExt.setProdStatus(MapHelper.getMapString(dataMap, "biz_state"));
        wfcUserExt.setPreQuitTime(MapHelper.getMapLocalDateTime(dataMap, "last_attendance_day"));
        wfcUserExt.setOnlineTime(MapHelper.getMapLocalDateTime(dataMap, "online_time"));

        // 设置系统字段
        wfcUserExt.setTenantCode(tenantCode);
        wfcUserExt.setUniqId(0L); // 默认为0，删除时会在处理逻辑中设置为主键ID

        // 设置有效性标志
        if (isDelete) {
            wfcUserExt.setYn(Boolean.FALSE);
        } else {
            Integer isDeleteFlag = MapHelper.getMapInt(dataMap, "is_delete");
            wfcUserExt.setYn(isDeleteFlag == null || isDeleteFlag == 0);
        }

        // 设置审计字段
        setAuditFields(wfcUserExt, dataMap);

        return wfcUserExt;
    }

    /**
     * 设置审计字段的通用方法
     */
    private void setAuditFields(Object entity, Map<String, String> dataMap) {
        LocalDateTime now = LocalDateTime.now();

        if (entity instanceof WfcUserPO) {
            WfcUserPO user = (WfcUserPO) entity;
            // 创建时间和创建人
            user.setCreated(
                    Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "create_time"))
                            .orElse(now)
            );
            user.setCreator(
                    Optional.ofNullable(MapHelper.getMapString(dataMap, "create_user"))
                            .filter(StringUtils::isNotBlank)
                            .orElse(DEFAULT_CREATOR)
            );

            // 修改时间和修改人
            user.setModified(
                    Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "update_time"))
                            .orElse(now)
            );
            user.setEditor(
                    Optional.ofNullable(MapHelper.getMapString(dataMap, "update_user"))
                            .filter(StringUtils::isNotBlank)
                            .orElse(DEFAULT_CREATOR)
            );
        } else if (entity instanceof WfcUserExtPO) {
            WfcUserExtPO userExt = (WfcUserExtPO) entity;
            // 创建时间和创建人
            userExt.setCreated(
                    Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "create_time"))
                            .orElse(now)
            );
            userExt.setCreator(
                    Optional.ofNullable(MapHelper.getMapString(dataMap, "create_user"))
                            .filter(StringUtils::isNotBlank)
                            .orElse(DEFAULT_CREATOR)
            );

            // 修改时间和修改人
            userExt.setModified(
                    Optional.ofNullable(MapHelper.getMapLocalDateTime(dataMap, "update_time"))
                            .orElse(now)
            );
            userExt.setEditor(
                    Optional.ofNullable(MapHelper.getMapString(dataMap, "update_user"))
                            .filter(StringUtils::isNotBlank)
                            .orElse(DEFAULT_CREATOR)
            );
        }
    }

}
