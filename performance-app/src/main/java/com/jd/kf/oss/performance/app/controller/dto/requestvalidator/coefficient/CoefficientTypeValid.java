package com.jd.kf.oss.performance.app.controller.dto.requestvalidator.coefficient;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;


@Documented
@Constraint(validatedBy = CoefficientTypeValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CoefficientTypeValid {
    String message() default "";

    /**
     * 是否为空
     * false 为空校验，true 非空校验
     */
    boolean notNull() default false;
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

