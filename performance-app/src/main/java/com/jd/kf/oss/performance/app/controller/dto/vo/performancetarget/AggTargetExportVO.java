package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 聚合target和指标的导出VO
 */
@Data
public class AggTargetExportVO {

    /**
     * 绩效月
     */
    @ExcelProperty(value = "绩效月", index = 0)
    private String period;
    /**
     * 绩效组id
     */
    @ExcelProperty(value = "绩效组ID", index = 1)
    private String businessLineId;
    /**
     * 绩效组名称
     */
    @ExcelProperty(value = "绩效组名称", index = 2)
    private String businessLineName;
    /**
     * 考核方案
     */
    @ExcelProperty(value = "关联绩效方案", index = 3)
    private String evaluationPlan;

    /**
     * 全部指标信息
     */
    private List<IndexExportVO> indexes;

    /**
     * index的模板名称，indexVO
     */
    public Map<String,IndexExportVO> getSortedIndexesByType(String indexType) {
        if (indexes == null || indexes.isEmpty()) {
            return Collections.emptyMap();
        }
        return indexes.stream().filter(index -> isSameIndexTemplate(index, indexType))
                .collect(Collectors.toMap(vo->
                        vo.getTemplate().getTemplateName(), // 使用模板名作为key
                        vo -> vo, // 保持IndexExportVO对象不变
                        (e, r) -> e)); // 如果有重复的key，保留第一个;
    }

    /**
     * 判断指标模板是否相同类型
     */
    private boolean isSameIndexTemplate(IndexExportVO index, String indexType) {
        if (index != null && index.getTemplate() != null) {
            return Objects.equals(indexType, index.getTemplate().getType());
        }
        return false;
    }
}
