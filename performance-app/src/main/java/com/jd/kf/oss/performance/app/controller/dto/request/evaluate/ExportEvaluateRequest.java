package com.jd.kf.oss.performance.app.controller.dto.request.evaluate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 导出评价数据请求参数
 */
@ApiModel("导出评价数据请求参数")
@Data
public class ExportEvaluateRequest {
    
    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", required = true, example = "2025-07")
    @NotBlank(message = "绩效月不能为空")
    private String period;
    
    /** 主管ERP列表，可选，为空时导出所有 */
    @ApiModelProperty(value = "主管ERP", example = "manager002")
    private String mangerErp;
}
