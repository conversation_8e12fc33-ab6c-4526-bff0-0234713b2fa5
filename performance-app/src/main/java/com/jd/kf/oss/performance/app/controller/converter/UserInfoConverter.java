package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.vo.userinfo.UserInfoPageVO;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.UserComposite;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息转换器
 */
@Mapper
public interface UserInfoConverter {
    
    UserInfoConverter INSTANCE = Mappers.getMapper(UserInfoConverter.class);

    /**
     * DO转VO
     */
    UserInfoPageVO do2VO(UserDO userDO);

    /**
     * DO列表转VO列表
     */
    List<UserInfoPageVO> doList2VOList(List<UserDO> userDOList);

    /**
     * UserComposite转VO
     * 从UserComposite的UserDO中提取信息，并使用DeptDO中的部门路径名称
     */
    @Mapping(source = "userDO.businessLineId", target = "businessLineId")
    @Mapping(source = "userDO.businessLineName", target = "businessLineName")
    @Mapping(source = "userDO.deptId", target = "deptId")
    @Mapping(source = "userDO.deptName", target = "deptName")
    @Mapping(source = "deptDO.deptPathName", target = "deptPathName")
    @Mapping(source = "userDO.managerErp", target = "managerErp")
    @Mapping(source = "userDO.erp", target = "erp")
    @Mapping(source = "userDO.period", target = "period")
    @Mapping(source = "userDO.name", target = "name")
    @Mapping(source = "userDO.evaluationPlan", target = "evaluationPlan")
    @Mapping(source = "userDO.editor", target = "editor")
    @Mapping(source = "userDO.modified", target = "modified")
    UserInfoPageVO composite2VO(UserComposite userComposite);

    /**
     * UserComposite列表转VO列表
     */
    List<UserInfoPageVO> compositeList2VOList(List<UserComposite> userCompositeList);

    /**
     * 分页DO转分页VO
     */
    default CommonPage<UserInfoPageVO> pageDO2PageVO(CommonPage<UserDO> commonPage) {
        List<UserInfoPageVO> voList = doList2VOList(commonPage.getData());
        return CommonPage.getCommonPage(commonPage.getPage(), commonPage.getSize(),
                                       commonPage.getTotal(), voList);
    }

    /**
     * 分页UserComposite转分页VO
     */
    default CommonPage<UserInfoPageVO> pageComposite2PageVO(CommonPage<UserComposite> commonPage) {
        List<UserInfoPageVO> voList = compositeList2VOList(commonPage.getData());
        return CommonPage.getCommonPage(commonPage.getPage(), commonPage.getSize(),
                                       commonPage.getTotal(), voList);
    }
}
