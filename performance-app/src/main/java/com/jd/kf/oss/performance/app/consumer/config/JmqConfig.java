package com.jd.kf.oss.performance.app.consumer.config;

import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: songweijia1
 * @description: JMQ动态配置
 * @date: 2025/7/2
 */

@Slf4j
@Data
@Component
public class JmqConfig {

    @LafValue("performance.wfc.waiter.info.sync.topic")
    private String wfcWaiterInfoSyncTopic = "kf_performance_waiter_info_binlog_sync_pre";

    @LafValue("performance.wfc.waiter.info.consume.thread.count")
    private Integer wfcWaiterInfoConsumeThreadCount = 32;

    @LafValue("performance.wfc.waiter.info.consume.queue.len")
    private Integer wfcWaiterInfoConsumeQueueLen = 1000;

    @LafValue("performance.wfc.waiter.info.consume.interval")
    private Long wfcWaiterInfoConsumeInterval = 1000L;

}
