package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.factor.FactorSaveRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.factor.FactorVO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FactorConverter {
    FactorConverter INSTANCE = Mappers.getMapper(FactorConverter.class);

    /**
     * 将DO转化为VO
     * @param factorDO
     * @return
     */
    FactorVO do2VO(FactorDO factorDO);

    /**
     * 将前端提交的保存对象转化为DO
     * @param request
     * @return
     */
    FactorDO do2DO(FactorSaveRequest request);
}
