package com.jd.kf.oss.performance.app.api;

import com.alibaba.fastjson.JSON;
import com.jd.kf.oss.performance.api.item.api.BusinessLineService;
import com.jd.kf.oss.performance.api.item.dto.BusinessLineDTO;
import com.jd.kf.oss.performance.api.item.dto.CommonPage;
import com.jd.kf.oss.performance.api.item.param.BusinessLinePageRequest;
import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContext;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.ApiResult;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Service
@Slf4j
public class BusinessLineServiceImpl implements BusinessLineService {
    @Resource
    private BusinessLineDomainService businessLineDomainService;
    @Resource
    private DynamicConfig dynamicConfig;

    /**
     * @param request
     * @return {@link ApiResult }<{@link CommonPage }<{@link BusinessLineDTO }>>
     * 分页查询绩效条线信息
     */
    @Override
    public CommonPage<BusinessLineDTO> queryBusinessLinePage(BusinessLinePageRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-queryBusinessLinePage");
        try {
            CheckUtil.notNull(request, "请求对象不能为空");
            CheckUtil.checkPageSize(request.getPageSize());
            if (!dynamicConfig.getTenantCodeMap().isEmpty() && dynamicConfig.getTenantCodeMap().containsKey(request.getTenantCode())) {
                request.setTenantCode(dynamicConfig.getTenantCodeMap().get(request.getTenantCode()));
            }
            UserContextHolder.initAndSetUserContext(request.getTenantCode(), SystemConstants.EDITOR_SYSTEM);
            com.jd.kf.oss.performance.utils.CommonPage<BusinessLineDO> commonPageDO = businessLineDomainService.queryBusinessLineByTenantAndEditorAndName(
                    request.getTenantCode(),
                    request.getEditor(),
                    request.getName(),
                    request.getBusinessLineIds(),
                    request.getPage(),
                    request.getPageSize());
            return convertDOPage2DTOPage(commonPageDO);

        } catch (Exception e) {
            log.error("queryBusinessLinePage exception, request:{}", JSON.toJSONString(request), e);
            Profiler.functionError(callerInfo);
            return null;
        } finally {
            UserContextHolder.remove();
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private CommonPage<BusinessLineDTO> convertDOPage2DTOPage(com.jd.kf.oss.performance.utils.CommonPage<BusinessLineDO> commonPageDO) {
        if (commonPageDO == null) {
            return null;
        }

        CommonPage<BusinessLineDTO> commonPageDTO = new CommonPage<>();
        commonPageDTO.setPage(commonPageDO.getPage());
        commonPageDTO.setTotal(commonPageDO.getTotal());
        commonPageDTO.setSize(commonPageDO.getSize());

        List<BusinessLineDTO> dtoList = new ArrayList<>();
        if (commonPageDO.getData() != null) {
            for (BusinessLineDO businessLineDO : commonPageDO.getData()) {
                BusinessLineDTO businessLineDTO = new BusinessLineDTO();
                businessLineDTO.setBusinessLineId(businessLineDO.getBusinessLineId());
                businessLineDTO.setName(businessLineDO.getName());
                dtoList.add(businessLineDTO);
            }
        }
        commonPageDTO.setData(dtoList);

        return commonPageDTO;
    }
}
