package com.jd.kf.oss.performance.app.consumer.mq;

import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.kf.oss.performance.app.consumer.config.TaskDynamicConfig;
import com.jd.kf.oss.performance.app.consumer.mq.processor.MultiTableProcessor;
import com.jd.kf.oss.performance.app.consumer.mq.processor.SingleTableProcessor;
import com.jd.kf.oss.performance.app.consumer.mq.processor.TableProcessorFactory;
import com.jd.kf.oss.performance.enums.SyncTableEnum;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: songweijia1
 * @description: 零售cchome系统客服基础信息&部门基础信息数据订阅消息监听
 * @date: 2025/7/2
 */

@Slf4j
public class WfcWaiterInfoListener implements MessageListener {

    private final MessageDeserialize<Message> deserialize = new JMQMessageDeserialize();

    @Resource
    private TaskDynamicConfig taskDynamicConfig;

    @Resource
    private TableProcessorFactory tableProcessorFactory;

    @Override
    public void onMessage(List<Message> messageList) throws Exception {
        if (!taskDynamicConfig.isBuildWfcWaiterInfoOpen()
                || CollectionUtils.isEmpty(messageList)) {
            return;
        }
        processMessages(deserialize.deserialize(messageList));
    }

    private void processMessages(List<EntryMessage> entryMessages) {
        CallerInfo callerInfo = Profiler.registerInfo("performance.mq.wfcWaiterInfo");
        try {
            entryMessages.forEach(this::processEntryMessage);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            log.error("WfcWaiterInfoListener error, Failed to process messages: {}",
                    entryMessages, e);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private void processEntryMessage(EntryMessage entryMessage) {
        WaveEntry.EntryType entryType = entryMessage.getEntryType();
        if (isTransactionEntry(entryType)) {
            handleTransactionEntry(entryMessage);
            return;
        }

        String tableName = entryMessage.getHeader().getTableName();
        SyncTableEnum syncTableEnum = SyncTableEnum.getByTableName(tableName);

        if (syncTableEnum == null) {
            log.warn("WfcWaiterInfoListener getTableProcessor error, tableName:{}", tableName);
            return;
        }

        Object processor = tableProcessorFactory.getProcessor(tableName);

        if (processor == null) {
            return;
        }

        if (processor instanceof MultiTableProcessor) {
            ((MultiTableProcessor<?, ?>) processor).process(entryMessage);
        } else if (processor instanceof SingleTableProcessor) {
            ((SingleTableProcessor<?>) processor).process(entryMessage);
        }
    }

    private boolean isTransactionEntry(WaveEntry.EntryType entryType) {
        return entryType.equals(WaveEntry.EntryType.TRANSACTIONBEGIN)
                || entryType.equals(WaveEntry.EntryType.TRANSACTIONEND);
    }

    private void handleTransactionEntry(EntryMessage entryMessage) {
        if (entryMessage.getEntryType().equals(WaveEntry.EntryType.TRANSACTIONBEGIN)) {
            log.info("WfcWaiterInfoListener Transaction begin: {}", entryMessage.getHeader());
        } else {
            WaveEntry.TransactionEnd end = entryMessage.getEnd();
            log.info("WfcWaiterInfoListener Transaction end: {}, transaction id: {}", end, end.getTransactionId());
        }
    }

}