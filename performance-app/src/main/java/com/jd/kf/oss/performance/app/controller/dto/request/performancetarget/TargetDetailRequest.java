package com.jd.kf.oss.performance.app.controller.dto.request.performancetarget;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/06/25
 */
@Data
public class TargetDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 绩效月
     */
    @NotBlank(message = "绩效月不能为空")
    @ApiModelProperty("绩效月")
    @Pattern(regexp = "\\d{4}-\\d{2}", message = "绩效月格式必须为 yyyy-MM")
    private String period;

    /**
     *绩效组Id
     */
    @ApiModelProperty("绩效组Id")
    private String businessLineId;
}
