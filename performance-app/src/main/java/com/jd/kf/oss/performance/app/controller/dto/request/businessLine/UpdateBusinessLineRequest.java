package com.jd.kf.oss.performance.app.controller.dto.request.businessLine;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/06/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateBusinessLineRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效组id
     */
    @ApiModelProperty("绩效组id")
    private String businessLineId;

    /**
     * 绩效组名
     */
    @ApiModelProperty("绩效组名称")
    private String name;

    /**
     * 绩效组描述
     */
    @ApiModelProperty("绩效组描述")
    private String description;
}
