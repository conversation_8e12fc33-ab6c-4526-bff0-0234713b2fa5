package com.jd.kf.oss.performance.app.controller.converter;


import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientCreateReq;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientPageReq;
import com.jd.kf.oss.performance.app.controller.dto.request.coefficient.CoefficientUpdateReq;
import com.jd.kf.oss.performance.app.controller.dto.vo.coefficient.CoefficientDetailVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.coefficient.CoefficientPageVO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

@Mapper
public interface CoefficientConverter {
    CoefficientConverter INSTANCE = Mappers.getMapper(CoefficientConverter.class);

    CoefficientDO request2DO(CoefficientCreateReq request);


    CoefficientDO request2DO(CoefficientUpdateReq request);

    @Mapping(source = "period", target = "period", qualifiedByName = "yearMonthToString")
    CoefficientDO request2DO(CoefficientPageReq request);


    CoefficientDetailVO do2VO(CoefficientDO coefficientDO);


    CommonPage<CoefficientPageVO> pageDO2VO(CommonPage<CoefficientDO> pageDO);
    @Mapping(source="code",target = "code")
    CoefficientPageVO pageDO2VO(CoefficientDO coefficientDO);

    /**
     * 将YearMonth转换为String格式
     * @param yearMonth YearMonth对象
     * @return 格式化的字符串 (yyyy-MM)
     */
    @Named("yearMonthToString")
    default String yearMonthToString(YearMonth yearMonth) {
        if (yearMonth == null) {
            return null;
        }
        return yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }
}
