package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.enums.IndexTypeEnum;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.excel.EasyExcelExportStringSheetExtract;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 绩效目标导出工具类
 * 构建excel表头和行数据
 * 解析excel行数据为实体对象
 */
@Getter
@Setter
@NoArgsConstructor
public class AggTargetExcelUtil extends EasyExcelExportStringSheetExtract {

    private List<AggTargetExportVO> aggTargetList;
    /**
     * 表头数据行 target数量 产能指标数量  质量指标数量
     */
    private static final int targetNum = 1;
    private static final int capacityIndexNum = 4;
    private static final int qualityIndexNum = 4;

    /**
     * 表头名称模板 生成动态表头
     */
    private static final String TARGET_HEAD = "targetHead";
    private static final List<String> targetHeadsTemplate = Arrays.asList("绩效月", "绩效组ID", "绩效组名称", "关联绩效方案");
    private static final String CAPACITY_HEAD = "capacityHead";
    private static final List<String> capacityHeadsTemplate = Arrays.asList("产能指标", "产能权重", "产能考核开始日期", "产能考核结束日期");
    private static final String QUALITY_HEAD = "qualityHead";
    private static final List<String> qualityHeadsTemplate = Arrays.asList("质量指标", "质量权重", "质量考核开始日期", "质量考核结束日期", "样本量下限");
    // head convert 注册 提供列名
    private static final List<Supplier<List<List<String>>>> headSuppliers = Arrays.asList(
            AggTargetExcelUtil::initTargetHeads,
            AggTargetExcelUtil::initCapacityHeads,
            AggTargetExcelUtil::initQualityHeads
    );

    // 表格数据生成注册
    private static final List<Function<AggTargetExportVO, List<String>>> dataSuppliers = Arrays.asList(
            AggTargetExcelUtil::getTargetRowValue,
            AggTargetExcelUtil::getCapacityRowValue,
            AggTargetExcelUtil::getQualityRowValue
    );

    // 有序的数据解析注册
    private static final LinkedHashMap<String, Pair<Integer, BiConsumer<TargetComposite, List<String>>>> parseDataSuppliers = new LinkedHashMap<>();

    static {
        // 注册解析数据的方法 key为解析的类型  value pair对为解析占用的列数和解析方法
        parseDataSuppliers.put(TARGET_HEAD, Pair.of(targetNum * targetHeadsTemplate.size(), AggTargetExcelUtil::buildTargetDO));
        parseDataSuppliers.put(CAPACITY_HEAD, Pair.of(capacityIndexNum * capacityHeadsTemplate.size(), AggTargetExcelUtil::buildCapacityIndexDOs));
        parseDataSuppliers.put(QUALITY_HEAD, Pair.of(qualityIndexNum * qualityHeadsTemplate.size(), AggTargetExcelUtil::buildQualityIndexDOs));
    }

    /**
     * 入口方法，传入数据，生成excel
     */
    public static AggTargetExcelUtil generateSingleSheet(List<AggTargetExportVO> aggTargetList) {
        AggTargetExcelUtil aggTargetExportSheetExtract = new AggTargetExcelUtil();
        aggTargetExportSheetExtract.setAggTargetList(aggTargetList);
        return aggTargetExportSheetExtract;
    }

    /**
     * 将excel行数据转为 TargetComposite 对象
     */
    public TargetComposite parseTargetComposite(Map<Integer, String> headMap, Map<Integer, String> data) {
        if( CollectionUtils.isEmpty(data)) {
            throw new IllegalArgumentException("整行数据为空");
        }
        List<String> rowData = data.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).collect(Collectors.toList());
        checkHeadMapBeforeParse(headMap);
        return parseTargetComposite(rowData);
    }

    /**
     * 构建生成excel的表头
     */
    public List<List<String>> getHeads() {
        List<List<String>> heads = new ArrayList<>();
        headSuppliers.forEach(supplier -> {
            List<List<String>> columnNames = supplier.get();
            heads.addAll(columnNames);
        });
        return heads;
    }

    /**
     * 获取原始表头
     *
     * @return
     */
    public List<String> getRawHeads() {
        List<List<String>> heads = getHeads();
        return heads.stream().flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获取excel的行数据格式
     *
     */
    public List<List<String>> getDataList() {
        List<List<String>> dataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(aggTargetList)) {
            return dataList;
        }
        aggTargetList.forEach(vo -> {
            if(vo==null) {
                return;
            }
            List<String> rowValue = getRowValue(vo);
            dataList.add(rowValue);
        });
        return dataList;
    }

    /**
     * 将AggTargetExportVO转换为excel行数据
     *
     */
    public List<String> getRowValue(AggTargetExportVO vo) {
        List<String> rowValue = new ArrayList<>();
        for (Function<AggTargetExportVO, List<String>> supplier : dataSuppliers) {
            rowValue.addAll(supplier.apply(vo));
        }
        return rowValue;
    }

    /**
     * 获取目标行中的target相关的数据
     *
     */
    private static List<String> getTargetRowValue(AggTargetExportVO vo) {
        return Arrays.asList(vo.getPeriod(), vo.getBusinessLineId(), vo.getBusinessLineName(), vo.getEvaluationPlan());
    }

    /**
     * 生成全部的产能指标数据
     *
     */
    private static List<String> getCapacityRowValue(AggTargetExportVO vo) {
        List<String> capacityRowValue = new ArrayList<>();
        Map<String, IndexExportVO>  templateNameAndVOMap = vo.getSortedIndexesByType(IndexTypeEnum.CAPACITY.getDesc());
        for (int i = 1; i <= capacityIndexNum; i++) {
            IndexExportVO indexExportVO = templateNameAndVOMap.get(IndexTypeEnum.CAPACITY.getDesc() + i);
            if (indexExportVO != null) {
                capacityRowValue.add(indexExportVO.getKpiName());
                capacityRowValue.add(indexExportVO.getWeight());
                capacityRowValue.add(indexExportVO.getStartDate());
                capacityRowValue.add(indexExportVO.getEndDate());
            } else {
                capacityRowValue.add(null);
                capacityRowValue.add(null);
                capacityRowValue.add(null);
                capacityRowValue.add(null);
            }

        }
        return capacityRowValue;
    }

    /**
     * 生成全部的质量指标数据
     *
     * @param vo
     * @return
     */
    private static List<String> getQualityRowValue(AggTargetExportVO vo) {
        List<String> qualityRowValue = new ArrayList<>();
        Map<String, IndexExportVO>  templateNameAndVOMap = vo.getSortedIndexesByType(IndexTypeEnum.QUALITY.getDesc());
        for (int i = 1; i <= qualityIndexNum; i++) {
            IndexExportVO indexExportVO = templateNameAndVOMap.get(IndexTypeEnum.QUALITY.getDesc() + i);
            if (indexExportVO != null ) {
                qualityRowValue.add(indexExportVO.getKpiName());
                qualityRowValue.add(indexExportVO.getWeight());
                qualityRowValue.add(indexExportVO.getStartDate());
                qualityRowValue.add(indexExportVO.getEndDate());
                qualityRowValue.add(indexExportVO.getThreshold());
            } else {
                qualityRowValue.add(null);
                qualityRowValue.add(null);
                qualityRowValue.add(null);
                qualityRowValue.add(null);
                qualityRowValue.add(null);
            }
        }
        return qualityRowValue;
    }

    /**
     * 初始化目标表头
     */
    private static List<List<String>> initTargetHeads() {
        return buildHeadTool(targetNum, targetHeadsTemplate,false);
    }

    private static List<List<String>> initCapacityHeads() {
        return buildHeadTool(capacityIndexNum,capacityHeadsTemplate,true);
    }

    private static List<List<String>> initQualityHeads() {
        return buildHeadTool(qualityIndexNum,qualityHeadsTemplate,true);
    }

    private static List<List<String>> buildHeadTool(int loopNum,List<String> headTemplate,boolean generateSuffix) {
        List<List<String>> headList = new ArrayList<>();
        for (int i = 1; i <= loopNum; i++) {
            for (String s : headTemplate) {
                if(generateSuffix){
                    headList.add(Collections.singletonList(s + i));
                }else{
                    headList.add(Collections.singletonList(s));
                }
            }
        }
        return headList;
    }

    /**
     * 将excel行数据转为 TargetComposite 对象
     */
    private TargetComposite parseTargetComposite(List<String> data) {
        TargetComposite targetComposite = new TargetComposite();
        targetComposite.setIndexes(new ArrayList<>());
        parseDataSuppliers.forEach((name, consumerPair) -> {
            // pair 为指标数量、消费者
            BiConsumer<TargetComposite, List<String>> consumer = consumerPair.getRight();
            consumer.accept(targetComposite, data);
        });
        return targetComposite;
    }

    /**
     * 校验表头名称
     * key为列索引 value为列名
     */
    private void checkHeadMapBeforeParse(Map<Integer, String> headMap) {
        if(headMap==null){
            throw new BizException("表头不能为空");
        }
        List<String> rawHeads = getRawHeads();
        if (headMap.size() != rawHeads.size()) {
            throw new IllegalArgumentException("excel模板不正确，表头长度不匹配");
        }
        for (int i = 0; i < headMap.size(); i++) {
            if (!Objects.equals(headMap.get(i), rawHeads.get(i))) {
                throw new IllegalArgumentException("excel列名不匹配，第" + (i + 1) + "列期望值为：" + rawHeads.get(i) +
                        "，实际值为：" + headMap.get(i));
            }
        }
    }


    private static int calculateStartIndex(String consumerName) {
        int startIndex = 0;
        for (Map.Entry<String, Pair<Integer, BiConsumer<TargetComposite, List<String>>>> entry : parseDataSuppliers.entrySet()) {
            if (entry.getKey().equals(consumerName)) {
                return startIndex;
            }
            startIndex += entry.getValue().getLeft();
        }
        throw new IllegalArgumentException("不存在该consumer函数");
    }

    /**
     * 解析数据并构建targetDO
     *
     */

    private static void buildTargetDO(TargetComposite targetComposite, List<String> data) {
        PerformanceTargetDO targetDO = new PerformanceTargetDO();
        int startIndex = calculateStartIndex(TARGET_HEAD);
        targetDO.setPeriod(data.get(startIndex));
        targetDO.setBusinessLineId(data.get(startIndex + 1));
        targetDO.setBusinessLineName(data.get(startIndex + 2));
        targetDO.setEvaluationPlan(data.get(startIndex + 3));
        targetComposite.setTargetDO(targetDO);
    }

    /**
     * 解析数据并构建capacityIndexDOs
     *
     * @param targetComposite
     * @param data
     */
    private static void buildCapacityIndexDOs(TargetComposite targetComposite, List<String> data) {
        List<IndexDO> indexDOs = new ArrayList<>();
        int startIndex = calculateStartIndex(CAPACITY_HEAD);
        int capacityFieldSize = capacityHeadsTemplate.size();
        String businessLineId = targetComposite.getTargetDO().getBusinessLineId();
        for (int i = 0; i < capacityIndexNum; i++) {
            IndexDO indexDO = new IndexDO();
            int idx = startIndex + i * capacityFieldSize;
            indexDO.setKpiName(data.get(idx));
            indexDO.setWeight(data.get(idx + 1));
            indexDO.setStartDate(data.get(idx + 2));
            indexDO.setEndDate(data.get(idx + 3));
            indexDO.setTemplate(IndexTemplate.getByCapacityIndex(String.valueOf(i + 1)));
            indexDO.setBusinessLineId(businessLineId);
            indexDOs.add(indexDO);
        }
        targetComposite.getIndexes().addAll(indexDOs);
    }

    /**
     * 解析数据并构建qualityIndexDOs
     */

    private static void buildQualityIndexDOs(TargetComposite targetComposite, List<String> data) {
        List<IndexDO> indexDOs = new ArrayList<>();
        int startIndex = calculateStartIndex(QUALITY_HEAD);
        int qualityFieldSize = qualityHeadsTemplate.size();
        String businessLineId = targetComposite.getTargetDO().getBusinessLineId();
        for (int i = 0; i < qualityIndexNum; i++) {
            IndexDO indexDO = new IndexDO();
            int idx = startIndex + i * qualityFieldSize;
            indexDO.setKpiName(data.get(idx));
            indexDO.setWeight(data.get(idx + 1));
            indexDO.setStartDate(data.get(idx + 2));
            indexDO.setEndDate(data.get(idx + 3));
            indexDO.setThreshold(data.get(idx + 4));
            indexDO.setTemplate(IndexTemplate.getByQualityIndex(String.valueOf(i + 1)));
            indexDO.setBusinessLineId(businessLineId);
            indexDOs.add(indexDO);
        }
        targetComposite.getIndexes().addAll(indexDOs);
    }
}
