package com.jd.kf.oss.performance.app.controller.dto.request.evaluate;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 分页查询上级评价请求参数
 */
@ApiModel("分页查询上级评价请求参数")
@Data
public class PageEvaluateRequest extends BasePageRequest {
    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", required = true, example = "2025-07")
    @NotBlank(message = "绩效月不能为空")
    private String period;
    /** 主管ERP */
    @ApiModelProperty(value = "主管ERP", example = "manager123")
    private String managerErp;
} 