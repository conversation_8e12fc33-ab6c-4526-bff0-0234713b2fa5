package com.jd.kf.oss.performance.app.controller.dto.vo.userinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 员工信息分页查询结果
 */
@Data
@ApiModel(description = "员工信息分页查询结果")
public class UserInfoPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效月
     */
    @ApiModelProperty(value = "绩效月", example = "2025-05")
    private String period;

    /**
     * ERP
     */
    @ApiModelProperty(value = "ERP", example = "user123")
    private String erp;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", example = "技术部")
    private String deptName;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", example = "DEPT001")
    private String deptId;

    /**
     * 部门路径名称（各级部门名称用"-"连接）
     */
    @ApiModelProperty(value = "部门路径名称", example = "总公司-华北区-北京分公司-技术部")
    private String deptPathName;


    /**
     * 直属上级
     */
    @ApiModelProperty(value = "直属上级", example = "manager123")
    private String managerErp;

    /**
     * 绩效组
     */
    @ApiModelProperty(value = "绩效组", example = "技术组")
    private String businessLineName;

    /**
     * 绩效组ID
     */
    @ApiModelProperty(value = "绩效组ID", example = "002001")
    private String businessLineId;

    /**
     * 绩效方案
     */
    @ApiModelProperty(value = "绩效方案", example = "技术方案")
    private String evaluationPlan;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "admin")
    private String editor;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-06-20 13:53:13")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;
}
