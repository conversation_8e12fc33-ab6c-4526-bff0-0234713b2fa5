package com.jd.kf.oss.performance.app.controller.dto.request.appeal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除归属数据修改请求参数
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("批量删除归属数据修改请求参数")
@Data
public class BatchDeleteAppealModifyRequest {

    /** 待删除ID列表 */
    @ApiModelProperty(value = "待删除ID列表", required = true, example = "[1, 2, 3]")
    @NotEmpty(message = "待删除ID列表不能为空")
    private List<Long> ids;
}
