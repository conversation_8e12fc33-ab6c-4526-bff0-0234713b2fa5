package com.jd.kf.oss.performance.app.controller.dto.vo.evaluate;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 上级评价导出VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class EvaluateExportVO {

    /** 绩效月 */
    @ExcelProperty(value = "绩效月", index = 0)
    @ColumnWidth(12)
    private String period;

    /** 主管ERP */
    @ExcelProperty(value = "主管ERP", index = 1)
    @ColumnWidth(15)
    private String managerErp;

    /** 评价分数 */
    @ExcelProperty(value = "评价分数", index = 2)
    @ColumnWidth(12)
    private String evaluationScore;
}
