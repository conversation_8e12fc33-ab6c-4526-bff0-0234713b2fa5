package com.jd.kf.oss.performance.app.consumer.mq.processor.adapter;

import com.jd.kf.oss.performance.infra.mybatis.entity.WfcUserExtPO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author: songweijia1
 * @description: WfcUserExtPO的适配器实现
 * @date: 2025/7/7
 */

@Component
public class WfcUserExtAdapter implements ModelAdapter<WfcUserExtPO> {

    @Override
    public Long getId(WfcUserExtPO model) {
        return model.getId();
    }

    @Override
    public void setId(WfcUserExtPO model, Long id) {
        model.setId(id);
    }

    @Override
    public String getBusinessKey(WfcUserExtPO model) {
        return model.getErp();
    }

    @Override
    public LocalDateTime getModified(WfcUserExtPO model) {
        return model.getModified();
    }

    @Override
    public Boolean getYn(WfcUserExtPO model) {
        return model.getYn();
    }

    @Override
    public void setYn(WfcUserExtPO model, Boolean yn) {
        model.setYn(yn);
    }

    @Override
    public void setUniqId(WfcUserExtPO model, Long uniqId) {
        model.setUniqId(uniqId);
    }

}
