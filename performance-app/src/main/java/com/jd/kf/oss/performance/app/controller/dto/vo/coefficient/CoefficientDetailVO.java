package com.jd.kf.oss.performance.app.controller.dto.vo.coefficient;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CoefficientDetailVO {

        Long id;
        /**
         * 系数ID
         */
        private String code;

        /**
         * 系数名称
         */
        private String name;

        /**
         * 系数类型：分段、月周期、常量
         */
        private CoefficientTemplateTypeEnum type;

        /**
         * 系数描述
         */
        private String description;

        /**
         * 系数项列表
         */
        private List<CoefficientItemVO> coefficientItems;

        /**
         * 绩效月
         */
        private String period;

        /**
         * 状态
         */
        private String status;

        /**
         * 修改者
         */
        private String editor;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
        private LocalDateTime created;

        /**
         * 修改时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
        private LocalDateTime modified;

    }