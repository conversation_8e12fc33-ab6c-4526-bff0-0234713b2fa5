package com.jd.kf.oss.performance.app.controller.dto.request.coefficient;


import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CoefficientCreateReq {
    /**
     * 系数名称
     */
    @NotBlank(message = "系数名称不能为空")
    private String name;

    /**
     * 系数类型：分段、月周期、常量
     */
    @ApiModelProperty("系数类型：分段系数、月周期系数、常量系数")
    @NotNull(message = "系数类型不能为空")
    private CoefficientTemplateTypeEnum type;

    /**
     * 系数描述
     */
    private String description;


    /**
     * 系数项列表
     */
    @NotEmpty(message = "系数项不能为空")
    private List<CoefficientItem> coefficientItems;


    @Data
    public static class CoefficientItem {
        /**
         * 左边界
         */
        private String leftEndpoint;

        /**
         * 右边界
         */
        private String rightEndpoint;

        /**
         * 系数值
         */
        private String coefficientNum;
    }


}
