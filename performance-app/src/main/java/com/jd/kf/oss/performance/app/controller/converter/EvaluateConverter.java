package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.PageEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.EvaluateVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateImportVO;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 上级评价对象转换器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface EvaluateConverter {
    EvaluateConverter INSTANCE = Mappers.getMapper(EvaluateConverter.class);

    /**
     * 分页请求参数转DO
     */
    @Mapping(target = "erp", source = "managerErp")
    EvaluateDO request2DO(PageEvaluateRequest request);

    /**
     * DO转VO
     */
    @Mapping(target = "managerErp", source = "erp")
    @Mapping(target = "evaluationScore", source = "score")
    EvaluateVO do2VO(EvaluateDO evaluateDO);

    /**
     * 分页DO转分页VO
     */
    CommonPage<EvaluateVO> pageDO2PageVO(CommonPage<EvaluateDO> commonPage);

    /**
     * DO列表转导出VO列表
     */
    @Mapping(target = "managerErp", source = "erp")
    @Mapping(target = "evaluationScore", source = "score")
    EvaluateExportVO do2ExportVO(EvaluateDO evaluateDO);

    /**
     * DO列表转导出VO列表
     */
    List<EvaluateExportVO> doList2ExportVOList(List<EvaluateDO> evaluateList);

    /**
     * 导入VO转DO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "erp", source = "managerErp")
    @Mapping(target = "score", source = "evaluationScore")
    @Mapping(target = "creator", source = "editor")
    @Mapping(target = "editor", source = "editor")
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "modified", ignore = true)
    EvaluateDO importVO2DO(EvaluateImportVO importVO);

    /**
     * 导入VO列表转DO列表
     */
    List<EvaluateDO> importVOList2DOList(List<EvaluateImportVO> importVOList);
}