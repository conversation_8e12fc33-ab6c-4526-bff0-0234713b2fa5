package com.jd.kf.oss.performance.app.handler;

import com.alibaba.fastjson.JSON;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.enums.ResultCodeEnum;
import com.jd.kf.oss.performance.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class GlobalExceptionHandler {
  @ExceptionHandler(HttpMessageNotReadableException.class)
  @ResponseBody
  public ApiResult<?> handleBindException(HttpMessageNotReadableException e) {
    log.error("web request httpMessageNotReadableException param:{}", JSON.toJSONString(e.getMessage()));
    return ApiResult.illegal(e.getMessage());
  }


  @ExceptionHandler(MethodArgumentNotValidException.class)
  @ResponseBody
  public ApiResult<?> handleBindException(MethodArgumentNotValidException e) {
    FieldError fieldError = e.getBindingResult().getFieldError();
    log.error("参数校验异常:{}({})", Objects.requireNonNull(fieldError).getDefaultMessage(),
        fieldError.getField());
    return ApiResult.illegal(fieldError.getDefaultMessage());
  }

  @ExceptionHandler(BizException.class)
  @ResponseBody
  public ApiResult<?> bizExceptionHandle(HttpServletRequest request, BizException e) {
    log.error("web request bizException param:{}", JSON.toJSONString(request.getParameterMap()),
        e);
    if(StringUtils.isNotBlank(e.getErrorMsg())){
      return new ApiResult<>(e.getCode(), String.format(e.getMessage(), e.getErrorMsg()));
    }
    return new ApiResult<>(e.getCode(), e.getMessage());
  }

  @ExceptionHandler(IllegalStateException.class)
  @ResponseBody
  public ApiResult<?> handleIllegalStateException(IllegalStateException e) {
    return ApiResult.illegal(e.getMessage());
  }

  @ExceptionHandler(IllegalArgumentException.class)
  @ResponseBody
  public ApiResult<?> illegalArgumentExceptionHandler(HttpServletRequest request, IllegalArgumentException e) {
    log.error("web request illegalArgumentExceptionHandler param:{}", JSON.toJSONString(request.getParameterMap()),
        e);
    if(StringUtils.isNotBlank(e.getMessage())){
      return new ApiResult<>(ResultCodeEnum.ERROR.getCode(), e.getMessage());
    }
    return new ApiResult<>(ResultCodeEnum.ERROR.getCode(), ResultCodeEnum.ERROR.getMessage());
  }

  @ExceptionHandler(Exception.class)
  @ResponseBody
  public ApiResult<?> exceptionHandle(HttpServletRequest request, Exception e) {
    log.error("web request system exception param:{}", JSON.toJSONString(request.getParameterMap()),
        e);
    return new ApiResult<>(ResultCodeEnum.ERROR.getCode(), ResultCodeEnum.ERROR.getMessage());
  }

}
