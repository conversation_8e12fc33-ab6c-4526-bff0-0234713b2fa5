package com.jd.kf.oss.performance.app.controller.dto.request.appeal;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 无效数据剔除分页查询请求参数
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@ApiModel("无效数据剔除分页查询请求参数")
@Data
public class PageAppealRemoveRequest extends BasePageRequest {

    /** 绩效月 */
    @ApiModelProperty(value = "绩效月", example = "2025-02", required = true)
    @NotBlank(message = "绩效月不能为空")
    private String period;

    /** 指标名称 */
    @ApiModelProperty(value = "指标名称", example = "事件满意度")
    private String kpiName;

    /** 需剔除的业务单号 */
    @ApiModelProperty(value = "需剔除的业务单号", example = "271595782")
    private String ticketId;
}
