package com.jd.kf.oss.performance.app.task;

import com.jd.kf.oss.performance.app.service.IndexBasicPeriodSyncService;
import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDomainService;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/01
 */
@Component
@DongJob(name = "指标基础数据绩效月同步任务")
public class IndexBasicPeriodSyncTask extends AbstractJobHandler {

    @Autowired
    private IndexBasicPeriodSyncService indexBasicPeriodSyncService;


    /**
     * 执行指标基础周期同步任务
     * @param jobContext 作业上下文对象，包含作业执行所需的信息和环境
     * @return 返回作业执行结果，成功时返回JobResult.SUCCESS
     */
    @Override
    public JobResult execute(JobContext jobContext) {

        indexBasicPeriodSyncService.indexBasicPeriodSyncTaskExec();
        return JobResult.SUCCESS;
    }
}
