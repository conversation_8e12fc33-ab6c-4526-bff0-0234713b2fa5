package com.jd.kf.oss.performance.app.interceptor;

import com.jd.fastjson.JSON;
import com.jd.jsl.base.account.dto.UserInfoResultDTO;
import com.jd.kf.oss.performance.app.service.JslAccountService;
import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContext;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.infra.annotation.IgnoreDataPermissionCheck;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Set;

@Component
public class UserContextInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(UserContextInterceptor.class);

    @Resource
    private JslAccountService jslAccountService;

    @Resource
    private DynamicConfig dynamicConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        UserContext userContext = initUserContext(request);
        // 拦截开关未开，统一使用default作为操作账号
        if (!dynamicConfig.isAdminAuth()) {
            userContext.setErp("default");
            UserContextHolder.removeAndSet(userContext);
            return true;
        }

        String token = request.getHeader(SystemConstants.TOKEN_NAME);
        if (StringUtils.isBlank(token)) {
            throw new BizException("token为空，请重新登录！");
        }
        UserInfoResultDTO userInfoByToken = jslAccountService.getUserInfoByToken(token);
        if (userInfoByToken == null || StringUtils.isBlank(userInfoByToken.getPin())) {
            throw new BizException("用户信息不存在，请重新登录！");
        }
        String erp = userInfoByToken.getPin();
        log.info("performance login erp : {}", erp);
        userContext.setErp(erp);
        IgnoreDataPermissionCheck methodAnnotation = handlerMethod.getMethodAnnotation(IgnoreDataPermissionCheck.class);
        if (methodAnnotation != null) {
            UserContextHolder.removeAndSet(userContext);
            return true;
        }
        // 查询资源码
        Set<String> resourceCodeSet = jslAccountService.getPortalResourceCodeListByToken(token);
        // 权限校验
        portalInterceptor(request, resourceCodeSet);
        // 权限校验通过，设置userContext
        UserContextHolder.removeAndSet(userContext);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, @Nullable Exception ex) throws Exception {
        UserContextHolder.remove();
    }

    /**
     * 支持统一运营门户的拦截，主要是获取 tenantCode
     */
    private void portalInterceptor(HttpServletRequest request, Set<String> tenantCodeSetByToken) throws Exception {
        String tenantCode = request.getHeader(SystemConstants.TENANT_CODE);
        if (StringUtils.isBlank(tenantCode) || SystemConstants.NULL.equalsIgnoreCase(tenantCode)) {
            throw new BizException("租户标识不能为空");
        }
        // 租户权限校验
        if (CollectionUtils.isEmpty(tenantCodeSetByToken)
                || !tenantCodeSetByToken.contains(tenantCode)) {
            throw new BizException("该账户没有权限");
        }
        log.info("tenantCodeSetByToken={}", JSON.toJSON(tenantCodeSetByToken));
    }

    private UserContext initUserContext(HttpServletRequest request) {
        String tenantCode = request.getHeader(SystemConstants.TENANT_CODE);
        UserContext userContext = UserContext
                .builder()
                .tenantCode(tenantCode)
                .build();

        return userContext;
    }

}
