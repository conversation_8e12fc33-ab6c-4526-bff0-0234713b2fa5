package com.jd.kf.oss.performance.app.controller.controller;

import com.jd.kf.oss.performance.app.controller.converter.TargetConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.*;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget.TargetDetailVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget.TargetPageVO;
import com.jd.kf.oss.performance.app.controller.service.FileHelperService;
import com.jd.kf.oss.performance.app.controller.service.PerformanceTargetService;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.TargetAggregateService;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.TargetComposite;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Api("绩效目标管理")
@RestController
@RequestMapping("/performance/target")
public class TargetController {
    @Resource
    private TargetAggregateService targetAggregateService;

    @Resource
    private PerformanceTargetDomainService targetDomainService;


    @Resource
    PerformanceTargetService performanceTargetService;


    @Resource
    FileHelperService fileHelperService;

    @Resource
    DynamicConfig dynamicConfig;

    /**
     * 分页查询绩效目标
     */
    @ApiOperation("分页查询绩效目标")
    @PostMapping("/page")
    public ApiResult<CommonPage<TargetPageVO>> targetPageList(@RequestBody @Validated TargetPageRequest request) {
        // 参数校验
        CheckUtil.notNull(request, "请求体不能为空");

        CommonPage<PerformanceTargetDO> commonPage = targetDomainService.queryTargetByConditions(
                UserContextHolder.getTenantCode(),
                request.getPeriod(),
                request.getBusinessLineId(),
                request.getBusinessLineName(),
                request.getPlanCode(),
                request.getPage(),
                request.getPageSize()
        );
        return ApiResult.success(TargetConverter.INSTANCE.pageDO2VO(commonPage));
    }

    /**
     * 获取绩效目标详情
     */
    @ApiModelProperty("绩效目标详情")
    @PostMapping("/detail")
    public ApiResult<TargetDetailVO> targetDetail(@RequestBody @Validated TargetDetailRequest request) {
        TargetComposite targetDetail = targetAggregateService.getTargetDetail(
                request.getPeriod(),
                UserContextHolder.getTenantCode(),
                request.getBusinessLineId()
        );
        TargetDetailVO targetDetailVO = TargetConverter.INSTANCE.target2VO(targetDetail.getTargetDO());
        targetDetailVO.setIndexes(TargetConverter.INSTANCE.indexDO2Item(targetDetail.getIndexes()));
        return ApiResult.success(targetDetailVO);
    }

    /**
     * 更新绩效目标、绩效方案、调整指标数据
     */
    @ApiOperation("更新绩效目标、绩效方案、调整指标数据")
    @PostMapping("/update")
    public ApiResult<Void> updateTargetOfBusinessLine(@RequestBody @Validated TargetUpdateRequest request) {
        //默认只能更新当前绩效月的目标
        performanceTargetService.updateTargetOfBusinessLine(request);
        return ApiResult.success();
    }


    /**
     * 导入CPD数据
     */
    @ApiOperation("导入CPD")
    @PostMapping("/import/CPD")
    public ApiResult<String> importCPD(@RequestBody @Validated BaseImportRequest request,
                                       HttpServletRequest httpServletRequest) throws IOException {
       return performanceTargetService.importCPD(request,httpServletRequest);
    }

    /**
     * 导入价格和天数数据
     */
    @ApiOperation("导入price和标准天")
    @PostMapping("/import/priceAndDays")
    public ApiResult<String> importPriceAndDays(@RequestBody @Validated BaseImportRequest request,
                                                HttpServletRequest httpServletRequest) {

      return performanceTargetService.importPriceAndDays(request,httpServletRequest);

    }

    /**
     * 导入当前绩效月绩效指标
     */
    @ApiOperation("导入绩效指标")
    @PostMapping("/import/index")
    public ApiResult<String> importTargetAndIndex(@RequestBody @Validated BaseImportRequest request,
                                                  HttpServletRequest httpServletRequest){
        return performanceTargetService.importTargetAndIndex(request, httpServletRequest);
    }


    /**
     * 导出CPD数据
     */
    @ApiOperation("导出CPD")
    @PostMapping("/export/CPD")
    public ApiResult<String> exportCPD(@Validated @RequestBody ExportCPDRequest request, HttpServletRequest httpServletRequest,
                          HttpServletResponse httpServletResponse) {
        String url=performanceTargetService.exportCPD(request, httpServletRequest, httpServletResponse);
        return ApiResult.success(url);
    }

    /**
     * 导出价格数据
     */
    @ApiOperation("导出price")
    @PostMapping("/export/priceAndDays")
    public ApiResult<String> exportPrice(@Validated @RequestBody ExportPriceRequest request, HttpServletRequest httpServletRequest,
                            HttpServletResponse httpServletResponse) {

        String url=performanceTargetService.exportPrice(request, httpServletRequest, httpServletResponse);
        return ApiResult.success(url);
    }

    /**
     * 导出绩效指标
     */
    @ApiOperation("导出绩效指标")
    @PostMapping("/export/index")
    public ApiResult<String> exportTargetAndIndex(@RequestBody @Validated ExportTargetAndIndexRequest request,
                                     HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {

        String url=performanceTargetService.exportTargetAndIndex(request, httpServletRequest, httpServletResponse);
        return ApiResult.success(url);
    }
    /**
     * 查询CPD Excel模板
     */
    @PostMapping("/excel/CPD")
    public ApiResult<String> queryCpdExcelTemplate() {
        return ApiResult.success(dynamicConfig.getCpdExcelTemplate());
    }

    /**
     * 查询价格和天数 Excel模板
     */
    @PostMapping("/excel/priceAndDays")
    public ApiResult<String> queryPriceAndDaysExcelTemplate() {
        return ApiResult.success(dynamicConfig.getPriceAndDaysExcelTemplate());
    }

    /**
     * 查询绩效指标 Excel模板
     */
    @PostMapping("/excel/targetIndex")
    public ApiResult<String> queryTargetIndexExcelTemplate() {
        return ApiResult.success(dynamicConfig.getTargetIndexExcelTemplate());
    }

}
