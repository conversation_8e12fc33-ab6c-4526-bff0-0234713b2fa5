package com.jd.kf.oss.performance.app.controller.dto.request.plan;

import lombok.Data;

/**
 * 绩效方案保存请求
 */
@Data
public class PlanSaveRequest {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * code
     */
    private String code;

    /**
     * 当前租户
     */
    private String tenantCode;

    /**
     * 当前月
     */
    private String period;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    private String type;


    /**
     * 公式展示信息
     */
    private String formulaDisplayInfo;

    /**
     * 公式描述信息
     */
    private String displayInfo;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 状态
     */
    private String status;
}
