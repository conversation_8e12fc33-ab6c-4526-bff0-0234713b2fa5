package com.jd.kf.oss.performance.app.controller.dto.vo.performancetarget;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ImportPriceAndDaysVO {


    @JsonProperty(value = "绩效月")
    private String period;

    /**
     * 绩效组id
     */
    @JsonProperty(value = "绩效组ID")
    private String businessLineId;


    /**
     * 单价
     */
    @JsonProperty(value = "单价(元)")
    private String price;
    /**
     * 月标准天数
     */
    @JsonProperty(value = "月标准天数")
    private String days;

}
