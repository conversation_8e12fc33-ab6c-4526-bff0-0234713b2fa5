package com.jd.kf.oss.performance.app.controller.dto.vo.coefficient;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CoefficientItemVO {
    Long id;
    /**
     * 区间左边界
     */
    private String leftEndpoint;

    /**
     * 右边界
     */
    private String rightEndpoint;

    /**
     * 系数值
     */
    private String coefficientNum;

    /**
     * 修改者
     */
    private String editor;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime modified;
    /**
     * 状态
     */
    private String status;

}