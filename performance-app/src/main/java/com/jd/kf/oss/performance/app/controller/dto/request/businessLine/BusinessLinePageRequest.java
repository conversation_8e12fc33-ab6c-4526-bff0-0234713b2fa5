package com.jd.kf.oss.performance.app.controller.dto.request.businessLine;

import com.jd.kf.oss.performance.app.controller.dto.request.base.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessLinePageRequest extends BasePageRequest {

    /**
     * erp账号，可选
     */
    @ApiModelProperty("更新人erp账号")
    private String editor;

    /**
     *绩效组名称
     */
    @ApiModelProperty("绩效组名称")
    private String name;

    /**
     * 绩效方案ID，可选
     */
    @ApiModelProperty("绩效方案ID")
    private String planCode;
}
