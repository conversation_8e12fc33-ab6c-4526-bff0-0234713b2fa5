package com.jd.kf.oss.performance.app.task;

import com.jd.kf.oss.performance.app.service.BusinessLinePeriodSyncService;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/30
 * 绩效组
 */
@Component
@DongJob(name = "绩效组绩效月同步任务")
@Slf4j
public class BusinessLinePeriodSyncTask extends AbstractJobHandler {
    @Autowired
    private BusinessLinePeriodSyncService businessLinePeriodSyncService;

    /**
     * @param jobContext
     * @return {@link JobResult }
     */
    @Override
    public JobResult execute(JobContext jobContext) {
        businessLinePeriodSyncService.businessLinePeriodSyncTask();
        return JobResult.SUCCESS;
    }
}
