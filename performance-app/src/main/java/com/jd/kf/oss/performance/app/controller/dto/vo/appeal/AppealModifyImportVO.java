package com.jd.kf.oss.performance.app.controller.dto.vo.appeal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;



/**
 * 归属数据修改导入VO
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class AppealModifyImportVO {

    /** 绩效月 */
    @JsonProperty(value = "绩效月", index = 0)
    private String period;

    /** 需修改指标名称 */
    @JsonProperty(value = "需修改指标名称", index = 1)
    private String kpiName;

    /** 需修改单据ID */
    @JsonProperty(value = "需修改单据ID", index = 2)
    private String billId;

    /** 客服ERP */
    @JsonProperty(value = "客服ERP", index = 3)
    private String erp;

    /** 技能ID */
    @JsonProperty(value = "修改后技能组ID", index = 5)
    private String skillId;

    /** 租户标识（导入时需要） */
    private String tenantCode;

    /** 编辑人（导入时需要） */
    private String editor;
}
