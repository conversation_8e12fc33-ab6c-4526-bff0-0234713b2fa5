package com.jd.kf.oss.performance.app.consumer.mq.processor;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: songweijia1
 * @description: 数据订阅表处理结果实体类
 * @date: 2025/7/2
 */

@Getter
public class MultiTableProcessResult<T, U> {

    // 主表处理结果
    private final List<T> totalMain = new ArrayList<>();
    private final List<T> toUpsertMain = new ArrayList<>();
    private final List<T> toDeleteMain = new ArrayList<>();

    // 扩展表处理结果
    private final List<U> totalExt = new ArrayList<>();
    private final List<U> toUpsertExt = new ArrayList<>();
    private final List<U> toDeleteExt = new ArrayList<>();

    // 主表操作方法
    public void addToTotalMain(T item) {
        if (Objects.nonNull(item)) {
            totalMain.add(item);
        }
    }

    public void addToUpsertMain(T item) {
        if (Objects.nonNull(item)) {
            toUpsertMain.add(item);
        }
    }

    public void addToDeleteMain(T item) {
        if (Objects.nonNull(item)) {
            toDeleteMain.add(item);
        }
    }

    // 扩展表操作方法
    public void addToTotalExt(U item) {
        if (Objects.nonNull(item)) {
            totalExt.add(item);
        }
    }

    public void addToUpsertExt(U item) {
        if (Objects.nonNull(item)) {
            toUpsertExt.add(item);
        }
    }

    public void addToDeleteExt(U item) {
        if (Objects.nonNull(item)) {
            toDeleteExt.add(item);
        }
    }

    // 主表判断方法
    public boolean hasTotalMainData() {
        return CollectionUtils.isNotEmpty(totalMain);
    }

    public boolean hasUpsertMainData() {
        return CollectionUtils.isNotEmpty(toUpsertMain);
    }

    public boolean hasToDeleteMainData() {
        return CollectionUtils.isNotEmpty(toDeleteMain);
    }

    // 扩展表判断方法
    public boolean hasTotalExtData() {
        return CollectionUtils.isNotEmpty(totalExt);
    }

    public boolean hasUpsertExtData() {
        return CollectionUtils.isNotEmpty(toUpsertExt);
    }

    public boolean hasToDeleteExtData() {
        return CollectionUtils.isNotEmpty(toDeleteExt);
    }

    // 总体判断方法
    public boolean hasAnyData() {
        return hasTotalMainData() || hasTotalExtData();
    }

    public boolean hasAnyUpsertData() {
        return hasUpsertMainData() || hasUpsertExtData();
    }

    public boolean hasAnyDeleteData() {
        return hasToDeleteMainData() || hasToDeleteExtData();
    }

}