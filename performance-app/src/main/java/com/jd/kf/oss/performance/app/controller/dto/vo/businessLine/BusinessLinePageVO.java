package com.jd.kf.oss.performance.app.controller.dto.vo.businessLine;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
@Data
@ApiModel(description = "绩效组分页查询结果")
public class BusinessLinePageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组名
     */
    @ApiModelProperty(value = "组名", example = "测试组1")
    private String name;

    /**
     * 组ID
     */
    @ApiModelProperty(value = "组ID", example = "GROUP001")
    private String businessLineId;

    /**
     * ERP
     */
    @ApiModelProperty(value = "editor", example = "user123")
    private String editor;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2025-06-20 13:53:13")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "Asia/Shanghai")
    private LocalDateTime modified;

    /**
     * 组描述
     */
    @ApiModelProperty(value = "组描述", example = "这是测试组1的描述")
    private String description;

    /**
     * 绩效方案ID
     */
    @ApiModelProperty(value = "绩效方案Code", example = "PLAN001")
    private String planCode;

    /**
     * 绩效方案名称
     */
    @ApiModelProperty(value = "绩效方案名称", example = "2025年Q1绩效方案")
    private String planName;
}
