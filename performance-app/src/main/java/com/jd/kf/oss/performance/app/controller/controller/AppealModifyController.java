package com.jd.kf.oss.performance.app.controller.controller;

import com.jd.kf.oss.performance.app.controller.dto.request.performancetarget.BaseImportRequest;
import com.jd.kf.oss.performance.app.controller.service.AppealModifyService;
import com.jd.kf.oss.performance.app.controller.converter.AppealModifyConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.BatchDeleteAppealModifyRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.ExportAppealModifyRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.PageAppealModifyRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.AppealModifyVO;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.AppealModifyDO;
import com.jd.kf.oss.performance.domain.config.domain.appealmodify.AppealModifyDomainService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.utils.CommonPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;

/**
 * 归属数据修改控制器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Api(tags = "归属数据修改管理")
@RestController
@RequestMapping("/performance/appeal_modify")
public class AppealModifyController {

    @Autowired
    private AppealModifyDomainService appealModifyDomainService;

    @Autowired
    private AppealModifyService appealModifyService;

    @Autowired
    private DynamicConfig dynamicConfig;

    /**
     * 分页查询归属数据修改
     * @param request 分页查询请求参数
     * @return 分页结果
     */
    @ApiOperation("分页查询归属数据修改")
    @PostMapping("/page")
    public ApiResult<CommonPage<AppealModifyVO>> pageAppealModify(@RequestBody @Validated PageAppealModifyRequest request) {
        // 使用字段参数方式调用DomainService
        CommonPage<AppealModifyDO> commonPage = appealModifyDomainService.queryAppealModifyByConditions(
                request.getPeriod(),
                request.getKpiName(),
                request.getSkillId(),
                request.getTicketId(),
                request.getPage(),
                request.getPageSize()
        );

        return ApiResult.success(AppealModifyConverter.INSTANCE.pageDO2PageVO(commonPage));
    }


    /**
     * 批量删除归属数据修改
     * @param request 批量删除请求参数
     * @return 操作结果
     */
    @ApiOperation("批量删除归属数据修改")
    @PostMapping("/delete")
    public ApiResult<Void> batchDeleteAppealModify(@RequestBody @Validated BatchDeleteAppealModifyRequest request) {
        boolean success = appealModifyDomainService.batchDelete(request.getIds());
        
        return success ? ApiResult.success() : ApiResult.error("删除失败");
    }



    /**
     * 导出归属数据修改
     * @param request 导出请求参数
     * @param httpServletRequest HTTP请求
     */
    @ApiOperation("导出归属数据修改")
    @PostMapping("/export")
    public ApiResult<String> exportAppealModify(@RequestBody @Validated ExportAppealModifyRequest request,
                                  HttpServletRequest httpServletRequest) {
        return ApiResult.success(appealModifyService.exportAppealModify(request, httpServletRequest));
    }

    /**
     * 导入归属数据修改
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     * @throws IOException IO异常
     */
    @ApiOperation("导入归属数据修改")
    @PostMapping("/import")
    public ApiResult<String> importAppealModify(@RequestBody @Validated BaseImportRequest request,
                                             HttpServletRequest httpServletRequest) throws IOException {
        return appealModifyService.importAppealModify(request.getUrl(), httpServletRequest);
    }


    /**
     * 查询归属数据修改Excel模板
     */
    @PostMapping("/excel")
    public ApiResult<String> queryAppealModifyTemplate() {
        return ApiResult.success(dynamicConfig.getAppealModifyExcelTemplate());
    }
}
