<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.jd.com/schema/jmq
	    http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <!--JMQ4-->
    <jmq:transport id="jmq4.transport" address="${jmq4.address}" user="${jmq4.username}" password="${jmq4.password}"
                   app="${jmq4.app}" epoll="false"/>

<!--    <jmq:consumer id="jfSkillGroupSyncConsumer" transport="jmq4.transport">-->
<!--        <jmq:listener topic="${assign.skill.group.sync.jf.topic}" listener="jfSkillGroupSyncListener"/>-->
<!--    </jmq:consumer>-->

<!--    <jmq:consumer id="eventSkillGroupSyncConsumer" transport="jmq4.transport">-->
<!--        <jmq:listener topic="${assign.skill.group.sync.event.topic}" listener="eventSkillGroupSyncListener"/>-->
<!--    </jmq:consumer>-->

    <jmq:consumer id="wfcWaiterInfoConsumer" transport="jmq4.transport">
    </jmq:consumer>

    <bean id="wfcWaiterInfoListener" class="com.jd.kf.oss.performance.app.consumer.mq.WfcWaiterInfoListener"/>

    <!--
        <jmq:consumer id="afsSkillGroupSyncConsumer" transport="jmq4.transport">
            <jmq:listener topic="${assign.skill.group.sync.afs.topic:}" listener="afsSkillGroupSyncListener"/>
        </jmq:consumer>
    -->


</beans>