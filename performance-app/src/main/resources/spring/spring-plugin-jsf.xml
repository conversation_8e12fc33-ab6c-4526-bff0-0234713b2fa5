<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf  http://jsf.jd.com/schema/jsf/jsf.xsd">

<!--    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.local"/>-->

<!--    <jsf:server id="jsf" protocol="jsf" iothreads="8" threads="200" threadpool="fixed"-->
<!--                queues="500"/>-->


    <jsf:registry id="jsfRegistry"
                  protocol="jsfRegistry"
                  index="${jsf.registry.index:test.i.jsf.jd.local}"/>

    <jsf:server id="jsf" protocol="jsf"
                iothreads="8" threads="200"
                threadpool="fixed"
                queues="500"/>

    <jsf:consumer id="authCheckService" interface="com.jd.jsl.base.auth.AuthCheckService"
                  alias="${jsl.jsf.alias:jdtest}" timeout="3000" serialization="hessian" check="false"/>

    <jsf:consumer id="accountService" interface="com.jd.jsl.base.account.AccountService"
                  alias="${jsl.jsf.alias:jdtest}" timeout="3000" serialization="hessian" check="false"/>


    <jsf:provider id="queryBusinessLinePage"
                  interface="com.jd.kf.oss.performance.api.item.api.BusinessLineService"
                  alias="${jsf.provider.alias:jdtest}"
                  ref="businessLineServiceImpl">
    </jsf:provider>
</beans>