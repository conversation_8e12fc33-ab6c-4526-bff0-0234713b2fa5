#spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
## writeDb config
#spring.datasource.druid.write.url=*********************************************************************************************************************************
#spring.datasource.druid.write.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.druid.write.initial-size=5
#spring.datasource.druid.write.max-active=5
#spring.datasource.druid.write.min-idle=1
#spring.datasource.druid.write.max-wait=60000
#spring.datasource.druid.write.pool-prepared-statements=false
#spring.datasource.druid.write.validation-query=select 1
#spring.datasource.druid.write.validation-query-timeout=1
#spring.datasource.druid.write.test-on-borrow=false
#spring.datasource.druid.write.test-on-return=false
#spring.datasource.druid.write.test-while-idle=true
#spring.datasource.druid.write.time-between-eviction-runs-millis=10000
#spring.datasource.druid.write.filters=log4j2,wall
## readDb config
#spring.datasource.druid.read.url=*********************************************************************************************************************************
#spring.datasource.druid.read.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.druid.read.initial-size=5
#spring.datasource.druid.read.max-active=5
#spring.datasource.druid.read.min-idle=1
#spring.datasource.druid.read.max-wait=60000
#spring.datasource.druid.read.pool-prepared-statements=false
#spring.datasource.druid.read.validation-query=select 1
#spring.datasource.druid.read.validation-query-timeout=1
#spring.datasource.druid.read.test-on-borrow=false
#spring.datasource.druid.read.test-on-return=false
#spring.datasource.druid.read.test-while-idle=true
#spring.datasource.druid.read.time-between-eviction-runs-millis=10000
#spring.datasource.druid.read.filters=log4j2,wall
#
##assign db
#spring.datasource.druid.assign.write.url=*********************************************************************************************************************************
#spring.datasource.druid.assign.write.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.druid.assign.write.initial-size=5
#spring.datasource.druid.assign.write.max-active=5
#spring.datasource.druid.assign.write.min-idle=1
#spring.datasource.druid.assign.write.max-wait=60000
#spring.datasource.druid.assign.write.pool-prepared-statements=false
#spring.datasource.druid.assign.write.validation-query=select 1
#spring.datasource.druid.assign.write.validation-query-timeout=1
#spring.datasource.druid.assign.write.test-on-borrow=false
#spring.datasource.druid.assign.write.test-on-return=false
#spring.datasource.druid.assign.write.test-while-idle=true
#spring.datasource.druid.assign.write.time-between-eviction-runs-millis=10000
#spring.datasource.druid.assign.write.filters=log4j2,wall

# jsf config
king.cache.alias=jdtest
agent.info.alias=jdtest
disputeApi.info.alias=jdtest
jsf.csim.alias=csim-dev
jsf.registry.index=test.i.jsf.jd.local
jsl.jsf.alias=jdtest
jsf.provider.alias=jdtest


#jss
jss.credential.accessKeyId=pe3Mv334pgLCvWW2
jss.credential.secretAccessKeyId=vHDI4m6ccRKNoa8Ib30UEV1OdfIMZOaQzPcH1JuC
jss.hostName=test.storage.jd.com
jss.credential.accessKeyId.ddfile=FWNbVQca8eBWkvZD
jss.credential.secretAccessKeyId.ddfile=ppDLWt9xgiEPPpeamMwJt76Bvh7Mg9FRoNNdwKUK


#king cache

laf.config.manager.application=jdos_wfm-schedule
laf.config.manager.resources[0].name=wfm-schedule
laf.config.manager.resources[0].uri=ucc://jdos_wfm-schedule:<EMAIL>/v1/namespace/wfmschedule/config/schedule_config/profiles/test?longPolling=60000&necessary=true

laf.config.manager.parameters[0].name=autoListener
laf.config.manager.parameters[0].value=true
laf.config.logger.enabled=true
laf.config.logger.type=logback
laf.config.logger.key=logger.level

#king cache
im.pivot.cache.group=pivot
im.pivot.appId=im4
im.pivot.ai.cache.group=ai-schedule

#??????
dispute.mq.topic=uad_tc_push_message_test151
#?????
dispute.order.topic=referee_to_zs_message_test
#????????
dispute.result.topic=uad_tc_message_pushed_test
#????????
agent.pod.topic=send_agent_state_test

crm.jsf.alias=crm_paas_yf

assign.skill.group.sync.jf.topic=assign_skill_group_jf_sync_uat
assign.skill.group.sync.event.topic=assign_skill_group_event_sync_uat

jmq4.address=nameserver.jmq.jd.local:80
jmq4.username=wfmschedule
jmq4.password=6b4d6da61225414c94205247ba5815ca
jmq4.app=wfmschedule

csim.jsf.alias=csim-dev
csim.jsf.token=10GAE51V9
jsf.alias.scheduleDevService = dd0
dev.prefix=pre
assignCache.prefix=dd0.

assign.success.topic=assign_result_test
assign.crm.task.change.topic=CRM2_CASE_COMM_INFO
assign.crm.task.result.topic=crm_intelligent_dispatch_result

wfm.cache.group=pivot
wfm.cache.appId=im4

assign.crm.task.first.res.topic=uad_tc_push_message_test151

#????????jmq2?jmq4??????????????
referee.assign.jmq2=true

# MyBatis-Plus SQL ?????? (properties??)

# ??MyBatis??????????
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ????SQL(???)
mybatis-plus.global-config.db-config.sql-session.sql-show=true