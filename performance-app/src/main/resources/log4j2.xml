<?xml version="1.0" encoding="UTF-8"?>

<Configuration status="WARN" monitorInterval="300">
  <properties>
    <property name="LOG_HOME">/tmp</property>
    <property name="FILE_NAME">performance</property>
  </properties>
  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <PatternLayout
        pattern="[%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %class{36}:%M(%L)] - [%X{IM_MDC_TRACE_ID}]%msg%xEx%n"/>
    </Console>
    <RollingFile name="RollingFile" fileName="${LOG_HOME}/${FILE_NAME}.log"
      filePattern="${LOG_HOME}/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log">
      <PatternLayout pattern="[%d][%t][%p][%c:%L] [%X{IM_MDC_TRACE_ID}]%m%n"/>
      <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
        <SizeBasedTriggeringPolicy size="512 MB"/>
      </Policies>
      <DefaultRolloverStrategy max="40">
        <Delete basePath="${LOG_HOME}/$${date:yyyy-MM}/" maxDepth="2">
          <IfFileName glob="*.log"/>
          <!--!Note: 这里的age必须和filePattern协调, 后者是精确到HH, 这里就要写成xH, xd就不起作用
          另外, 数字最好>2, 否则可能造成删除的时候, 最近的文件还处于被占用状态,导致删除不成功!-->
          <!--7天-->
          <IfLastModified age="168H"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>

    <RollingFile name="RollingFile" fileName="${LOG_HOME}/${FILE_NAME}.log"
      filePattern="${LOG_HOME}/$${date:yyyy-MM}/info-%d{yyyy-MM-dd}-%i.log">
      <PatternLayout pattern="[%d][%t][%p][%c:%L] [%X{IM_MDC_TRACE_ID}]%m%n"/>
      <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
        <SizeBasedTriggeringPolicy size="512 MB"/>
      </Policies>
      <DefaultRolloverStrategy max="40">
        <Delete basePath="${LOG_HOME}/$${date:yyyy-MM}/" maxDepth="2">
          <IfFileName glob="*.log"/>
          <!--!Note: 这里的age必须和filePattern协调, 后者是精确到HH, 这里就要写成xH, xd就不起作用
          另外, 数字最好>2, 否则可能造成删除的时候, 最近的文件还处于被占用状态,导致删除不成功!-->
          <!--7天-->
          <IfLastModified age="168H"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>
  </Appenders>

  <loggers>
    <!--全局日志级别-->
    <root level="info">
      <appender-ref ref="Console"/>
      <appender-ref ref="RollingFile"/>
    </root>
    <!--对应包输出日志级别-->
    <!--注意：jsf无法根据配置调整日志级别-->
    <logger name="org.springframework.core" level="info"/>
    <logger name="org.springframework.beans" level="info"/>
    <logger name="org.springframework.context" level="info"/>
    <logger name="org.springframework.web" level="info"/>
    <logger name="org.jboss.netty" level="warn"/>
    <logger name="org.apache.http" level="warn"/>
    <logger name="org.hibernate" level="info"/>
<!--    <logger name="com.alibaba.druid" level="info"/>-->
    <!-- <logger name="com.jd.im.scheduling.dao" level="debug"/> -->
    <logger name="com.jd.im.keeper" level="FATAL"/>
    <logger name="com.jd.ump" level="FATAL"/>
  </loggers>

</Configuration>