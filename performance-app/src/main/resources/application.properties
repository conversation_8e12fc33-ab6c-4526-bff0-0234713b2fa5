spring.application.name=performance
spring.profiles.active=dev
env=dev
# mybatis


#mybatis-plus.configuration.map-underscore-to-camel-case=true
#mybatis-plus.mapper-locations=classpath*:mapper/**/*Mapper.xml
mybatis.mapper-locations=classpath*:**/*.xml
mybatis.type-aliases-package=com.jd.kf.oss.performance.infra.mybatis.entity
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.default-enum-type-handler=com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
mybatis-plus.type-enums-package=com.jd.kf.oss.performance.enums

logging.config=classpath:log4j2.xml
logging.level.com.jd.im.scheduling.dao=debug
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER


dong.dal.datasource.jdbc-url=${DONG_DAL_MASTER_JDBC_URL:**************************************************************************************************************************************************************************************}
dong.dal.datasource.username=${DONG_DAL_MASTER_JDBC_USERNAME:imtest_rw}
dong.dal.datasource.password=${DONG_DAL_MASTER_JDBC_PASSWORD:5iht9BBHknVCMLQw}
dong.dal.datasource.pool-name=dal-vtd11_2
dong.dal.datasource.driver-class-name=com.mysql.jdbc.Driver
dong.dal.datasource.maximum-pool-size=120
dong.dal.datasource.minimum-idle=10
dong.dal.datasource.connection-timeout=1000
dong.dal.datasource.validation-timeout=500
dong.dal.datasource.max-lifetime=1800000
dong.dal.datasource.keepalive-time=300000
dong.dal.datasource.idle-timeout=600000
dong.dal.datasource.auto-commit=true
server.port=8888