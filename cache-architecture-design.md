# 缓存架构设计说明

## 架构概述

按照依赖倒置原则（DIP），我们将缓存组件设计为接口与实现分离的架构：

```
performance-domain (领域层)
├── IDeptPathCache (接口定义)
└── DeptDomainService (使用接口)

performance-infra (基础设施层)
└── cache/
    └── DeptPathCache (接口实现)
```

## 设计原则

### 1. 依赖倒置原则（Dependency Inversion Principle）
- **高层模块不依赖低层模块**：DeptDomainService（高层）不直接依赖具体的缓存实现
- **抽象不依赖细节**：IDeptPathCache 接口不依赖具体的缓存技术（Guava Cache）
- **细节依赖抽象**：DeptPathCache 实现依赖 IDeptPathCache 接口

### 2. 单一职责原则（Single Responsibility Principle）
- **接口职责**：IDeptPathCache 只定义缓存操作的契约
- **实现职责**：DeptPathCache 只负责具体的缓存逻辑
- **服务职责**：DeptDomainService 只负责业务逻辑协调

### 3. 开闭原则（Open-Closed Principle）
- **对扩展开放**：可以轻松替换缓存实现（Redis、Caffeine等）
- **对修改封闭**：更换缓存实现不需要修改业务代码

## 文件结构

### 接口定义（Domain层）
```java
// performance-domain/src/main/java/com/jd/kf/oss/performance/domain/config/domain/dept/IDeptPathCache.java
public interface IDeptPathCache {
    /**
     * 从缓存中获取部门路径名称
     */
    String getDeptPathName(String tenantCode, String deptId);
    
    /**
     * 将部门路径名称放入缓存
     */
    void putDeptPathName(String tenantCode, String deptId, String deptPathName);
    
    /**
     * 批量将部门路径名称放入缓存
     */
    void putDeptPathNames(String tenantCode, Map<String, String> deptPathMap);
    
    /**
     * 批量检查哪些部门ID在缓存中不存在
     */
    Set<String> getMissingDeptIds(String tenantCode, Set<String> deptIds);
    
    // 其他缓存管理方法...
}
```

### 实现类（Infra层）
```java
// performance-infra/src/main/java/com/jd/kf/oss/performance/infra/cache/DeptPathCache.java
@Component
public class DeptPathCache implements IDeptPathCache {
    private final Cache<String, String> deptPathCache = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .recordStats()
            .build();
    
    @Override
    public String getDeptPathName(String tenantCode, String deptId) {
        // 具体实现...
    }
    
    // 其他方法实现...
}
```

### 服务使用（Domain层）
```java
// performance-domain/src/main/java/com/jd/kf/oss/performance/domain/config/domain/dept/DeptDomainService.java
@Service
public class DeptDomainService {
    
    @Resource
    private IDeptRepository deptRepository;
    
    @Resource
    private IDeptPathCache deptPathCache; // 依赖接口，不依赖实现
    
    public Map<String, String> getDeptPathNamesByDeptIds(String tenantCode, Set<String> deptIds) {
        // 使用缓存接口进行业务逻辑处理...
    }
}
```

## 优势分析

### 1. 可测试性
- 可以轻松创建 Mock 实现进行单元测试
- 不需要启动真实的缓存服务

### 2. 可维护性
- 缓存逻辑与业务逻辑分离
- 修改缓存策略不影响业务代码

### 3. 可扩展性
- 支持多种缓存实现：本地缓存、分布式缓存
- 可以实现缓存降级策略

### 4. 符合DDD设计
- 领域层定义业务需要的缓存能力
- 基础设施层提供技术实现

## 实现细节

### 缓存Key设计
```java
private String buildCacheKey(String tenantCode, String deptId) {
    return tenantCode + ":" + deptId;
}
```

### 缓存配置
- **容量**：最大10000个条目
- **过期策略**：写入后30分钟过期
- **统计功能**：启用命中率统计

### 批量操作优化
```java
public Set<String> getMissingDeptIds(String tenantCode, Set<String> deptIds) {
    Set<String> missingIds = new HashSet<>();
    for (String deptId : deptIds) {
        if (!containsDeptPath(tenantCode, deptId)) {
            missingIds.add(deptId);
        }
    }
    return missingIds;
}
```

## 使用示例

### 业务代码使用
```java
// 1. 检查缓存未命中的部门ID
Set<String> missingDeptIds = deptPathCache.getMissingDeptIds(tenantCode, deptIds);

// 2. 从数据库查询未命中的数据
if (!missingDeptIds.isEmpty()) {
    Map<String, String> dbResult = deptRepository.getDeptPathNamesByDeptIds(tenantCode, missingDeptIds);
    
    // 3. 将查询结果放入缓存
    deptPathCache.putDeptPathNames(tenantCode, dbResult);
}

// 4. 从缓存获取所有结果
Map<String, String> result = new HashMap<>();
for (String deptId : deptIds) {
    String pathName = deptPathCache.getDeptPathName(tenantCode, deptId);
    if (pathName != null) {
        result.put(deptId, pathName);
    }
}
```

### 缓存管理
```java
// 获取缓存统计信息
String stats = deptPathCache.getCacheStats();
log.info("部门路径缓存统计: {}", stats);

// 清空指定租户的缓存
deptPathCache.clearTenantCache(tenantCode);

// 清空所有缓存
deptPathCache.clearAll();
```

## 扩展方案

### 1. 分布式缓存实现
```java
@Component
@Profile("redis")
public class RedisDeptPathCache implements IDeptPathCache {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    // Redis 实现...
}
```

### 2. 多级缓存实现
```java
@Component
@Profile("multi-level")
public class MultiLevelDeptPathCache implements IDeptPathCache {
    private final IDeptPathCache localCache;
    private final IDeptPathCache distributedCache;
    
    // 多级缓存实现...
}
```

### 3. 缓存降级策略
```java
@Component
public class FallbackDeptPathCache implements IDeptPathCache {
    private final IDeptPathCache primaryCache;
    private final IDeptRepository deptRepository;
    
    // 降级策略实现...
}
```

## 总结

通过依赖倒置的设计，我们实现了：
1. **高内聚低耦合**的架构设计
2. **易于测试和维护**的代码结构
3. **灵活可扩展**的缓存方案
4. **符合DDD原则**的分层设计

这种设计为系统的长期演进和维护提供了良好的基础。
