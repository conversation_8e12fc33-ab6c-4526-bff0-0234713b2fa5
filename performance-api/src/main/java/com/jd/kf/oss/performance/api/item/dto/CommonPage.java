package com.jd.kf.oss.performance.api.item.dto;

import java.util.ArrayList;
import java.util.List;
public class CommonPage<T> {

    private Long page;

    private Long total;

    private Long size;

    private List<T> data;

    public static <T> CommonPage<T> getCommonPage(Long page, Long pageSize, Long total, List<T> data) {
        CommonPage<T> CommonPage = new CommonPage<T>();
        CommonPage.setPage(page);
        CommonPage.setSize(pageSize);
        CommonPage.setTotal(total);
        CommonPage.setData(data);
        return CommonPage;
    }

    public static <T> CommonPage<T> emptyPage() {
        return emptyPage(0L, 0L);
    }

    public static <T> CommonPage<T> emptyPage(Long page, Long size) {
        return getCommonPage(page, size, 0L, new ArrayList<T>());
    }

    public Long getPage() {
        return page;
    }

    public void setPage(Long page) {
        this.page = page;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }
}
