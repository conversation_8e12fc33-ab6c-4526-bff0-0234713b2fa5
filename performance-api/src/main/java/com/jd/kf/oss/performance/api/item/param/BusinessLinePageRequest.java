package com.jd.kf.oss.performance.api.item.param;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
public class BusinessLinePageRequest implements Serializable {

    /**
     * 绩效组名称
     */
    private String name;

    /**
     *  绩效组id
     */
    private List<String> businessLineIds;

    /**
     * 编辑人
     */
    private String editor;

    /**
     * 租户标识
     */
    private String tenantCode;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 分页大小
     */
    private Integer pageSize = 20;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getBusinessLineIds() {
        return businessLineIds;
    }

    public void setBusinessLineIds(List<String> businessLineIds) {
        this.businessLineIds = businessLineIds;
    }
}
